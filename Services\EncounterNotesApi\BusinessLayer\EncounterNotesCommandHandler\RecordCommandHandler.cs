﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class RecordCommandHandler : IRecordCommandHandler<Record>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<RecordCommandHandler> _logger;

        public RecordCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<RecordCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddRecord(List<Record> records)
        {
            await _unitOfWork.RecordRepository.AddRecordsAsync(records);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateRecord(Record record)
        {
            await _unitOfWork.RecordRepository.UpdateAsync(record);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteRecordById(Guid id)
        {
            await _unitOfWork.RecordRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteRecordByEntity(Record record)
        {
            await _unitOfWork.RecordRepository.DeleteByEntityAsync(record);
            await _unitOfWork.SaveAsync();
        }
    }
}
