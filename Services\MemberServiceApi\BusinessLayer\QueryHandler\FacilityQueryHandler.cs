﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class FacilityQueryHandler : IFacilityQueryHandler<Facility>
    {
        private readonly IUnitOfWork _unitOfWork;

        public FacilityQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Facility> GetFacilityByIdAsync(Guid id)
        {
            var facility = await _unitOfWork.FacilityRepository.GetByIdAsync(id);
            return facility;
        }

        public async Task<List<Facility>> GetFacilitiesByNameAsync(string name)
        {
            var facilities = await _unitOfWork.FacilityRepository.GetAllAsync();
            return facilities.Where(facility => facility.FacilityName.Contains(name, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public async Task<List<Facility>> GetAllFacilitiesAsync()
        {
            var facilities = await _unitOfWork.FacilityRepository.GetAllAsync();
            return facilities.ToList();
        }
        public async Task<List<Facility>> GetFacilitiesByOrgAsync(Guid id)
        {
            var facilities = await _unitOfWork.FacilityRepository.GetAllAsync();
            var filteredFacilities = facilities.Where(f => f.OrganizationId == id).ToList();
            return filteredFacilities.ToList();
        }
    }
}
