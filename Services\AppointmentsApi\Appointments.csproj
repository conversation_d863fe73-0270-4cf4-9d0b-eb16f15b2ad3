<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Appointmentsapi.Test\**" />
    <Compile Remove="BusinessLayer\**" />
    <Compile Remove="Contracts\**" />
    <Compile Remove="DataAccessLayer\**" />
    <Content Remove="Appointmentsapi.Test\**" />
    <Content Remove="BusinessLayer\**" />
    <Content Remove="Contracts\**" />
    <Content Remove="DataAccessLayer\**" />
    <EmbeddedResource Remove="Appointmentsapi.Test\**" />
    <EmbeddedResource Remove="BusinessLayer\**" />
    <EmbeddedResource Remove="Contracts\**" />
    <EmbeddedResource Remove="DataAccessLayer\**" />
    <None Remove="Appointmentsapi.Test\**" />
    <None Remove="BusinessLayer\**" />
    <None Remove="Contracts\**" />
    <None Remove="DataAccessLayer\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.Azure.SqlDatabase.ElasticScale.Client" Version="2.4.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.1" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.6.2" />
    <PackageReference Include="Moq" Version="4.20.72" />

    <PackageReference Include="Nunit" Version="4.3.2" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />

    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.IO.Hashing" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="BusinessLayer\AppointmentsBusinessLayer.csproj" />
    <ProjectReference Include="Contracts\AppointmentContracts.csproj" />
    <ProjectReference Include="DataAccessLayer\AppointmentDataAccessLayer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="AppointmentResources\AppointmentStrings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>AppointmentStrings.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="AppointmentResources\AppointmentStrings.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AppointmentStrings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
