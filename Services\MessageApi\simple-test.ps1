# Simple test for Azure Blob Storage attachment functionality
Write-Host "Testing MessageAPI with Azure Blob Storage Attachments" -ForegroundColor Green

# API endpoint
$apiUrl = "http://localhost:5000/api/Message/send"

# Create test file content
$testContent = "Hello World! This is a test attachment for Azure Blob Storage."
$base64Content = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($testContent))
$fileSizeBytes = [System.Text.Encoding]::UTF8.GetBytes($testContent).Length

Write-Host "Test file created: test-attachment.txt ($fileSizeBytes bytes)" -ForegroundColor Yellow

# Create the JSON request with attachment
$requestBody = @{
    senderName = "Azure Test User"
    senderEmailId = "<EMAIL>"
    receiverName = "Blob Storage Tester"
    receiverEmailId = "<EMAIL>"
    subject = "Azure Blob Storage Test"
    messageContent = "This message contains an attachment stored in Azure Blob Storage."
    attachments = @(
        @{
            fileName = "test-attachment.txt"
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSizeBytes
        }
    )
} | ConvertTo-Json -Depth 3

Write-Host "Sending message with attachment..." -ForegroundColor Yellow

try {
    # Send the request
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $requestBody -ContentType "application/json" -ErrorAction Stop
    
    Write-Host "SUCCESS! Message sent successfully" -ForegroundColor Green
    Write-Host "Primary Message ID: $($response.PrimaryMessageId)" -ForegroundColor Cyan
    Write-Host "Status: $($response.Status)" -ForegroundColor Cyan
    Write-Host "Attachment Count: $($response.AttachmentCount)" -ForegroundColor Cyan
    
    # Test getting the message attachments
    $messageId = $response.PrimaryMessageId
    if ($messageId) {
        Write-Host "Testing attachment retrieval..." -ForegroundColor Yellow
        
        try {
            $attachmentsUrl = "http://localhost:5000/api/Message/$messageId/attachments"
            $attachments = Invoke-RestMethod -Uri $attachmentsUrl -Method GET -ErrorAction Stop
            
            Write-Host "Attachments retrieved successfully:" -ForegroundColor Green
            foreach ($attachment in $attachments) {
                Write-Host "Attachment ID: $($attachment.AttachmentId)" -ForegroundColor Cyan
                Write-Host "Original Name: $($attachment.OriginalFileName)" -ForegroundColor Cyan
                Write-Host "Blob Name: $($attachment.BlobFileName)" -ForegroundColor Cyan
                Write-Host "Blob URL: $($attachment.BlobStorageUrl)" -ForegroundColor Cyan
                Write-Host "Container: $($attachment.BlobContainerName)" -ForegroundColor Cyan
                Write-Host "Size: $($attachment.FileSizeBytes) bytes" -ForegroundColor Cyan
                
                # Test downloading the attachment
                Write-Host "Testing attachment download..." -ForegroundColor Yellow
                try {
                    $downloadUrl = "http://localhost:5000/api/Message/attachment/$($attachment.AttachmentId)/download"
                    $downloadResponse = Invoke-WebRequest -Uri $downloadUrl -Method GET -ErrorAction Stop
                    
                    Write-Host "Attachment downloaded successfully!" -ForegroundColor Green
                    Write-Host "Downloaded content length: $($downloadResponse.Content.Length) characters" -ForegroundColor Cyan
                    
                    # Verify content matches
                    if ($downloadResponse.Content -eq $testContent) {
                        Write-Host "Content verification: PASSED" -ForegroundColor Green
                    } else {
                        Write-Host "Content verification: FAILED" -ForegroundColor Red
                    }
                    
                } catch {
                    Write-Host "Attachment download failed: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
            
        } catch {
            Write-Host "Failed to retrieve attachments: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "FAILED! Error sending message:" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorContent = $reader.ReadToEnd()
            Write-Host "Response: $errorContent" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response" -ForegroundColor Red
        }
    }
}

Write-Host "Test completed!" -ForegroundColor Green
