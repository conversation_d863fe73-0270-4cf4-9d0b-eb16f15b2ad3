# Simple Email Test with Azure Blob Storage
Write-Host "Testing Azure Blob Storage Email..." -ForegroundColor Green

$baseUrl = "http://localhost:5000"
$attachmentFile = "test-medical-report.txt"

# Read attachment
$fileContent = Get-Content $attachmentFile -Raw -Encoding UTF8
$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
$base64Content = [Convert]::ToBase64String($fileBytes)
$fileSize = $fileBytes.Length

Write-Host "File size: $fileSize bytes"

# Create request
$emailRequest = @{
    senderName = "Dr. <PERSON>"
    senderEmailId = "<EMAIL>"
    receiverName = "Dr. <PERSON>"
    receiverEmailId = "<EMAIL>"
    subject = "Medical Report - Test"
    messageContent = "Please find attached the medical report."
    attachments = @(
        @{
            fileName = $attachmentFile
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSize
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Sending email..."

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/message/send" -Method POST -Body $emailRequest -ContentType "application/json"
    
    Write-Host "SUCCESS! Message ID: $($response.messageId)" -ForegroundColor Green
    Write-Host "Attachments processed: $($response.attachmentsProcessed)" -ForegroundColor Green
    
    $messageId = $response.messageId
    
    # Get attachments
    Write-Host "Getting attachments..."
    $attachments = Invoke-RestMethod -Uri "$baseUrl/api/message/$messageId/attachments" -Method GET
    
    Write-Host "Found $($attachments.Count) attachment(s)" -ForegroundColor Green
    
    if ($attachments.Count -gt 0) {
        $attachment = $attachments[0]
        Write-Host "Attachment ID: $($attachment.attachmentId)"
        Write-Host "Blob Name: $($attachment.blobFileName)"
        Write-Host "Blob URL: $($attachment.blobStorageUrl)"
        Write-Host "Container: $($attachment.blobContainerName)"
        
        # Download attachment
        Write-Host "Downloading attachment..."
        $attachmentId = $attachment.attachmentId
        $downloadResponse = Invoke-RestMethod -Uri "$baseUrl/api/message/attachment/$attachmentId/download" -Method GET
        
        $downloadedFile = "downloaded-test.txt"
        $downloadResponse | Out-File -FilePath $downloadedFile -Encoding UTF8
        
        Write-Host "Downloaded to: $downloadedFile" -ForegroundColor Green
        
        # Verify content
        $originalContent = Get-Content $attachmentFile -Raw
        $downloadedContent = Get-Content $downloadedFile -Raw
        
        if ($originalContent -eq $downloadedContent) {
            Write-Host "CONTENT VERIFIED: Files match!" -ForegroundColor Green
        } else {
            Write-Host "WARNING: Files do not match!" -ForegroundColor Yellow
        }
    }
    
    Write-Host "ALL TESTS PASSED!" -ForegroundColor Green
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
