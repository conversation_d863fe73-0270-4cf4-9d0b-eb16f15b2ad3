﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;
using MemberServiceDataAccessLayer.Implementation;

namespace DataAccessLayer.Implementation
{
    public class UpToDateRepository : GenericRepository<UpToDate>, IUpToDateRepository
    {
        private readonly AccountDatabaseContext _context;

        public UpToDateRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }
    }
}