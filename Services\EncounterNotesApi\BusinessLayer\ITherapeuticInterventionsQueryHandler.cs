﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITherapeuticInterventionsQueryHandler<TText>
    {
        Task<IEnumerable<TherapeuticInterventionsData>> GetTherapeuticInterventionsByIdAndIsActive(Guid id);
        Task<IEnumerable<TherapeuticInterventionsData>> GetAllTherapeuticInterventionsById(Guid id);
    }
}
