﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class AssessmentsRepository : GenericRepository<AssessmentsData>, IAssessmentsRepository
    {
        private readonly RecordDatabaseContext _context;

        public AssessmentsRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer)
            : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<AssessmentsData>> GetAllAssessmentsAsync()
        {
            return await _context._Assessments
                .AsNoTracking()
                .ToListAsync();
        }
        public async Task<List<string>> GetAssessmentRelatedMedications(Guid PatientId)
        {
            var query = from a in _context._Assessments
                        join cc in _context.ChiefComplaint
                            on new { CheifComplaintId = a.CheifComplaintId, PatientId = a.PatientId }
                            equals new { CheifComplaintId = cc.Id, PatientId = cc.PatientId }
                        join m in _context.CurrentMedications
                            on new { CheifComplaintId = cc.Id, PatientId = cc.PatientId }
                            equals new { CheifComplaintId = m.CheifComplaintId, PatientId = m.PatientId }
                        where a.PatientId == PatientId && a.IsActive == true && m.isActive == true
                        select new
                        {
                            a.AssessmentsID,
                            a.PatientId,
                            a.Diagnosis,
                            ChiefComplaint = cc.Description,
                            m.MedicineId,
                            m.BrandName,
                            m.DrugDetails
                        };
            List<string> result = await query.Select(x => $"{x.Diagnosis} == {x.DrugDetails}").ToListAsync();
            return result;
        }
        public async Task<List<AssessmentsData>> GetListOfAssesmentsThroughCheifComplaintId(Guid CheifComplaintId)
        {
            return await _context._Assessments
                .Where(a => a.CheifComplaintId == CheifComplaintId)
                .ToListAsync();
        }
    }
}