﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IPhysicalExaminationCommandHandler<TText>
    {
        Task DeleteExaminationByEntity(PhysicalExamination physical);
        Task DeleteExaminationById(Guid id);
        Task AddExamination(List<TText> texts);
        Task UpdateExamination(PhysicalExamination Phyexamination);
        Task UpdateExaminationList(List<PhysicalExamination> physicalExamination);
    }
}