﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ImmunizationRepository : GenericRepository<Immunization>, IImmunizationRepository
    {
        private readonly RecordDatabaseContext _context;

        public ImmunizationRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<Immunization>> GetAllImmunizationsAsync()
        {
            return await _context.Immunizations
                .AsNoTracking()
                .ToListAsync();
        }
    }
}