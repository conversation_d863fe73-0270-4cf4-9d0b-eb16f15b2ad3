﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class MemberQueryHandler : IMemberQueryHandler<Member>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public MemberQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Member>> GetMember()
        {
            return await _unitOfWork.MemberRepository.GetAllAsync();
        }

        public async Task<Member> GetMemberById(Guid id)
        {
            return await _unitOfWork.MemberRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid id)
        {
            return await _unitOfWork.MemberRepository.GetProviderPatientByOrganizationId(id);
        }

        public async Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid id)
        {
            return await _unitOfWork.MemberRepository.GetPatientsByOrganizationId(id);
        }
        public async Task<Patient> GetPatientdatabyid(Guid id)
        {
            var member = await _unitOfWork.MemberRepository.GetByIdAsync(id);
            var addressId = member.AddressId.Value;
            var insuranceId = member.InsuranceId.Value;
            var addressDetails = await _unitOfWork.AddressesRepository.GetByIdAsync(addressId);
            var insuranceDetails = await _unitOfWork.InsuranceRepository.GetByIdAsync(insuranceId);

            var patient = new Patient
            {
                Id = member.Id,
                OrganizationID = member.OrganizationID,
                PCPName = member.PCPName,
                Name = member.UserName,
                Email = member.Email,
                PhoneNumber = member.PhoneNumber,
                Sex = member.SexualOrientation,
                DOB = member.DateOfBirth,
                Street = addressDetails.AddressLine1,
                City = addressDetails.City,
                State = addressDetails.State,
                PostalCode = addressDetails.PostalCode,
                PrimaryInsuranceProvider = insuranceDetails.PrimaryInsuranceProvider,
                PolicyNumber = insuranceDetails.PolicyNumber,
                PlanName=insuranceDetails.PlanName,
                GroupNumber =insuranceDetails.GroupNumber,
                PatientImageURL = member.ProfileImageUrl
            };

            return patient;
        }

        public async Task<IEnumerable<Member>> SearchMembersAsync(string searchTerm)
        {
            return await _unitOfWork.MemberRepository.SearchMembersAsync(searchTerm);
        }

        public async Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds)
        {
            return await _unitOfWork.MemberRepository.GetPatientsByIdsAsync(patientIds);
        }

    }
}
