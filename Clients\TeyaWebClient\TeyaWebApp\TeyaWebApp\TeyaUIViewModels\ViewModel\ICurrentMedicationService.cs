﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICurrentMedicationService
    {
        Task<List<ActiveMedication>> GetMedicationsByIdAsync(Guid id);
        Task<List<ActiveMedication>> GetMedicationsByIdAsyncAndIsActive(Guid id);
        Task AddMedicationAsync(List<ActiveMedication> medicines);
        Task DeletemedicationAsync(ActiveMedication medicine);
        Task UpdateMedicationAsync(ActiveMedication medicines);
        Task UpdateMedicationListAsync(List<ActiveMedication> medicines);
    }
}
