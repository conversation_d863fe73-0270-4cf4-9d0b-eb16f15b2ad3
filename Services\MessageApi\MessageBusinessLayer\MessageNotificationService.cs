using MessageContracts;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using MessageApi.Hubs;

namespace MessageBusinessLayer.Services
{
    public class MessageNotificationService : IMessageNotificationService
    {
        private readonly IHubContext<MessageHub> _hubContext;
        private readonly ILogger<MessageNotificationService> _logger;

        public MessageNotificationService(IHubContext<MessageHub> hubContext, ILogger<MessageNotificationService> logger)
        {
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task NotifyNewMessageAsync(string recipientEmail, Message message)
        {
            try
            {
                var userGroup = $"user_{recipientEmail}";

                var notification = new
                {
                    Type = "NewMessage",
                    MessageId = message.MessageId,
                    SenderName = message.SenderName,
                    SenderEmail = message.SenderEmailId,
                    Subject = message.Subject,
                    MessageContent = message.MessageContent,
                    SentDateTime = message.SentDateTime,
                    HasAttachments = message.HasAttachments,
                    AttachmentCount = message.AttachmentCount
                };

                await _hubContext.Clients.Group(userGroup).SendAsync("NewMessage", notification);
                _logger.LogInformation($"Real-time notification sent successfully to {recipientEmail}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send new message notification to {recipientEmail}");
            }
        }

        public async Task NotifyMessageReadAsync(string senderEmail, Guid messageId)
        {
            try
            {
                var userGroup = $"user_{senderEmail}";

                var notification = new
                {
                    Type = "MessageRead",
                    MessageId = messageId,
                    ReadDateTime = DateTime.UtcNow
                };

                await _hubContext.Clients.Group(userGroup).SendAsync("MessageRead", notification);
                _logger.LogInformation($"Read notification sent successfully to {senderEmail}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send read notification to {senderEmail}");
            }
        }

        public async Task NotifyMessageArchivedAsync(string userEmail, Guid messageId)
        {
            try
            {
                var userGroup = $"user_{userEmail}";

                var notification = new
                {
                    Type = "MessageArchived",
                    MessageId = messageId,
                    ArchivedDateTime = DateTime.UtcNow
                };

                await _hubContext.Clients.Group(userGroup).SendAsync("MessageArchived", notification);
                _logger.LogInformation($"Archive notification sent successfully to {userEmail}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send archive notification to {userEmail}");
            }
        }

        public async Task NotifyMessageDeletedAsync(string userEmail, Guid messageId)
        {
            try
            {
                var userGroup = $"user_{userEmail}";

                var notification = new
                {
                    Type = "MessageDeleted",
                    MessageId = messageId,
                    DeletedDateTime = DateTime.UtcNow
                };

                await _hubContext.Clients.Group(userGroup).SendAsync("MessageDeleted", notification);
                _logger.LogInformation($"Delete notification sent successfully to {userEmail}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send delete notification to {userEmail}");
            }
        }

        public async Task NotifyMessageImportantAsync(string userEmail, Guid messageId, bool isImportant)
        {
            try
            {
                var userGroup = $"user_{userEmail}";

                var notification = new
                {
                    Type = "MessageImportantChanged",
                    MessageId = messageId,
                    IsImportant = isImportant,
                    UpdatedDateTime = DateTime.UtcNow
                };

                await _hubContext.Clients.Group(userGroup).SendAsync("MessageImportantChanged", notification);
                _logger.LogInformation($"Important status notification sent successfully to {userEmail}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send important status notification to {userEmail}");
            }
        }
    }
}
