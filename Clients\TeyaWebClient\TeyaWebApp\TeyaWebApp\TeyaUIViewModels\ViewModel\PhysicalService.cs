﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class PhysicalService : IPhysicalService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _PhysicalUrl;
        private readonly ITokenService _tokenService;

        public PhysicalService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _PhysicalUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<Physicalexamination>> GetExaminationsByIdAsync(Guid id)
        {
            var apiUrl = $"{_PhysicalUrl}/api/PhysicalExamination/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<Physicalexamination>>();
                }
                else
                {
                    throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="examinations"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task AddExaminationAsync(List<Physicalexamination> examinations)
        {
            var apiUrl = $"{_PhysicalUrl}/api/PhysicalExamination/AddExamination";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(examinations);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physical"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task DeleteExaminationAsync(Physicalexamination physical)
        {
            var apiUrl = _PhysicalUrl;
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(physical);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="examination"></param>
        /// <returns></returns>
        public async Task UpdateExaminationAsync(Physicalexamination examination)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_PhysicalUrl}/api/PhysicalExamination/{examination.PatientId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(examination);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<Physicalexamination>> GetExaminationByIdAsyncAndIsActive(Guid id)
        {
            var apiUrl = $"{_PhysicalUrl}/api/PhysicalExamination/{id}/IsActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<Physicalexamination>>();
                }
                else
                {
                    throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physicals"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task UpdateExaminationListAsync(List<Physicalexamination> physicals)
        {
            var apiUrl = $"{_PhysicalUrl}/api/PhysicalExamination/UpdateExaminationList";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(physicals);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }
    }

}
