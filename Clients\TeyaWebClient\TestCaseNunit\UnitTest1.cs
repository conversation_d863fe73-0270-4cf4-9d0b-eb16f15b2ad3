//// 1. Test Setup and Base Classes

//using Moq;
//using FluentAssertions;
//using Microsoft.Extensions.Logging;
//using Microsoft.Extensions.Configuration;
//using System.Text.Json;
//using Microsoft.Extensions.Localization;
//using TeyaWebApp.Tests;
//using TeyaWebApp.ViewModel;
//using TeyaUIModels.Model;
//using TeyaUIViewModels.ViewModel;

//namespace TeyaWebApp.Tests
//{
//    // Base test class with common setup
//    public abstract class TestBase
//    {
//        protected Mock<ILogger<T>> CreateLoggerMock<T>()
//        {
//            return new Mock<ILogger<T>>();
//        }

//        protected Mock<IConfiguration> CreateConfigurationMock()
//        {
//            var configMock = new Mock<IConfiguration>();
//            configMock.Setup(x => x["RegistrationUrl"]).Returns("http://test.com/register");
//            configMock.Setup(x => x["MembersUrl"]).Returns("http://test.com/members");
//            return configMock;
//        }

//        protected HttpClient CreateHttpClientMock(HttpMessageHandler handler)
//        {
//            return new HttpClient(handler) { BaseAddress = new Uri("http://test.com") };
//        }
//    }

//    // Mock HTTP handler for testing HTTP clients
//    public class MockHttpMessageHandler : HttpMessageHandler
//    {
//        private readonly Func<HttpRequestMessage, HttpResponseMessage> _handler;

//        public MockHttpMessageHandler(Func<HttpRequestMessage, HttpResponseMessage> handler)
//        {
//            _handler = handler;
//        }

//        protected override Task<HttpResponseMessage> SendAsync(
//            HttpRequestMessage request,
//            CancellationToken cancellationToken)
//        {
//            return Task.FromResult(_handler(request));
//        }
//    }
//}

//// 2. Member Service Tests
//[TestFixture]
//public class MemberServiceTests : TestBase
//{
//    private Mock<ILogger<MemberService>> _loggerMock;
//    private Mock<IConfiguration> _configMock;
//    private Mock<IStringLocalizer<MemberService>> _localizerMock;
//    private IMemberService _memberService;
//    private List<Member> _testMembers;

//    [SetUp]
//    public void Setup()
//    {
//        _loggerMock = CreateLoggerMock<MemberService>();
//        _configMock = CreateConfigurationMock();
//        _localizerMock = new Mock<IStringLocalizer<MemberService>>();

//        // Setup configuration for MembersUrl with productId parameter
//        _configMock.Setup(x => x["MembersUrl"])
//            .Returns("http://test.com/api/members/{productId}");

//        // Setup localizer
//        _localizerMock.Setup(x => x["{productId}"])
//            .Returns(new LocalizedString("{productId}", "{productId}"));

//        _testMembers = new List<Member>
//        {
//            new Member
//            {
//                Id = Guid.NewGuid(),
//                UserName = "testuser",
//                Email = "<EMAIL>",
//                IsActive = true
//            }
//        };
//    }

//    [Test]
//    public async Task RegisterMembersAsync_ValidMembers_ReturnsSuccessfully()
//    {
      
//        var handler = new MockHttpMessageHandler(request =>
//        {
//            return new HttpResponseMessage
//            {
//                StatusCode = System.Net.HttpStatusCode.OK,
//                Content = new StringContent(
//                    JsonSerializer.Serialize(_testMembers.First()))
//            };
//        });

//        var httpClient = CreateHttpClientMock(handler);
//        _memberService = new MemberService(httpClient, _configMock.Object,
//            Mock.Of<IStringLocalizer<MemberService>>(), _loggerMock.Object);

//        // Act
//        var result = await _memberService.RegisterMembersAsync(_testMembers);

//        // Assert
//        result.Should().NotBeNull();
//        result.UserName.Should().Be(_testMembers.First().UserName);
//    }

//    [Test]
//    public async Task RegisterMembersAsync_DuplicateUser_ThrowsHttpRequestException()
//    {
      
//        var handler = new MockHttpMessageHandler(request =>
//        {
//            return new HttpResponseMessage
//            {
//                StatusCode = System.Net.HttpStatusCode.Conflict
//            };
//        });

//        var httpClient = CreateHttpClientMock(handler);
//        _memberService = new MemberService(httpClient, _configMock.Object,
//            Mock.Of<IStringLocalizer<MemberService>>(), _loggerMock.Object);

//        // Act & Assert
//         Assert.ThrowsAsync<HttpRequestException>(
//            async () => await _memberService.RegisterMembersAsync(_testMembers));
//    }
//}

//// 3. Product Service Tests
//[TestFixture]
//public class ProductServiceTests : TestBase
//{
//    private Mock<ILogger<ProductService>> _loggerMock;
//    private Mock<IConfiguration> _configMock;
//    private IProductService _productService;
//    private List<Product> _testProducts;

//    [SetUp]
//    public void Setup()
//    {
//        _loggerMock = CreateLoggerMock<ProductService>();
//        _configMock = CreateConfigurationMock();

//        _testProducts = new List<Product>
//        {
//            new Product
//            {
//                Id = Guid.NewGuid(),
//                Name = "Test Product",
//                Description = "Test Description"
//            }
//        };
//    }

//    [Test]
//    public async Task GetProductsAsync_ReturnsProductsList()
//    {
      
//        var handler = new MockHttpMessageHandler(request =>
//        {
//            return new HttpResponseMessage
//            {
//                StatusCode = System.Net.HttpStatusCode.OK,
//                Content = new StringContent(JsonSerializer.Serialize(_testProducts))
//            };
//        });

//        var httpClient = CreateHttpClientMock(handler);
//        _productService = new ProductService(httpClient, _configMock.Object,
//            Mock.Of<IStringLocalizer<ProductService>>());

//        // Act
//        var result = await _productService.GetProductsAsync();

//        // Assert
//        result.Should().NotBeNull();
//        result.Should().HaveCount(1);
//        result.First().Name.Should().Be(_testProducts.First().Name);
//    }

//    [Test]
//    public async Task UpdateMembersAccessAsync_ValidUpdates_ExecutesSuccessfully()
//    {
        
//        var productId = Guid.NewGuid();
//        var updates = new List<MemberAccessUpdate>
//        {
//            new MemberAccessUpdate
//            {
//                MemberId = Guid.NewGuid(),
//                HasAccess = true
//            }
//        };

//        var handler = new MockHttpMessageHandler(request =>
//        {
//            return new HttpResponseMessage
//            {
//                StatusCode = System.Net.HttpStatusCode.OK
//            };
//        });

//        var httpClient = CreateHttpClientMock(handler);
//        _productService = new ProductService(httpClient, _configMock.Object,
//            Mock.Of<IStringLocalizer<ProductService>>());

        
//        await _productService.Invoking(s => s.UpdateMembersAccessAsync(productId, updates))
//            .Should().NotThrowAsync();
//    }
//}

//// 4. Speech Service Tests
//[TestFixture]
//public class SpeechServiceTests : TestBase
//{
//    private ISpeechService _speechService;
//    private List<Speech> _testSpeeches;

//    [SetUp]
//    public void Setup()
//    {
//        _testSpeeches = new List<Speech>
//        {
//            new Speech { Result = "Test speech result" }
//        };
//    }

//    [Test]
//    public async Task TranscribeAsync_ValidInput_ReturnsTranscribedText()
//    {
        
//        var handler = new MockHttpMessageHandler(request =>
//        {
//            return new HttpResponseMessage
//            {
//                StatusCode = System.Net.HttpStatusCode.OK,
//                Content = new StringContent(JsonSerializer.Serialize(_testSpeeches))
//            };
//        });

//        var httpClient = CreateHttpClientMock(handler);
//        _speechService = new SpeechService(httpClient);

//        // Act
//        var result = await _speechService.TranscribeAsync(_testSpeeches);

//        // Assert
//        result.Should().NotBeNull();
//        result.First().Result.Should().Be(_testSpeeches.First().Result);
//    }
//}
