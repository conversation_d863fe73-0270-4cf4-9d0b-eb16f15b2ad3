﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class CurrentMedicationQueryHandler : ICurrentMedicationQueryHandler<CurrentMedication>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public CurrentMedicationQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<CurrentMedication>> GetMedicationByIdAndIsActive(Guid id)
        {
            var medications = await _unitOfWork.CurrentMedicationRepository.GetAsync();

            return medications.Where(CurrMedication => CurrMedication.PatientId == id && CurrMedication.isActive == true);
        }

        public async Task<IEnumerable<CurrentMedication>> GetAllMedicationsbyId(Guid id)
        {
            var medications  = await _unitOfWork.CurrentMedicationRepository.GetAsync();

            return medications.Where(CurrMedication => CurrMedication.PatientId == id);
        }
    }
}
