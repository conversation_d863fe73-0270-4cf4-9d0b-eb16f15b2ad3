using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using MessageContracts;
using MessageBusinessLayer;
using MessageDataAccessLayer.Implementation;
using System.Linq;

namespace MessageApi.Tests.BusinessLayer
{
    [TestFixture]
    public class AttachmentServiceTests
    {
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<AttachmentService>> _loggerMock;
        private Mock<IAzureBlobStorageService> _blobStorageServiceMock;
        private Mock<IAttachmentRepository> _attachmentRepositoryMock;
        private AttachmentService _attachmentService;
        private List<MessageAttachment> _testAttachments;
        private List<AttachmentRequest> _testAttachmentRequests;

        [SetUp]
        public void Setup()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<AttachmentService>>();
            _blobStorageServiceMock = new Mock<IAzureBlobStorageService>();
            _attachmentRepositoryMock = new Mock<IAttachmentRepository>();

            // Setup unit of work to return attachment repository
            _unitOfWorkMock.Setup(x => x.AttachmentRepository).Returns(_attachmentRepositoryMock.Object);

            _testAttachments = new List<MessageAttachment>
            {
                new MessageAttachment
                {
                    AttachmentId = Guid.NewGuid(),
                    MessageId = Guid.NewGuid(),
                    FileName = "blob_test.pdf",
                    OriginalFileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileSizeBytes = 1024,
                    BlobFileName = "blob_test.pdf",
                    BlobStorageUrl = "https://test.blob.core.windows.net/attachments/blob_test.pdf",
                    BlobContainerName = "attachments",
                    FileHash = "testhash123",
                    UploadedDateTime = DateTime.UtcNow,
                    IsScanned = true,
                    IsSafe = true
                }
            };

            _testAttachmentRequests = new List<AttachmentRequest>
            {
                new AttachmentRequest
                {
                    FileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileContentBase64 = Convert.ToBase64String(new byte[] { 1, 2, 3, 4, 5 })
                }
            };

            _attachmentService = new AttachmentService(
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _blobStorageServiceMock.Object
            );
        }

        #region ProcessAttachmentsAsync Tests

        [Test]
        public async Task ProcessAttachmentsAsync_ValidAttachments_ReturnsProcessedAttachments()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var blobFileName = "blob_test.pdf";
            var blobUrl = "https://test.blob.core.windows.net/attachments/blob_test.pdf";
            var containerName = "attachments";

            _blobStorageServiceMock.Setup(x => x.UploadAttachmentAsync(
                It.IsAny<byte[]>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(blobFileName);

            _blobStorageServiceMock.Setup(x => x.GetAttachmentUrl(blobFileName))
                .Returns(blobUrl);

            _blobStorageServiceMock.SetupGet(x => x.ContainerName)
                .Returns(containerName);

            // Act
            var result = await _attachmentService.ProcessAttachmentsAsync(messageId, _testAttachmentRequests);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);

            var attachment = result.First();
            attachment.MessageId.Should().Be(messageId);
            attachment.OriginalFileName.Should().Be("test.pdf");
            attachment.ContentType.Should().Be("application/pdf");
            attachment.FileSizeBytes.Should().Be(5); // Length of test byte array
            attachment.BlobFileName.Should().Be(blobFileName);
            attachment.BlobStorageUrl.Should().Be(blobUrl);
            attachment.BlobContainerName.Should().Be(containerName);
            attachment.IsScanned.Should().BeFalse();
            attachment.IsSafe.Should().BeTrue();

            _blobStorageServiceMock.Verify(x => x.UploadAttachmentAsync(
                It.IsAny<byte[]>(), "test.pdf", "application/pdf"), Times.Once);
        }

        [Test]
        public async Task ProcessAttachmentsAsync_NullAttachments_ReturnsEmptyList()
        {
            // Arrange
            var messageId = Guid.NewGuid();

            // Act
            var result = await _attachmentService.ProcessAttachmentsAsync(messageId, null);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task ProcessAttachmentsAsync_EmptyAttachments_ReturnsEmptyList()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var emptyAttachments = new List<AttachmentRequest>();

            // Act
            var result = await _attachmentService.ProcessAttachmentsAsync(messageId, emptyAttachments);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task ProcessAttachmentsAsync_InvalidAttachment_SkipsInvalidAttachment()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var invalidAttachment = new AttachmentRequest
            {
                FileName = "", // Invalid - empty filename
                ContentType = "application/pdf",
                FileContentBase64 = Convert.ToBase64String(new byte[] { 1, 2, 3 })
            };

            // Act
            var result = await _attachmentService.ProcessAttachmentsAsync(messageId, new List<AttachmentRequest> { invalidAttachment });

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();

            _blobStorageServiceMock.Verify(x => x.UploadAttachmentAsync(
                It.IsAny<byte[]>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        #endregion

        #region GetAttachmentAsync Tests

        [Test]
        public async Task GetAttachmentAsync_ExistingId_ReturnsAttachment()
        {
            // Arrange
            var attachmentId = _testAttachments[0].AttachmentId;
            _attachmentRepositoryMock.Setup(x => x.GetByIdAsync(attachmentId))
                .ReturnsAsync(_testAttachments[0]);

            // Act
            var result = await _attachmentService.GetAttachmentAsync(attachmentId);

            // Assert
            result.Should().NotBeNull();
            result.AttachmentId.Should().Be(attachmentId);
            result.OriginalFileName.Should().Be("test.pdf");
        }

        [Test]
        public async Task GetAttachmentAsync_NonExistingId_ReturnsNull()
        {
            // Arrange
            var attachmentId = Guid.NewGuid();
            _attachmentRepositoryMock.Setup(x => x.GetByIdAsync(attachmentId))
                .ReturnsAsync((MessageAttachment)null);

            // Act
            var result = await _attachmentService.GetAttachmentAsync(attachmentId);

            // Assert
            result.Should().BeNull();
        }

        #endregion

        #region GetAttachmentContentAsync Tests

        [Test]
        public async Task GetAttachmentContentAsync_ExistingAttachment_ReturnsContent()
        {
            // Arrange
            var attachmentId = _testAttachments[0].AttachmentId;
            var expectedContent = new byte[] { 1, 2, 3, 4, 5 };

            _attachmentRepositoryMock.Setup(x => x.GetByIdAsync(attachmentId))
                .ReturnsAsync(_testAttachments[0]);

            _blobStorageServiceMock.Setup(x => x.DownloadAttachmentAsync(_testAttachments[0].BlobFileName))
                .ReturnsAsync(expectedContent);

            // Act
            var result = await _attachmentService.GetAttachmentContentAsync(attachmentId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedContent);

            _blobStorageServiceMock.Verify(x => x.DownloadAttachmentAsync(_testAttachments[0].BlobFileName), Times.Once);
        }

        [Test]
        public async Task GetAttachmentContentAsync_NonExistingAttachment_ThrowsFileNotFoundException()
        {
            // Arrange
            var attachmentId = Guid.NewGuid();
            _attachmentRepositoryMock.Setup(x => x.GetByIdAsync(attachmentId))
                .ReturnsAsync((MessageAttachment)null);

            // Act & Assert
            await FluentActions.Invoking(() => _attachmentService.GetAttachmentContentAsync(attachmentId))
                .Should().ThrowAsync<FileNotFoundException>()
                .WithMessage($"Attachment not found: {attachmentId}");
        }

        [Test]
        public async Task GetAttachmentContentAsync_EmptyBlobFileName_ThrowsFileNotFoundException()
        {
            // Arrange
            var attachmentId = _testAttachments[0].AttachmentId;
            var attachmentWithEmptyBlob = new MessageAttachment
            {
                AttachmentId = attachmentId,
                BlobFileName = string.Empty
            };

            _attachmentRepositoryMock.Setup(x => x.GetByIdAsync(attachmentId))
                .ReturnsAsync(attachmentWithEmptyBlob);

            // Act & Assert
            await FluentActions.Invoking(() => _attachmentService.GetAttachmentContentAsync(attachmentId))
                .Should().ThrowAsync<FileNotFoundException>()
                .WithMessage($"Attachment content not found in blob storage: ");
        }

        #endregion

        #region GetMessageAttachmentsAsync Tests

        [Test]
        public async Task GetMessageAttachmentsAsync_ExistingMessageId_ReturnsAttachments()
        {
            // Arrange
            var messageId = _testAttachments[0].MessageId;
            _attachmentRepositoryMock.Setup(x => x.GetAttachmentsByMessageIdAsync(messageId))
                .ReturnsAsync(_testAttachments);

            // Act
            var result = await _attachmentService.GetMessageAttachmentsAsync(messageId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().MessageId.Should().Be(messageId);
        }

        [Test]
        public async Task GetMessageAttachmentsAsync_NonExistingMessageId_ReturnsEmptyList()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _attachmentRepositoryMock.Setup(x => x.GetAttachmentsByMessageIdAsync(messageId))
                .ReturnsAsync(new List<MessageAttachment>());

            // Act
            var result = await _attachmentService.GetMessageAttachmentsAsync(messageId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        #endregion

        #region ValidateAttachment Tests

        [Test]
        public void ValidateAttachment_ValidAttachment_ReturnsTrue()
        {
            // Arrange
            var validAttachment = _testAttachmentRequests[0];

            // Act
            var result = _attachmentService.ValidateAttachment(validAttachment);

            // Assert
            result.Should().BeTrue();
        }

        [Test]
        public void ValidateAttachment_EmptyFileName_ReturnsFalse()
        {
            // Arrange
            var invalidAttachment = new AttachmentRequest
            {
                FileName = "",
                ContentType = "application/pdf",
                FileContentBase64 = Convert.ToBase64String(new byte[] { 1, 2, 3 })
            };

            // Act
            var result = _attachmentService.ValidateAttachment(invalidAttachment);

            // Assert
            result.Should().BeFalse();
        }

        [Test]
        public void ValidateAttachment_EmptyContentType_ReturnsFalse()
        {
            // Arrange
            var invalidAttachment = new AttachmentRequest
            {
                FileName = "test.pdf",
                ContentType = "",
                FileContentBase64 = Convert.ToBase64String(new byte[] { 1, 2, 3 })
            };

            // Act
            var result = _attachmentService.ValidateAttachment(invalidAttachment);

            // Assert
            result.Should().BeFalse();
        }

        [Test]
        public void ValidateAttachment_EmptyFileContent_ReturnsFalse()
        {
            // Arrange
            var invalidAttachment = new AttachmentRequest
            {
                FileName = "test.pdf",
                ContentType = "application/pdf",
                FileContentBase64 = ""
            };

            // Act
            var result = _attachmentService.ValidateAttachment(invalidAttachment);

            // Assert
            result.Should().BeFalse();
        }

        #endregion

        #region DeleteAttachmentAsync Tests

        [Test]
        public async Task DeleteAttachmentAsync_ExistingAttachment_DeletesSuccessfully()
        {
            // Arrange
            var attachmentId = _testAttachments[0].AttachmentId;
            _attachmentRepositoryMock.Setup(x => x.GetByIdAsync(attachmentId))
                .ReturnsAsync(_testAttachments[0]);

            _blobStorageServiceMock.Setup(x => x.DeleteAttachmentAsync(_testAttachments[0].BlobFileName))
                .ReturnsAsync(true);

            _attachmentRepositoryMock.Setup(x => x.DeleteAsync(attachmentId))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(x => x.SaveAsync())
                .Returns(Task.CompletedTask);

            // Act
            var result = await _attachmentService.DeleteAttachmentAsync(attachmentId);

            // Assert
            result.Should().BeTrue();

            _blobStorageServiceMock.Verify(x => x.DeleteAttachmentAsync(_testAttachments[0].BlobFileName), Times.Once);
            _attachmentRepositoryMock.Verify(x => x.DeleteAsync(attachmentId), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteAttachmentAsync_NonExistingAttachment_ReturnsFalse()
        {
            // Arrange
            var attachmentId = Guid.NewGuid();
            _attachmentRepositoryMock.Setup(x => x.GetByIdAsync(attachmentId))
                .ReturnsAsync((MessageAttachment)null);

            // Act
            var result = await _attachmentService.DeleteAttachmentAsync(attachmentId);

            // Assert
            result.Should().BeFalse();

            _blobStorageServiceMock.Verify(x => x.DeleteAttachmentAsync(It.IsAny<string>()), Times.Never);
            _attachmentRepositoryMock.Verify(x => x.DeleteAsync(It.IsAny<Guid>()), Times.Never);
        }

        #endregion
    }
}
