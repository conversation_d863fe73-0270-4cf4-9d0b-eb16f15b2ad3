﻿using DataAccessLayer.Context;
using DataAccessLayer.Implementation;
using MemberServiceDataAccessLayer;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly AccountDatabaseContext _context;
        public IMemberRepository MemberRepository { get; }
        public IProductRepository ProductRepository { get; }
        public ILicenseRepository ProductLicenseRepository { get; }
        public IProductUserAccessRepository ProductUserAccessRepository { get; }
        public IOrganizationRepository OrganizationRepository { get; }
        public IRolesRepository RolesRepository { get; }
        public IFacilityRepository FacilityRepository { get; }
        public IAddressesRepository AddressesRepository { get; }
        public IUpToDateRepository UpToDateRepository { get; }
        public IInsuranceRepository InsuranceRepository { get; }
        public IUserThemeRepository UserThemeRepository { get; }
        public IPageRoleMappingRepository PageRoleMappingRepository { get; }
        public IPreDefinedPageRoleMappingRepository PreDefinedPageRoleMappingRepository { get; }
        public IPredefinedVisitTypeRepository PredefinedVisitTypeRepository { get; }
        public IRoleslistRepository RoleslistRepository { get; }
        public IVisitTypeRepository VisitTypeRepository { get; }
        public IVisitStatusRepository VisitStatusRepository { get; }
        public IPagePathRepository PagePathRepository { get; }
        public ICountryRepository CountryRepository { get; }

        public IEmployerRepository EmployerRepository { get; }
        public IGuardianRepository GuardianRepository { get; }

        public IUserLicenseRepository UserLicenseRepository { get; }
        public IPlanTypeRepository PlanTypeRepository { get; }


        public UnitOfWork(AccountDatabaseContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            MemberRepository = new MemberRepository(_context);
            ProductRepository = new ProductRepository(_context);
            ProductLicenseRepository = new LicenseRepository(_context);
            ProductUserAccessRepository = new ProductUserAccessRepository(_context);
            RolesRepository = new RolesRepository(context);
            OrganizationRepository = new OrganizationRepository(context);
            FacilityRepository = new FacilityRepository(context);
            AddressesRepository = new AddressesRepository(context);
            InsuranceRepository = new InsuranceRepository(context);
            UpToDateRepository = new UpToDateRepository(context);
            UserThemeRepository = new UserThemeRepository(context);
            PageRoleMappingRepository = new PageRoleMappingRepository(context);
            PreDefinedPageRoleMappingRepository = new PreDefinedPageRoleMappingRepository(context);
            PredefinedVisitTypeRepository = new PredefinedVisitTypeRepository(context);
            RoleslistRepository = new RoleslistRepository(context);
            VisitTypeRepository = new VisitTypeRepository(context);
            VisitStatusRepository = new VisitStatusRepository(context);
            PagePathRepository = new PagePathRepository(context);
            CountryRepository = new CountryRepository(context);
            GuardianRepository = new GuardianRepository(context);
            EmployerRepository = new EmployerRepository(context);
            UserLicenseRepository = new UserLicenseRepository(context);
            PlanTypeRepository = new PlanTypeRepository(context);
        }

        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}