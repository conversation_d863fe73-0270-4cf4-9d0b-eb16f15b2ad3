﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Communication.Email" Version="1.0.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Carbon.Redis" Version="2.8.2" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Markdig" Version="0.40.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.AzureAD.UI" Version="6.0.33" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.40.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.7.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.10" />
    <PackageReference Include="Microsoft.Graph" Version="5.68.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="Syncfusion.Blazor.Grid" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Lists" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Popups" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="27.1.58" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TeyaUIModels\TeyaUIModels.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="TeyaUIViewModelResources\TeyaUIViewModelsStrings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TeyaUIViewModelsStrings.resx</DependentUpon>
    </Compile>
    <Compile Update="TeyaUIViewModelResource\TeyaUIViewModelsResource.Designer.cs">
      <DependentUpon>TeyaUIViewModelsResource.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="TeyaUIViewModelResources\TeyaUIViewModelsStrings.resx">
      <SubType>Designer</SubType>
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TeyaUIViewModelsStrings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="TeyaUIViewModelResource\TeyaUIViewModelsResource.resx">
      <LastGenOutput>TeyaUIViewModelsResource.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>

</Project>
