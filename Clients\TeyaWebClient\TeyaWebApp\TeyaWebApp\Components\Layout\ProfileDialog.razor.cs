﻿using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Layout
{
    public partial class ProfileDialog
    {
        [CascadingParameter]
        private MudDialogInstance? MudDialog { get; set; }

        [Inject]
        private GraphApiService GraphApiService { get; set; }

        [Inject]
        private StateContainer StateContainer { get; set; }
        [Inject] private ActiveUser user { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }


        protected override async Task OnInitializedAsync()
        {
            StateContainer.OnChange += StateHasChanged;
            await UpdateUserDetails();
        }

        private async Task UpdateUserDetails()
        {
            await GraphApiService.GetLoggedInUserDetailsAsync();

            var activeUser = GraphApiService.ActiveUser;
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity.IsAuthenticated)
            {
                StateContainer.Username = activeUser.displayName ?? user.FindFirst(Localizer["UsernameClaim"])?.Value ?? Localizer["User"];
                StateContainer.ExtractedName = user.FindFirst(Localizer["NameClaim"])?.Value ?? Localizer["Guest"];
            }
            else
            {
                StateContainer.Username = Localizer["Guest"];
                StateContainer.ExtractedName = Localizer["Guest"];
            }
        }

        public void Dispose()
        {
            StateContainer.OnChange -= StateHasChanged;
        }

        private string GetInitials(string name)
         {
            if (string.IsNullOrEmpty(name))
                return Localizer["GusestUser"]; 

            return name.Substring(0, 1).ToUpper();
        }

        private void Submit() => MudDialog.Close(DialogResult.Ok(true));
        private void Cancel() => MudDialog.Cancel();
        private void NavigateToManageProfile()
        {
            NavigationManager.NavigateTo(Localizer["ManageProfile"]);
        }
        private async Task signout()
        {
            await GraphApiService.GetLoggedInUserDetailsAsync();
            Guid activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
            await UserLicenseService.ResetActiveUsersAsync(activeUserOrganizationId);
            TokenService.AccessToken = null;
            await AuthenticationStateProvider.GetAuthenticationStateAsync();
            NavigationManager.NavigateTo(Localizer["authentication/logout"], true);
        }
    }
}
