using System;

namespace MessageContracts
{
    public class Message : IContract
    {
        public Guid MessageId { get; set; }
        public string SenderName { get; set; } = string.Empty;
        public string SenderEmailId { get; set; } = string.Empty;
        public string ReceiverName { get; set; } = string.Empty;
        public string ReceiverEmailId { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string MessageContent { get; set; } = string.Empty;
        public DateTime SentDateTime { get; set; } = DateTime.UtcNow;
        public DateTime? DeliveredDateTime { get; set; }
        public DateTime? ReadDateTime { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int Status { get; set; } = 1;
        public string? AzureMessageId { get; set; }
        public string? ConversationId { get; set; }
        public bool HasAttachments { get; set; } = false;
        public int AttachmentCount { get; set; } = 0;
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedDateTime { get; set; }
        public string? DeletedBy { get; set; }
        public bool IsArchived { get; set; } = false;
        public DateTime? ArchivedDateTime { get; set; }
        public string? ArchivedBy { get; set; }
        public bool IsImportant { get; set; } = false;
        public virtual ICollection<MessageAttachment> Attachments { get; set; } = new List<MessageAttachment>();
    }
}
