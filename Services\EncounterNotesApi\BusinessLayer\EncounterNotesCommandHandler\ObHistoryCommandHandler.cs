﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;



namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class ObHistoryCommandHandler : IObHistoryCommandHandler<ObHistory>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ObHistoryCommandHandler> _logger;

        public ObHistoryCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<ObHistoryCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddAsync(IEnumerable<ObHistory> obhistory)
        {
            await _unitOfWork.ObHistoryRepository.AddAsync(obhistory);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAsync(ObHistory obhistory)
        {
            await _unitOfWork.ObHistoryRepository.UpdateAsync(obhistory);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateObHistoryListAsync(IEnumerable<ObHistory> obHistory)
        {
            foreach (var obhistory in obHistory)
            {
                await _unitOfWork.ObHistoryRepository.UpdateAsync(obhistory);
            }

            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteByIdAsync(Guid ObId)
        {
            await _unitOfWork.ObHistoryRepository.DeleteByIdAsync(ObId);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteByEntity(ObHistory obhistory)
        {
            await _unitOfWork.ObHistoryRepository.DeleteByEntityAsync(obhistory);
            await _unitOfWork.SaveAsync();
        }
    }
}
