﻿
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaWebApp;

namespace TeyaUIViewModels.ViewModel
{
    public interface IGynHistoryService
    {

        Task AddAsync(GynHistoryDTO gynHistoryDto);
        Task UpdateGynHistoryAsync(Guid id, GynHistoryDTO gynHistoryDto);
        Task DeleteGynHistoryAsync(Guid id);
        Task UpdateGynHistoryListAsync(List<GynHistoryDTO> gynHistories);
        Task<List<GynHistoryDTO>> LoadGynHistoriesAsync(Guid patientId);
        Task<IEnumerable<GynHistoryDTO>> GetByPatientIdAsync(Guid patientId);
    }
}
