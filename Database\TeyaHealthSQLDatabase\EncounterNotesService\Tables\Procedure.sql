﻿CREATE TABLE [EncounterNotesService].[Procedure] (
    [Id]              UNIQUEIDENTIFIER NOT NULL,
    [PatientId]       UNIQUEIDENTIFIER NOT NULL,
    [CPTCode]         NVARCHAR (MAX)   NULL,
    [OrderedBy]       NVARCHAR (MAX)   NULL,
    [Description]     TEXT             NULL,
    [Notes]           TEXT             NULL,
    [OrderDate]       DATETIME         NOT NULL,
    [LastUpdatedDate] DATETIME         NULL,
    [CreatedByUserId] UNIQUEIDENTIFIER NOT NULL,
    [UpdatedByUserId] UNIQUEIDENTIFIER NULL,
    [OrganizationId]  UNIQUEIDENTIFIER NOT NULL,
    [PCPID]           UNIQUEIDENTIFIER NOT NULL,
    [IsDeleted]       BIT              DEFAULT ((0)) NULL,
    [AssessmentData]  NVARCHAR (MAX)   NULL,
    [AssessmentId]    UNIQUEIDENTIFIER NOT NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Procedure_Assessments_AssessmentId] FOREIGN KEY ([AssessmentId]) REFERENCES [EncounterNotesService].[Assessments] ([AssessmentsID])
);

