﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using Microsoft.AspNetCore.Authorization;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.Identity.Web.Resource;
using System.Text.Json;
using System.Text;
using System.Text.Json.Serialization;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class GynHistoryController : ControllerBase
    {
        private readonly IGynHistoryCommandHandler<GynHistory> _gynHistoryCommandHandler;
        private readonly IGynHistoryQueryHandler<GynHistory> _gynHistoryQueryHandler;
        private readonly ILogger<GynHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        private readonly JsonSerializerOptions _jsonOptions;

        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(10);
        private readonly TimeSpan _suggestionCacheExpiration = TimeSpan.FromMinutes(5);
        private JsonDocument content;

        /// <summary>
        /// Initializes a new instance of the <see cref="GynHistoryController"/> class.
        /// </summary>
        /// <param name="gynHistoryQueryHandler">The GYN history query handler.</param>
        /// <param name="gynHistoryCommandHandler">The GYN history command handler.</param>
        /// <param name="logger">The logger instance.</param>
        /// <param name="localizer">The localizer for the application strings.</param>
        public GynHistoryController(
            IGynHistoryQueryHandler<GynHistory> gynHistoryQueryHandler,
            IGynHistoryCommandHandler<GynHistory> gynHistoryCommandHandler,
            ILogger<GynHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _gynHistoryCommandHandler = gynHistoryCommandHandler;
            _gynHistoryQueryHandler = gynHistoryQueryHandler;
            _logger = logger;
            _localizer = localizer;
            _jsonOptions = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        /// <summary>
        /// Creates a new GYN history record.
        /// </summary>
        /// <param name="GynHistoryDto">The GYN history data transfer object.</param>
        /// <returns>The action result indicating success or failure.</returns>

        [HttpPost]
        public async Task<IActionResult> CreateGynHistory([FromBody] GynHistory gynHistoryDto)
        {
            try
            {
                var gynHistory = new GynHistory
                {
                    gynId = Guid.NewGuid(),
                    PatientId = gynHistoryDto.PatientId,
                    Notes = gynHistoryDto.Notes,
                    Symptoms = gynHistoryDto.Symptoms,
                    DateOfHistory = gynHistoryDto.DateOfHistory, // Added line
                    OrganizationId = gynHistoryDto.OrganizationId,
                    PcpId = gynHistoryDto.PcpId
                };

                await _gynHistoryCommandHandler.AddAsync(new List<GynHistory> { gynHistory });
                return CreatedAtAction(nameof(GetGynHistoryById), new { id = gynHistory.gynId }, gynHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorCreatingGynHistory"]);
                return StatusCode(500, _localizer["ErrorCreatingGynHistory"]);
            }
        }

        /// <summary>
        /// Retrieves an GYN history by its ID.
        /// </summary>
        /// <param name="id">The GYN history ID.</param>
        /// <returns>The action result containing the GYN history or an error message.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetGynHistoryById(Guid id)
        {
            try
            {
                var gynHistory = await _gynHistoryQueryHandler.GetByIdAsync(id);
                if (gynHistory == null)
                {
                    return NotFound(_localizer["GynHistoryNotFound"]);
                }

                return Ok(gynHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingGynHistory"]);
                return BadRequest(_localizer["ErrorFetchingGynHistory"]);
            }
        }

        /// <summary>
        /// Retrieves all GYN histories for a specific patient.
        /// </summary>
        /// <param name="patientId">The patient ID.</param>
        /// <returns>The action result containing a list of GYN histories.</returns>
        [HttpGet("patient/{patientId}")]
        public async Task<IActionResult> GetByPatientIdAsync(Guid patientId)
        {
            try
            {
                var gynHistories = await _gynHistoryQueryHandler.GetByPatientIdAsync(patientId);
                return Ok(gynHistories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingGynHistories"]);
                return BadRequest(_localizer["ErrorFetchingGynHistories"]);
            }
        }

        /// <summary>
        /// Deletes an GYN history record by its ID.
        /// </summary>
        /// <param name="id">The GYN history ID.</param>
        /// <returns>The action result indicating success or failure.</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteGynHistory(Guid id)
        {
            try
            {
                var gynHistory = await _gynHistoryQueryHandler.GetByIdAsync(id);
                if (gynHistory == null)
                {
                    return NotFound(_localizer["GynHistoryNotFound"]);
                }

                await _gynHistoryCommandHandler.DeleteByIdAsync(id);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingGynHistory"]);
                return BadRequest(_localizer["ErrorDeletingGynHistory"]);
            }
        }

        /// <summary>
        /// Bulk updates a list of GYN history records.
        /// </summary>
        /// <param name="gynHistories">The list of GYN history records to update.</param>
        /// <returns>The action result indicating success or failure.</returns>
        [HttpPut("bulk-update")]
        public async Task<IActionResult> UpdateGynHistoryList([FromBody] List<GynHistory> gynHistories)
        {
            if (gynHistories == null || !gynHistories.Any())
            {
                return BadRequest("Invalid records provided.");
            }

            try
            {
                await _gynHistoryCommandHandler.UpdateGynHistoryListAsync(gynHistories);
                return Ok("Bulk update successful");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating GynHistory list");
                return StatusCode(500, "An error occurred while updating the GynHistory list.");
            }
        }

        /// <summary>
        /// Updates an existing GYN history record by its ID.
        /// </summary>
        /// <param name="id">The GYN history ID.</param>
        /// <param name="gynHistoryDto">The GYN history data transfer object.</param>
        /// <returns>The action result containing the updated GYN history or an error message.</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateGynHistory(Guid id, [FromBody] GynHistory gynHistoryDto)
        {
            try
            {
                var existingGynHistory = await _gynHistoryQueryHandler.GetByIdAsync(id);
                if (existingGynHistory == null)
                {
                    return NotFound(_localizer["GynHistoryNotFound"]);
                }

                existingGynHistory.Notes = gynHistoryDto.Notes;
                existingGynHistory.Symptoms = gynHistoryDto.Symptoms;
                existingGynHistory.DateOfHistory = gynHistoryDto.DateOfHistory; // Added line
                existingGynHistory.OrganizationId = gynHistoryDto.OrganizationId;
                existingGynHistory.PcpId = gynHistoryDto.PcpId;
                await _gynHistoryCommandHandler.UpdateAsync(existingGynHistory);

                return Ok(existingGynHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingGynHistory"]);
                return StatusCode(500, _localizer["ErrorUpdatingGynHistory"]);
            }
        }
        /// <summary>
        /// Retrieves all GYN histories.
        /// </summary>
        /// <returns>The action result containing a list of all GYN histories.</returns>


    }
}
