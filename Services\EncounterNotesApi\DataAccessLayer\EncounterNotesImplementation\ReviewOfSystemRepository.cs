﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ReviewOfSystemRepository : GenericRepository<ReviewOfSystem>, IReviewOfSystemRepository
    {
        private readonly RecordDatabaseContext _context;

        public ReviewOfSystemRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer)
            : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<ReviewOfSystem>> GetAllReviewOfSystemsAsync()
        {
            return await _context.ReviewOfSystems
                .AsNoTracking()
                .ToListAsync();
        }
    }
}
