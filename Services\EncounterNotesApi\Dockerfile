# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

RUN mkdir -p /app/ffmpeg

# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Services/EncounterNotesApi/EncounterNotesService.csproj", "Services/EncounterNotesApi/"]
COPY ["Services/EncounterNotesApi/BusinessLayer/EncounterNotesBusinessLayer.csproj", "Services/EncounterNotesApi/BusinessLayer/"]
COPY ["Services/EncounterNotesApi/Contracts/EncounterNotesContracts.csproj", "Services/EncounterNotesApi/Contracts/"]
COPY ["Services/EncounterNotesApi/DataAccessLayer/EncounterNotesDataAccessLayer.csproj", "Services/EncounterNotesApi/DataAccessLayer/"]
RUN dotnet restore "./Services/EncounterNotesApi/EncounterNotesService.csproj"
COPY . .
WORKDIR "/src/Services/EncounterNotesApi"
RUN dotnet build "./EncounterNotesService.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./EncounterNotesService.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
COPY Services/EncounterNotesApi/ffmpeg-7.0.2-amd64-static/ffmpeg /app/ffmpeg/ffmpeg
USER root
RUN chmod +x /app/ffmpeg/ffmpeg && \
    chown app:app /app/ffmpeg/ffmpeg
USER app
ENTRYPOINT ["dotnet", "EncounterNotesService.dll"]
