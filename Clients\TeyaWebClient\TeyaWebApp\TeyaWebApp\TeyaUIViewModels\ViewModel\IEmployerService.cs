﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IEmployerService
    {
        Task<Employer?> GetEmployerByIdAsync(Guid id);

        Task<bool> AddEmployerAsync(Employer Employer);

        Task<bool> UpdateEmployerAsync(Guid id, Employer Employer);

        Task<bool> DeleteEmployerAsync(Guid id);

        Task<List<Employer>?> GetEmployerByNameAsync(string name);

        Task<List<Employer>?> GetAllEmployerAsync();
    }
}
