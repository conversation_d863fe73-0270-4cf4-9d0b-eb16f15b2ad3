﻿

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;



namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class GynHistoryCommandHandler : IGynHistoryCommandHandler<GynHistory>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<GynHistoryCommandHandler> _logger;

        public GynHistoryCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<GynHistoryCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddAsync(IEnumerable<GynHistory> gynhistory)
        {
            await _unitOfWork.GynHistoryRepository.AddAsync(gynhistory);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAsync(GynHistory gynhistory)
        {
            await _unitOfWork.GynHistoryRepository.UpdateAsync(gynhistory);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateGynHistoryListAsync(IEnumerable<GynHistory> gynHistory)
        {
            foreach (var gynhistory in gynHistory)
            {
                await _unitOfWork.GynHistoryRepository.UpdateAsync(gynhistory);
            }

            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteByIdAsync(Guid GynId)
        {
            await _unitOfWork.GynHistoryRepository.DeleteByIdAsync(GynId);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteByEntity(GynHistory gynhistory)
        {
            await _unitOfWork.GynHistoryRepository.DeleteByEntityAsync(gynhistory);
            await _unitOfWork.SaveAsync();
        }
    }
}
