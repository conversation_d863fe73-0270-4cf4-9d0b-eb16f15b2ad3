﻿using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaUIModels.ViewModel
{
    public class FacilityService : IFacilityService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<FacilityService> _localizer;
        private readonly ILogger<FacilityService> _logger;
        private readonly string _MemberService;

        public FacilityService(HttpClient httpClient, IStringLocalizer<FacilityService> localizer, ILogger<FacilityService> logger)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        public async Task<Facility> RegisterFacilityAsync(Facility facility)
        {
            try
            {
                var bodyContent = JsonSerializer.Serialize(facility);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/Facility";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);

                    try
                    {
                        return JsonSerializer.Deserialize<Facility>(responseData);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, _localizer["JsonDeserializationError", ex.Message]);
                        throw;
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Facility already exists with status code {StatusCode}", response.StatusCode);
                    throw new HttpRequestException(_localizer["FacilityAlreadyExists"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Registration failed. Status Code: {response.StatusCode}, Reason: {response.ReasonPhrase}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RegistrationFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["FacilityRegistrationError"]);
                throw;
            }
        }

        public async Task<Facility> GetFacilityByIdAsync(Guid facilityId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Facility/{facilityId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    return JsonSerializer.Deserialize<Facility>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingFacilityById"], facilityId);
                throw;
            }
        }

        public async Task<List<string>> GetFacilityNamesAsync()
        {
            try
            {
                var allFacilities = await GetAllFacilitiesAsync();

                return allFacilities?
                    .Select(facility => facility.FacilityName)
                    .Where(facilityName => !string.IsNullOrEmpty(facilityName))
                    .ToList() ?? new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingFacilityNames"]);
                throw;
            }
        }

        public async Task<List<Facility>> GetAllFacilitiesAsync()
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Facility";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Facility>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllFacilities"]);
                throw;
            }
        }
        public async Task<List<Facility>> GetAllFacilitiesByOrgIdAsync(Guid? ID)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Facility/Org/{ID}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Facility>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllFacilities"]);
                throw;
            }
        }

        public async Task DeleteFacilityByIdAsync(Guid facilityId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Facility/{facilityId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["FacilityDeletedSuccessfully"], facilityId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["FacilityDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingFacility"], facilityId);
                throw;
            }
        }

        public async Task UpdateFacilityByIdAsync(Guid facilityId, Facility facility)
        {
            if (facility == null || facility.FacilityId != facilityId)
            {
                _logger.LogError(_localizer["InvalidFacility"]);
                throw new ArgumentException(_localizer["InvalidFacility"]);
            }

            try
            {
                var apiUrl = $"{_MemberService}/api/Facility/{facilityId}";
                var bodyContent = JsonSerializer.Serialize(facility);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["UpdateSuccessful"], facilityId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingFacility"], facilityId);
                throw;
            }
        }
    }
}
