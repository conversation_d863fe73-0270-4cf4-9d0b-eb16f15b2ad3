﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ReviewOfSystemController : ControllerBase
    {
        private readonly IReviewOfSystemCommandHandler<ReviewOfSystem> _reviewDataHandler;
        private readonly IReviewOfSystemQueryHandler<ReviewOfSystem> _reviewQueryHandler;
        private readonly ILogger<ReviewOfSystemController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public ReviewOfSystemController(
            IReviewOfSystemCommandHandler<ReviewOfSystem> reviewDataHandler,
            IReviewOfSystemQueryHandler<ReviewOfSystem> reviewQueryHandler,
            ILogger<ReviewOfSystemController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _reviewDataHandler = reviewDataHandler;
            _reviewQueryHandler = reviewQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<ReviewOfSystem>>> GetAllById(Guid id)
        {
            try
            {
                var data = await _reviewQueryHandler.GetAllReviewOfSystemsById(id);
                return data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        [HttpGet("{id:guid}/IsActive")]
        public async Task<ActionResult<IEnumerable<ReviewOfSystem>>> GetAllByIdAndIsActive(Guid id)
        {
            try
            {
                var history = await _reviewQueryHandler.GetReviewOfSystemByIdAndIsActive(id);
                return history != null ? Ok(history) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        [HttpPost]
        [Route("AddReviewOfSystem")]
        public async Task<IActionResult> AddReviewOfSystem([FromBody] List<ReviewOfSystem> reviews)
        {
            if (reviews == null || reviews.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _reviewDataHandler.AddReviewOfSystem(reviews);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete]
        [Route("DeleteReviewOfSystem")]
        public async Task<IActionResult> DeleteByEntity([FromBody] ReviewOfSystem review)
        {
            if (review == null)
            {
                return BadRequest(_localizer["InvalidRecord"]);
            }

            try
            {
                await _reviewDataHandler.DeleteReviewOfSystemByEntity(review);
                return Ok(_localizer["DeleteSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500, _localizer["DeleteLogError"]);
            }
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateReviewOfSystemById(Guid id, [FromBody] ReviewOfSystem review)
        {
            if (review == null || review.PatientId != id)
            {
                return BadRequest(_localizer["InvalidRecord"]);
            }

            try
            {
                await _reviewDataHandler.UpdateReviewOfSystem(review);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500, _localizer["UpdateLogError"]);
            }
        }

        [HttpPut]
        [Route("UpdateReviewOfSystemList")]
        public async Task<IActionResult> UpdateReviewOfSystemList([FromBody] List<ReviewOfSystem> reviews)
        {
            try
            {
                await _reviewDataHandler.UpdateReviewOfSystemList(reviews);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500, _localizer["UpdateLogError"]);
            }
        }
    }
}
