﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using Azure.Core;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class AppointmentService : IAppointmentService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _AppointmentsUrl;
        private readonly ITokenService _tokenService;

        public AppointmentService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _AppointmentsUrl = Environment.GetEnvironmentVariable("AppointmentsURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Retrieves a list of appointments for a specific date.
        /// </summary>
        /// <param name="date">The date for which appointments are to be retrieved.</param>
        /// <returns>A list of appointments.</returns>
        public async Task<List<Appointment>> GetAppointmentsAsync(DateTime date, Guid? OrgID,bool Subscription)
        {
            var dateUrl = $"{_AppointmentsUrl}/api/Appointments/{date:yyyy-MM-dd}/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, dateUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Appointment>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }


        /// <summary>
        /// Creates new appointments by sending a request to the appointment registration API.
        /// </summary>
        /// <param name="appointments">A list of appointments to be created.</param>
        public async Task CreateAppointmentsAsync(List<Appointment> appointments)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_AppointmentsUrl}/api/Appointments/appointmentRecord";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(appointments);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Updates an existing appointment.
        /// </summary>
        /// <param name="appointment">The appointment object containing updated details.</param>
        public async Task UpdateAppointmentAsync(Appointment appointment)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_AppointmentsUrl}/api/Appointments/{appointment.Id}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(appointment);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Deletes an appointment by its unique identifier.
        /// </summary>
        /// <param name="appointmentId">The unique identifier of the appointment to be deleted.</param>
        public async Task DeleteAppointmentAsync(Guid appointmentId,Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_AppointmentsUrl}/api/Appointments/{appointmentId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }
    }
}