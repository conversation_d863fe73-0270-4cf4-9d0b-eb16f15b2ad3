﻿using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Threading;
using System.Linq;
using static MudBlazor.Icons.Custom;
using Unity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;

namespace TeyaWebApp.Components.Pages
{
    public partial class Immunization
    {
        [Inject] public IVaccineService VaccineService { get; set; }
        [Inject] public IImmunizationService _ImmunizationService { get; set; }
        [Inject] private ILogger<Immunization> _logger { get; set; }
        [Inject] private IStringLocalizer<Immunization> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }

        [Inject] private ISnackbar Snackbar { get; set; }
        private MudDialog _immunization;
        private SfGrid<ImmunizationData> ImmunizationGrid;

        private List<ImmunizationData> immunization { get; set; }

        public string VaccineName { get; set; }

        public string SelectedCPTCode { get; set; }
        public string SelectedCVXCode { get; set; }
        public string SelectedCPTDescription { get; set; }
        public DateTime GivenDate { get; set; } = DateTime.Now;
        public string Comments { get; set; }

        private SfRichTextEditor RichTextEditor;

        private List<Vaccines> _details { get; set; } = new List<Vaccines>();

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
        };

        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientId { get; set; }
        private string editorContent;

        private List<ImmunizationData> deletesurgerylist { get; set; } = new List<ImmunizationData>();
        private List<ImmunizationData> AddList = new();

        protected override async Task OnInitializedAsync()
        {
            PatientId = _PatientService.PatientData.Id;
            try
            {
                _details = await VaccineService.GetAllVaccinesDataAsync();
                var existingImmunizations = await _ImmunizationService.GetImmunizationByIdAsyncAndIsActive(PatientId);

                immunization = existingImmunizations?.Where(i => !string.IsNullOrEmpty(i.Immunizations)).ToList() ?? new List<ImmunizationData>();

                int emptyRowsNeeded = 9 - immunization.Count;
                if (emptyRowsNeeded > 0)
                {
                    immunization.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                        .Select(_ => new ImmunizationData
                        {
                            Immunizations = string.Empty,
                            CPTCode = string.Empty,
                            CVXCode = string.Empty,
                            Comments = string.Empty
                        }));
                }

                UpdateEditorContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving immunization data");
            }
        }

        private void UpdateEditorContent()
        {
            var savedRecords = immunization
                .Where(s => !string.IsNullOrEmpty(s.Immunizations))
                .OrderByDescending(s => s.CreatedDate)
                .ToList();

            editorContent = string.Join("<p>", savedRecords
                .Select(s => $"<strong>{s.CreatedDate?.ToString("MM-dd-yyyy")}</strong> - {s.Immunizations} " +
                            $"(CPT: {s.CPTCode}, CVX: {s.CVXCode})" +
                            (string.IsNullOrEmpty(s.Comments) ? "" : $" - {s.Comments}")));
        }

        private async Task OpenNewDialogBox()
        {
            await _immunization.ShowAsync();
        }

        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _immunization.CloseAsync();
        }

        protected async Task<IEnumerable<string>> SearchVaccinesData(string value, CancellationToken cancellationToken)
        {
            var searchResults = _details
                .Where(t => !string.IsNullOrEmpty(t.VaccineName) && t.VaccineName.Contains(value, StringComparison.OrdinalIgnoreCase))
                .Select(t => t.VaccineName)
                .Distinct()
                .ToList();

            cancellationToken.ThrowIfCancellationRequested();

            return searchResults;
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(_localizer["BackdropDisabledMessage"], Severity.Info);
        }

        public void ActionCompletedHandler(ActionEventArgs<ImmunizationData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedImmunization = args.Data as ImmunizationData;
                var existingItem = AddList.FirstOrDefault(v => v.ImmunizationId == deletedImmunization.ImmunizationId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deletesurgerylist.Add(args.Data);
                }
            }
        }

        public void ActionBeginHandler(ActionEventArgs<ImmunizationData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.GivenDate > DateTime.Now.Date)
                {
                    Snackbar.Add(@Localizer["Date cannot be in the future"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        private async Task SaveData()
        {
            try
            {
                if (AddList.Any(item => item.GivenDate == null || item.GivenDate == DateTime.MinValue))
                {
                    Snackbar.Add(_localizer["Please select given date for all immunizations"], Severity.Warning);
                    return;
                }

                if (AddList.Count > 0)
                {
                    await _ImmunizationService.AddImmunizationAsync(AddList);
                }

                if (deletesurgerylist.Count > 0)
                {
                    await _ImmunizationService.UpdateImmunizationListAsync(deletesurgerylist);
                }

                await _ImmunizationService.UpdateImmunizationListAsync(immunization.Where(i => !string.IsNullOrEmpty(i.Immunizations)).ToList());

                deletesurgerylist.Clear();
                AddList.Clear();

                UpdateEditorContent();

                ResetInputFields();

                await InvokeAsync(StateHasChanged);
                await CloseNewDialogBox();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving immunization data");
                Snackbar.Add(_localizer["Failed to save immunization records"], Severity.Error);
            }
        }

        private async Task CancelData()
        {
            deletesurgerylist.Clear();
            AddList.Clear();

            var existingImmunizations = await _ImmunizationService.GetImmunizationByIdAsyncAndIsActive(PatientId);

            immunization = existingImmunizations?.Where(i => !string.IsNullOrEmpty(i.Immunizations)).ToList() ?? new List<ImmunizationData>();

            int emptyRowsNeeded = 9 - immunization.Count;
            if (emptyRowsNeeded > 0)
            {
                immunization.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                    .Select(_ => new ImmunizationData
                    {
                        Immunizations = string.Empty,
                        CPTCode = string.Empty,
                        CVXCode = string.Empty,
                        Comments = string.Empty
                    }));
            }

            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        private async Task OnICDNameChanged(string value)
        {
            VaccineName = value;

            var selectedVaccine = _details.FirstOrDefault(v => v.VaccineName == value);
            if (selectedVaccine != null)
            {
                SelectedCPTCode = selectedVaccine.CPTCode;
                SelectedCVXCode = selectedVaccine.CVXCode;
                SelectedCPTDescription = selectedVaccine.CPTDescription;
            }
            else
            {
                SelectedCPTCode = string.Empty;
                SelectedCVXCode = string.Empty;
                SelectedCPTDescription = string.Empty;
            }

            StateHasChanged();
        }

        private void ResetInputFields()
        {
            VaccineName = string.Empty;
            SelectedCPTCode = string.Empty;
            SelectedCVXCode = string.Empty;
            SelectedCPTDescription = string.Empty;
            Comments = string.Empty;
            GivenDate = DateTime.Now;
        }

        private async Task AddNewSurgery()
        {
            try
            {
                if (string.IsNullOrEmpty(VaccineName))
                {
                    Snackbar.Add(_localizer["Please Select Vaccine"], Severity.Warning);
                    return;
                }

                var emptyRow = immunization.FirstOrDefault(i => string.IsNullOrEmpty(i.Immunizations));

                if (emptyRow == null)
                {
                    emptyRow = new ImmunizationData();
                    immunization.Add(emptyRow);
                }

                emptyRow.ImmunizationId = Guid.NewGuid();
                emptyRow.PatientId = PatientId;
                emptyRow.PCPId = Guid.Parse(User.id);
                emptyRow.OrganizationId = PatientId;
                emptyRow.CreatedBy = Guid.Parse(User.id);
                emptyRow.UpdatedBy = PatientId;
                emptyRow.CreatedDate = GivenDate;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.GivenDate = DateTime.Now;
                emptyRow.Immunizations = VaccineName;
                emptyRow.CPTCode = SelectedCPTCode;
                emptyRow.CVXCode = SelectedCVXCode;
                emptyRow.CPTDescription = SelectedCPTDescription;
                emptyRow.Comments = Comments;
                emptyRow.IsActive = true;

                AddList.Add(emptyRow);

                if (ImmunizationGrid != null)
                {
                    await ImmunizationGrid.Refresh();
                }

                ResetInputFields();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new immunization");
                Snackbar.Add(_localizer["Failed to add immunization"], Severity.Error);
            }
        }
    }
}