using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using MessageContracts;
using MessageBusinessLayer;
using MessageDataAccessLayer;

namespace MessageApi.Tests.BusinessLayer
{
    [TestFixture]
    public class MessageCommandHandlerTests
    {
        private Mock<IMessageRepository> _repositoryMock;
        private Mock<IAttachmentService> _attachmentServiceMock;
        private Mock<ILogger<MessageCommandHandler>> _loggerMock;
        private Mock<IStringLocalizer<MessageCommandHandler>> _localizerMock;
        private MessageCommandHandler _commandHandler;
        private List<Message> _testMessages;
        private List<MessageAttachment> _testAttachments;

        [SetUp]
        public void Setup()
        {
            _repositoryMock = new Mock<IMessageRepository>();
            _attachmentServiceMock = new Mock<IAttachmentService>();
            _loggerMock = new Mock<ILogger<MessageCommandHandler>>();
            _localizerMock = new Mock<IStringLocalizer<MessageCommandHandler>>();

            _testMessages = new List<Message>
            {
                new Message 
                { 
                    MessageId = Guid.NewGuid(), 
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject",
                    MessageContent = "Test Content",
                    SentDateTime = DateTime.UtcNow,
                    IsRead = false,
                    IsDeleted = false,
                    IsArchived = false
                }
            };

            _testAttachments = new List<MessageAttachment>
            {
                new MessageAttachment
                {
                    AttachmentId = Guid.NewGuid(),
                    MessageId = _testMessages[0].MessageId,
                    FileName = "test.pdf",
                    OriginalFileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileSizeBytes = 1024
                }
            };

            _commandHandler = new MessageCommandHandler(
                _repositoryMock.Object,
                _attachmentServiceMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task SendMessage_ValidMessage_ReturnsMessageId()
        {
            // Arrange
            var message = _testMessages[0];
            var attachments = _testAttachments;
            var expectedMessageId = message.MessageId;

            _repositoryMock.Setup(x => x.AddMessageAsync(It.IsAny<Message>()))
                          .ReturnsAsync(expectedMessageId);
            _attachmentServiceMock.Setup(x => x.SaveAttachmentsAsync(expectedMessageId, attachments))
                                 .Returns(Task.CompletedTask);

            // Act
            var result = await _commandHandler.SendMessage(message, attachments);

            // Assert
            result.Should().Be(expectedMessageId);
            _repositoryMock.Verify(x => x.AddMessageAsync(It.IsAny<Message>()), Times.Once);
            _attachmentServiceMock.Verify(x => x.SaveAttachmentsAsync(expectedMessageId, attachments), Times.Once);
        }

        [Test]
        public async Task MarkMessageAsRead_ValidMessageId_UpdatesMessage()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _repositoryMock.Setup(x => x.MarkMessageAsReadAsync(messageId))
                          .Returns(Task.CompletedTask);

            // Act
            await _commandHandler.MarkMessageAsRead(messageId);

            // Assert
            _repositoryMock.Verify(x => x.MarkMessageAsReadAsync(messageId), Times.Once);
        }

        [Test]
        public async Task SoftDeleteMessage_ValidParameters_ReturnsTrue()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var deletedBy = "<EMAIL>";
            var reason = "Test deletion";

            _repositoryMock.Setup(x => x.SoftDeleteMessageAsync(messageId, deletedBy, reason))
                          .ReturnsAsync(true);

            // Act
            var result = await _commandHandler.SoftDeleteMessage(messageId, deletedBy, reason);

            // Assert
            result.Should().BeTrue();
            _repositoryMock.Verify(x => x.SoftDeleteMessageAsync(messageId, deletedBy, reason), Times.Once);
        }

        [Test]
        public async Task RestoreMessage_ValidParameters_ReturnsTrue()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var restoredBy = "<EMAIL>";
            var reason = "Test restoration";

            _repositoryMock.Setup(x => x.RestoreMessageAsync(messageId, restoredBy, reason))
                          .ReturnsAsync(true);

            // Act
            var result = await _commandHandler.RestoreMessage(messageId, restoredBy, reason);

            // Assert
            result.Should().BeTrue();
            _repositoryMock.Verify(x => x.RestoreMessageAsync(messageId, restoredBy, reason), Times.Once);
        }

        [Test]
        public async Task ArchiveMessage_ValidParameters_ReturnsTrue()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var archivedBy = "<EMAIL>";

            _repositoryMock.Setup(x => x.ArchiveMessageAsync(messageId, archivedBy))
                          .ReturnsAsync(true);

            // Act
            var result = await _commandHandler.ArchiveMessage(messageId, archivedBy);

            // Assert
            result.Should().BeTrue();
            _repositoryMock.Verify(x => x.ArchiveMessageAsync(messageId, archivedBy), Times.Once);
        }

        [Test]
        public async Task UnarchiveMessage_ValidParameters_ReturnsTrue()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var unarchivedBy = "<EMAIL>";

            _repositoryMock.Setup(x => x.UnarchiveMessageAsync(messageId, unarchivedBy))
                          .ReturnsAsync(true);

            // Act
            var result = await _commandHandler.UnarchiveMessage(messageId, unarchivedBy);

            // Assert
            result.Should().BeTrue();
            _repositoryMock.Verify(x => x.UnarchiveMessageAsync(messageId, unarchivedBy), Times.Once);
        }

        [Test]
        public async Task DeleteMessage_ValidMessageId_DeletesMessage()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _repositoryMock.Setup(x => x.DeleteMessageAsync(messageId))
                          .Returns(Task.CompletedTask);

            // Act
            await _commandHandler.DeleteMessage(messageId);

            // Assert
            _repositoryMock.Verify(x => x.DeleteMessageAsync(messageId), Times.Once);
        }

        [Test]
        public async Task SendMessage_WithNullAttachments_SendsMessageWithoutAttachments()
        {
            // Arrange
            var message = _testMessages[0];
            List<MessageAttachment> attachments = null;
            var expectedMessageId = message.MessageId;

            _repositoryMock.Setup(x => x.AddMessageAsync(It.IsAny<Message>()))
                          .ReturnsAsync(expectedMessageId);

            // Act
            var result = await _commandHandler.SendMessage(message, attachments);

            // Assert
            result.Should().Be(expectedMessageId);
            _repositoryMock.Verify(x => x.AddMessageAsync(It.IsAny<Message>()), Times.Once);
            _attachmentServiceMock.Verify(x => x.SaveAttachmentsAsync(It.IsAny<Guid>(), It.IsAny<List<MessageAttachment>>()), Times.Never);
        }

        [Test]
        public async Task SendMessage_WithEmptyAttachments_SendsMessageWithoutAttachments()
        {
            // Arrange
            var message = _testMessages[0];
            var attachments = new List<MessageAttachment>();
            var expectedMessageId = message.MessageId;

            _repositoryMock.Setup(x => x.AddMessageAsync(It.IsAny<Message>()))
                          .ReturnsAsync(expectedMessageId);

            // Act
            var result = await _commandHandler.SendMessage(message, attachments);

            // Assert
            result.Should().Be(expectedMessageId);
            _repositoryMock.Verify(x => x.AddMessageAsync(It.IsAny<Message>()), Times.Once);
            _attachmentServiceMock.Verify(x => x.SaveAttachmentsAsync(It.IsAny<Guid>(), It.IsAny<List<MessageAttachment>>()), Times.Never);
        }

        [Test]
        public async Task SoftDeleteMessage_NonExistentMessage_ReturnsFalse()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var deletedBy = "<EMAIL>";
            var reason = "Test deletion";

            _repositoryMock.Setup(x => x.SoftDeleteMessageAsync(messageId, deletedBy, reason))
                          .ReturnsAsync(false);

            // Act
            var result = await _commandHandler.SoftDeleteMessage(messageId, deletedBy, reason);

            // Assert
            result.Should().BeFalse();
            _repositoryMock.Verify(x => x.SoftDeleteMessageAsync(messageId, deletedBy, reason), Times.Once);
        }

        [Test]
        public async Task ArchiveMessage_NonExistentMessage_ReturnsFalse()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var archivedBy = "<EMAIL>";

            _repositoryMock.Setup(x => x.ArchiveMessageAsync(messageId, archivedBy))
                          .ReturnsAsync(false);

            // Act
            var result = await _commandHandler.ArchiveMessage(messageId, archivedBy);

            // Assert
            result.Should().BeFalse();
            _repositoryMock.Verify(x => x.ArchiveMessageAsync(messageId, archivedBy), Times.Once);
        }
    }
}
