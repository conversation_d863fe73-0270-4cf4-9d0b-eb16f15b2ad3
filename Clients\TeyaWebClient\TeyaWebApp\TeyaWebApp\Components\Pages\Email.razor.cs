﻿using MailKit.Net.Smtp;
using MimeKit;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Microsoft.JSInterop;
using System.Text;

namespace TeyaWebApp.Components.Pages
{
    public partial class Email
    {
        [Inject] private IMessageService _messageService { get; set; }
        [Inject] private ActiveUser _activeUser { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IStringLocalizer<Email> Localizer { get; set; }
        [Inject] private IJSRuntime JSRuntime { get; set; }

        private List<MessageModel> messages = new();
        private List<MessageModel> allMessages = new(); // Store all messages
        private string searchQuery = string.Empty;
        private string selectedTab = "inbox";
        private string currentFolder = "all";
        private string replyText = string.Empty;
        private bool muteThread; // Removed as it is unused
        private bool showCompose = false;
        private SendMessageRequest newMessage = new();
        private List<AttachmentRequest> attachments = new();
        private bool isLoading = false;
        private bool showDeleteConfirm = false;
        private MessageModel messageToDelete;
        private string recipientEmails = "";
        private string ccEmails = "";

        private List<MenuItem> menuItems = new()
            {
                new MenuItem { Name = "Inbox", Icon = "fas fa-inbox", Count = 0, IsActive = true, Folder = "inbox" },
                new MenuItem { Name = "Sent", Icon = "fas fa-paper-plane", Count = 0, Folder = "sent" },
                new MenuItem { Name = "Drafts", Icon = "fas fa-file", Count = 0, Folder = "drafts" },
                new MenuItem { Name = "Archive", Icon = "fas fa-archive", Count = 0, Folder = "archive" },
                new MenuItem { Name = "Trash", Icon = "fas fa-trash", Count = 0, Folder = "trash" },
                new MenuItem { Name = "Categories", IsCategory = true },
                new MenuItem { Name = "Unread", Icon = "fas fa-envelope", Count = 0, Folder = "unread" },
                new MenuItem { Name = "Important", Icon = "fas fa-star", Count = 0, Folder = "important" },
                new MenuItem { Name = "All Mail", Icon = "fas fa-mail-bulk", Count = 0, Folder = "all" }
            };



        private MessageModel selectedMessage;

        protected override async Task OnInitializedAsync()
        {
            await LoadMessagesAsync();
        }

        private async Task LoadMessagesAsync()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                // Debug: Check user email
                var userEmail = _activeUser?.mail ?? "<EMAIL>";
                Snackbar.Add($"Loading messages for user: {userEmail}", Severity.Info);

                // Load all messages from API
                allMessages = await _messageService.GetMessagesByEmailAsync(userEmail);
                Snackbar.Add($"Loaded {allMessages.Count} messages from API", Severity.Info);

                // Also load deleted messages for trash folder
                var deletedMessages = await _messageService.GetDeletedMessagesAsync(userEmail);
                Snackbar.Add($"Loaded {deletedMessages.Count} deleted messages from API", Severity.Info);
                allMessages.AddRange(deletedMessages);

                // Filter messages based on current folder
                FilterMessagesByFolder();

                // Update menu counts
                UpdateMenuCounts();

                Snackbar.Add($"Total loaded: {allMessages.Count} messages", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error loading messages: {ex.Message}", Severity.Error);
                Console.WriteLine($"LoadMessagesAsync Error: {ex}");


            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }



        private void FilterMessagesByFolder()
        {
            var userEmail = _activeUser?.mail ?? "";

            messages = currentFolder switch
            {
                "inbox" => allMessages.Where(m => m.ReceiverEmailId == userEmail && !m.IsDeleted && !m.IsArchived).ToList(),
                "sent" => allMessages.Where(m => m.SenderEmailId == userEmail && !m.IsDeleted && !m.IsArchived).ToList(),
                "trash" => allMessages.Where(m => m.IsDeleted).ToList(),
                "archive" => allMessages.Where(m => m.IsArchived && !m.IsDeleted).ToList(),
                "unread" => allMessages.Where(m => m.ReceiverEmailId == userEmail && !m.IsRead && !m.IsDeleted && !m.IsArchived).ToList(),
                "important" => allMessages.Where(m => m.IsImportant && !m.IsDeleted && !m.IsArchived).ToList(),
                "all" => allMessages.Where(m => !m.IsDeleted).ToList(),
                _ => allMessages.Where(m => m.ReceiverEmailId == userEmail && !m.IsDeleted && !m.IsArchived).ToList()
            };
        }



        private void UpdateMenuCounts()
        {
            var userEmail = _activeUser?.mail ?? "";

            foreach (var item in menuItems.Where(m => !m.IsCategory))
            {
                item.Count = item.Folder switch
                {
                    "inbox" => allMessages.Count(m => m.ReceiverEmailId == userEmail && !m.IsDeleted && !m.IsArchived),
                    "sent" => allMessages.Count(m => m.SenderEmailId == userEmail && !m.IsDeleted && !m.IsArchived),
                    "trash" => allMessages.Count(m => m.IsDeleted),
                    "archive" => allMessages.Count(m => m.IsArchived && !m.IsDeleted),
                    "unread" => allMessages.Count(m => m.ReceiverEmailId == userEmail && !m.IsRead && !m.IsDeleted && !m.IsArchived),
                    "important" => allMessages.Count(m => m.IsImportant && !m.IsDeleted && !m.IsArchived),
                    "all" => allMessages.Count(m => !m.IsDeleted),
                    _ => 0
                };
            }
        }



        private void SelectMessage(MessageModel message)
        {
            selectedMessage = message;

            // Mark message as read in the backend if it wasn't read
            if (!selectedMessage.IsRead)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _messageService.MarkMessageAsReadAsync(selectedMessage.MessageId);
                        selectedMessage.ReadDateTime = DateTime.Now;
                    }
                    catch (Exception ex)
                    {
                        // Log error but don't show to user for this background operation
                        Console.WriteLine($"Error marking message as read: {ex.Message}");
                    }
                });
            }
        }

        private IEnumerable<MessageModel> FilteredMessages => messages
            .Where(m => string.IsNullOrWhiteSpace(searchQuery) ||
                m.SenderName.Contains(searchQuery, StringComparison.OrdinalIgnoreCase) ||
                m.Subject.Contains(searchQuery, StringComparison.OrdinalIgnoreCase) ||
                m.MessageContent.Contains(searchQuery, StringComparison.OrdinalIgnoreCase))
            .Where(m => selectedTab == "all" || !m.IsRead)
            .OrderByDescending(m => m.SentDateTime);

        private void OnSearchChanged(string value)
        {
            searchQuery = value ?? "";
            StateHasChanged();
        }

        private void SetTab(string tab) => selectedTab = tab.Trim();

        private void SelectMenuItem(MenuItem item)
        {
            if (item.IsCategory) return;

            menuItems.ForEach(m => m.IsActive = false);
            item.IsActive = true;
            currentFolder = item.Folder;
            selectedMessage = null;

            FilterMessagesByFolder();
            StateHasChanged();
        }

        private void ToggleCompose() => showCompose = !showCompose;

        // Test method to create sample messages with attachments
        private async Task CreateTestMessages()
        {
            try
            {
                var userEmail = _activeUser?.mail ?? "<EMAIL>";
                var userName = _activeUser?.displayName ?? "Test User";

                // Create a test message with attachment
                var testMessageWithAttachment = new SendMessageRequest
                {
                    SenderName = "System Test",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = userName,
                    ReceiverEmailId = userEmail,
                    Subject = "Test Email with Sample Attachment",
                    MessageContent = "This is a test message with a sample attachment. The attachment is a small text file for testing purposes.\n\nPlease test:\n1. Viewing the attachment icon\n2. Downloading the attachment\n3. Verifying the file content",
                    Attachments = new List<AttachmentRequest>
                    {
                        new AttachmentRequest
                        {
                            FileName = "test-document.txt",
                            ContentType = "text/plain",
                            FileContentBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("This is a sample test document.\n\nContent:\n- Test line 1\n- Test line 2\n- Test line 3\n\nThis file was created automatically for testing email attachments.")),
                            FileSizeBytes = 150
                        }
                    }
                };

                // Create a regular test message
                var testMessage = new SendMessageRequest
                {
                    SenderName = "System Test",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = userName,
                    ReceiverEmailId = userEmail,
                    Subject = "Welcome to TeyaHealth Email System",
                    MessageContent = "This is a test message to verify your email system is working correctly. You can reply to this message or create new ones."
                };

                // Send both messages
                var messageId1 = await _messageService.SendMessageAsync(testMessageWithAttachment);
                var messageId2 = await _messageService.SendMessageAsync(testMessage);

                if (messageId1 != Guid.Empty && messageId2 != Guid.Empty)
                {
                    Snackbar.Add("Test messages created successfully (including one with attachment)!", Severity.Success);
                    await LoadMessagesAsync(); // Refresh messages
                }
                else
                {
                    Snackbar.Add("Failed to create some test messages", Severity.Warning);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error creating test messages: {ex.Message}", Severity.Error);
                Console.WriteLine($"CreateTestMessages error: {ex}");
            }
        }

        // Test method specifically for attachment functionality
        private async Task TestAttachmentFunctionality()
        {
            try
            {
                var userEmail = _activeUser?.mail ?? "<EMAIL>";
                var userName = _activeUser?.displayName ?? "Test User";

                
                var testMessage = new SendMessageRequest
                {
                    SenderName = "Attachment Test",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = userName,
                    ReceiverEmailId = userEmail,
                    Subject = "Multiple Attachments Test",
                    MessageContent = "This email contains multiple attachments for testing:\n\n1. sample-report.txt - A text report file\n2. config.json - A JSON configuration file\n\nPlease test downloading these attachments to verify the functionality works correctly.",
                    Attachments = attachments
                };

                var messageId = await _messageService.SendMessageAsync(testMessage);
                if (messageId != Guid.Empty)
                {
                    Snackbar.Add($"Attachment test message created with {attachments.Count} attachments!", Severity.Success);
                    await LoadMessagesAsync(); // Refresh messages
                }
                else
                {
                    Snackbar.Add("Failed to create attachment test message", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error creating attachment test: {ex.Message}", Severity.Error);
                Console.WriteLine($"TestAttachmentFunctionality error: {ex}");
            }
        }

        


        private async Task SendEmail()
        {
            try
            {
                Console.WriteLine("SendEmail called");

                // Parse recipients
                var toEmails = ParseEmailList(recipientEmails);
                var ccEmailsList = ParseEmailList(ccEmails);

                if (!toEmails.Any() || string.IsNullOrWhiteSpace(newMessage.MessageContent))
                {
                    Snackbar.Add("Please fill in at least one recipient and message content", Severity.Warning);
                    return;
                }

                // Validate attachments before sending
                if (attachments.Any())
                {
                    Console.WriteLine($"Validating {attachments.Count} attachments before sending");
                    foreach (var attachment in attachments)
                    {
                        if (string.IsNullOrEmpty(attachment.FileContentBase64))
                        {
                            Snackbar.Add($"Attachment {attachment.FileName} has no content", Severity.Error);
                            return;
                        }
                        if (attachment.FileSizeBytes <= 0)
                        {
                            Snackbar.Add($"Attachment {attachment.FileName} has invalid size", Severity.Error);
                            return;
                        }
                        Console.WriteLine($"Attachment validated: {attachment.FileName}, Size: {attachment.FileSizeBytes}, ContentType: {attachment.ContentType}");
                    }
                }

                isLoading = true;
                StateHasChanged();

                // Set sender information from current user
                newMessage.SenderName = _activeUser?.displayName ?? "Unknown User";
                newMessage.SenderEmailId = _activeUser?.mail ?? "<EMAIL>";
                newMessage.ToEmails = toEmails;
                newMessage.CcEmails = ccEmailsList;

                // Fix attachment assignment - always assign the list, even if empty
                newMessage.Attachments = attachments.ToList();

                newMessage.ReceiverEmailId = toEmails.First();
                newMessage.ReceiverName = toEmails.First();

                Console.WriteLine($"Sending message with {newMessage.Attachments?.Count ?? 0} attachments");

                // Send message via API
                var messageId = await _messageService.SendMessageAsync(newMessage);

                if (messageId != Guid.Empty)
                {
                    var recipientCount = toEmails.Count + ccEmailsList.Count;
                    Snackbar.Add($"Message sent successfully to {recipientCount} recipient(s)!", Severity.Success);

                    // Add to local list for immediate UI update
                    var sentMessage = new MessageModel
                    {
                        MessageId = messageId,
                        SenderName = newMessage.SenderName,
                        SenderEmailId = newMessage.SenderEmailId,
                        ReceiverName = string.Join(", ", toEmails),
                        ReceiverEmailId = string.Join(", ", toEmails),
                        Subject = newMessage.Subject,
                        MessageContent = newMessage.MessageContent,
                        SentDateTime = DateTime.Now,
                        Status = MessageStatus.Sent,
                        HasAttachments = attachments.Any(),
                        AttachmentCount = attachments.Count
                    };

                    // Add to all messages list
                    allMessages.Insert(0, sentMessage);

                    // Refresh current view and counts
                    FilterMessagesByFolder();
                    UpdateMenuCounts();

                    // Clear form only on success
                    ClearComposeForm();
                    Snackbar.Add("Form cleared after successful send", Severity.Info);
                }
                else
                {
                    Snackbar.Add("Failed to send message - API returned empty ID", Severity.Error);
                    // Don't clear form on failure so user can retry
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SendEmail error: {ex}");
                Snackbar.Add($"Error sending message: {ex.Message}", Severity.Error);
                // Don't clear form on error so user can retry
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        // Helper method to clear the compose form
        private void ClearComposeForm()
        {
            newMessage = new SendMessageRequest();
            attachments.Clear();
            recipientEmails = "";
            ccEmails = "";
            showCompose = false;
        }
        private async Task TestSimpleMessage()
        {
            try
            {
                var userEmail = _activeUser?.mail ?? "<EMAIL>";
                var userName = _activeUser?.displayName ?? "Test User";

                var testMessage = new SendMessageRequest
                {
                    SenderName = "Simple Test",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = userName,
                    ReceiverEmailId = userEmail,
                    Subject = "Simple Test Message (No Attachments)",
                    MessageContent = "This is a simple test message without any attachments to verify basic functionality works."
                };

                var messageId = await _messageService.SendMessageAsync(testMessage);
                if (messageId != Guid.Empty)
                {
                    Snackbar.Add("Simple test message created successfully!", Severity.Success);
                    await LoadMessagesAsync(); // Refresh messages
                }
                else
                {
                    Snackbar.Add("Failed to create simple test message", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error creating simple test: {ex.Message}", Severity.Error);
                Console.WriteLine($"TestSimpleMessage error: {ex}");
            }
        }




        private void ShowDeleteConfirmation(MessageModel message)
        {
            Console.WriteLine($"ShowDeleteConfirmation called for message: {message.Subject}");
            messageToDelete = message;
            showDeleteConfirm = true;
            StateHasChanged();
            Snackbar.Add($"Delete confirmation shown for: {message.Subject}", Severity.Info);
        }

        private void CancelDelete()
        {
            Console.WriteLine("CancelDelete called");
            messageToDelete = null;
            showDeleteConfirm = false;
            StateHasChanged();
            Snackbar.Add("Delete cancelled", Severity.Info);
        }

        private async Task DeleteMessage()
        {
            Console.WriteLine("DeleteMessage called");
            if (messageToDelete == null)
            {
                Console.WriteLine("DeleteMessage: messageToDelete is null");
                return;
            }

            try
            {
                Console.WriteLine($"DeleteMessage: Processing message {messageToDelete.Subject}");
                var userEmail = _activeUser?.mail ?? "<EMAIL>";

                Snackbar.Add($"Deleting message: {messageToDelete.Subject}", Severity.Info);

                // Use soft delete instead of hard delete
                var success = await _messageService.SoftDeleteMessageAsync(
                    messageToDelete.MessageId,
                    userEmail,
                    "Moved to trash by user");

                Console.WriteLine($"DeleteMessage: API call result = {success}");

                if (success)
                {
                    // Mark as deleted locally
                    messageToDelete.IsDeleted = true;
                    messageToDelete.DeletedDateTime = DateTime.Now;

                    // Refresh the current view
                    FilterMessagesByFolder();
                    UpdateMenuCounts();

                    // Clear selection if deleted message was selected
                    if (selectedMessage?.MessageId == messageToDelete.MessageId)
                    {
                        selectedMessage = null;
                    }

                    Snackbar.Add("Message moved to trash", Severity.Success);
                }
                else
                {
                    Snackbar.Add("Failed to move message to trash", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DeleteMessage error: {ex}");
                Snackbar.Add($"Error moving message to trash: {ex.Message}", Severity.Error);
            }
            finally
            {
                Console.WriteLine("DeleteMessage: Calling CancelDelete");
                CancelDelete();
                StateHasChanged();
            }
        }

        private async Task ArchiveMessage(MessageModel message)
        {
            try
            {
                var userEmail = _activeUser?.mail ?? "<EMAIL>";

                // Archive using backend API
                var success = await _messageService.ArchiveMessageAsync(message.MessageId, userEmail);

                if (success)
                {
                    // Mark as archived locally
                    message.IsArchived = true;
                    message.ArchivedDateTime = DateTime.Now;

                    // Refresh the current view
                    FilterMessagesByFolder();
                    UpdateMenuCounts();

                    // Clear selection if archived message was selected
                    if (selectedMessage?.MessageId == message.MessageId)
                    {
                        selectedMessage = null;
                    }

                    Snackbar.Add("Message archived", Severity.Success);
                }
                else
                {
                    Snackbar.Add("Failed to archive message", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error archiving message: {ex.Message}", Severity.Error);
            }
            finally
            {
                StateHasChanged();
            }
        }

        private async Task UnarchiveMessage(MessageModel message)
        {
            try
            {
                var userEmail = _activeUser?.mail ?? "<EMAIL>";


                var success = await _messageService.UnarchiveMessageAsync(message.MessageId, userEmail);

                if (success)
                {
                    // Mark as unarchived locally
                    message.IsArchived = false;
                    message.ArchivedDateTime = null;

                    // Refresh the current view
                    FilterMessagesByFolder();
                    UpdateMenuCounts();

                    Snackbar.Add("Message unarchived", Severity.Success);
                }
                else
                {
                    Snackbar.Add("Failed to unarchive message", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error unarchiving message: {ex.Message}", Severity.Error);
            }
            finally
            {
                StateHasChanged();
            }
        }

        private async Task ToggleImportant(MessageModel message)
        {
            try
            {
                var originalStatus = message.IsImportant;
                var newStatus = !message.IsImportant;
                var userEmail = _activeUser?.mail ?? "<EMAIL>";

                // Update in backend first
                var success = await _messageService.ToggleImportantAsync(message.MessageId, newStatus, userEmail);

                if (success)
                {
                    // Update locally only if backend update succeeded
                    message.IsImportant = newStatus;
                    UpdateMenuCounts();

                    var status = message.IsImportant ? "marked as important" : "unmarked as important";
                    Snackbar.Add($"Message {status}", Severity.Success);
                }
                else
                {
                    Snackbar.Add("Failed to update important status", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error updating message: {ex.Message}", Severity.Error);
            }
            finally
            {
                StateHasChanged();
            }
        }

        private async Task RestoreFromTrash(MessageModel message)
        {
            try
            {
                var userEmail = _activeUser?.mail ?? "<EMAIL>";

                // Restore from trash using backend API
                var success = await _messageService.RestoreMessageAsync(
                    message.MessageId,
                    userEmail,
                    "Restored by user");

                if (success)
                {
                    // Update locally
                    message.IsDeleted = false;
                    message.DeletedDateTime = null;

                    // Refresh the current view
                    FilterMessagesByFolder();
                    UpdateMenuCounts();

                    Snackbar.Add("Message restored", Severity.Success);
                }
                else
                {
                    Snackbar.Add("Failed to restore message", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error restoring message: {ex.Message}", Severity.Error);
            }
            finally
            {
                StateHasChanged();
            }
        }

        private async Task PermanentlyDeleteMessage(MessageModel message)
        {
            try
            {
                isLoading = true;

                // Remove from backend
                await _messageService.DeleteMessageAsync(message.MessageId);

                // Remove from local lists
                allMessages.Remove(message);
                messages.Remove(message);

                // Update UI
                FilterMessagesByFolder();
                UpdateMenuCounts();

                // Clear selection if deleted message was selected
                if (selectedMessage?.MessageId == message.MessageId)
                {
                    selectedMessage = null;
                }

                Snackbar.Add("Message permanently deleted", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error permanently deleting message: {ex.Message}", Severity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }



        private List<string> ParseEmailList(string emailString)
        {
            if (string.IsNullOrWhiteSpace(emailString))
                return new List<string>();

            return emailString
                .Split(new char[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(email => email.Trim())
                .Where(email => !string.IsNullOrWhiteSpace(email))
                .ToList();
        }

        // Helper methods for Gmail-like UI
        private string GetInitials(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 1)
                return parts[0].Substring(0, Math.Min(2, parts[0].Length)).ToUpper();
            else if (parts.Length >= 2)
                return (parts[0].Substring(0, 1) + parts[1].Substring(0, 1)).ToUpper();
            else
                return name.Substring(0, Math.Min(2, name.Length)).ToUpper();
        }

        private string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "Just now";
            else if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} min ago";
            else if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hour{((int)timeSpan.TotalHours != 1 ? "s" : "")} ago";
            else if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} day{((int)timeSpan.TotalDays != 1 ? "s" : "")} ago";
            else if (dateTime.Year == DateTime.Now.Year)
                return dateTime.ToString("MMM dd");
            else
                return dateTime.ToString("MMM dd, yyyy");
        }

        private void ShowReply()
        {
            if (selectedMessage != null)
            {
                // Use the compose modal for replies instead of separate reply UI
                showCompose = true;
                showReply = false; // Ensure reply modal is closed

                // Clear any existing attachments
                attachments.Clear();

                // Set up reply message
                newMessage = new SendMessageRequest
                {
                    Subject = selectedMessage.Subject.StartsWith("Re:") ? selectedMessage.Subject : $"Re: {selectedMessage.Subject}",
                    SenderName = _activeUser?.displayName ?? "Unknown User",
                    SenderEmailId = _activeUser?.mail ?? "<EMAIL>",
                    ReceiverName = selectedMessage.SenderName,
                    ReceiverEmailId = selectedMessage.SenderEmailId,
                    MessageContent = "" // Clean reply without footer
                };

                // Set recipient fields for the compose form
                recipientEmails = selectedMessage.SenderEmailId;
                ccEmails = "";

                Snackbar.Add($"Replying to {selectedMessage.SenderName}", Severity.Info);
            }
        }

        private string GetDeliveryStatusIcon(MessageStatus status)
        {
            return status switch
            {
                MessageStatus.Sent => "✓",
                MessageStatus.Delivered => "✓✓",
                MessageStatus.Read => "✓✓",
                MessageStatus.Failed => "✗",
                _ => "○"
            };
        }

        private string GetDeliveryStatusText(MessageStatus status)
        {
            return status switch
            {
                MessageStatus.Sent => "Sent",
                MessageStatus.Delivered => "Delivered",
                MessageStatus.Read => "Read",
                MessageStatus.Failed => "Failed to send",
                _ => "Pending"
            };
        }

        // File attachment methods
        private async Task OnFileSelected(Microsoft.AspNetCore.Components.Forms.InputFileChangeEventArgs e)
        {
            try
            {
                foreach (var file in e.GetMultipleFiles(5)) // Max 5 files
                {
                    if (file.Size > 10 * 1024 * 1024) // 10MB limit
                    {
                        Snackbar.Add($"File {file.Name} is too large (max 10MB)", Severity.Warning);
                        continue;
                    }

                    using var stream = file.OpenReadStream(10 * 1024 * 1024);
                    var buffer = new byte[file.Size];
                    await stream.ReadAsync(buffer);

                    // Fix content type if browser doesn't provide it correctly
                    var contentType = file.ContentType;
                    if (string.IsNullOrEmpty(contentType) || contentType == "application/octet-stream")
                    {
                        contentType = GetContentTypeFromFileName(file.Name);
                    }

                    var attachment = new AttachmentRequest
                    {
                        FileName = file.Name,
                        ContentType = contentType,
                        FileContentBase64 = Convert.ToBase64String(buffer),
                        FileSizeBytes = file.Size
                    };

                    Console.WriteLine($"Adding attachment: {file.Name}, ContentType: {contentType}, Size: {file.Size}");
                    attachments.Add(attachment);
                    Snackbar.Add($"Added attachment: {file.Name} ({contentType})", Severity.Success);
                }

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"OnFileSelected error: {ex}");
                Snackbar.Add($"Error adding attachment: {ex.Message}", Severity.Error);
            }
        }

        // Helper method to determine content type from file extension
        private string GetContentTypeFromFileName(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".txt" => "text/plain",
                ".json" => "application/json",
                ".xml" => "application/xml",
                ".html" => "text/html",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                ".7z" => "application/x-7z-compressed",
                ".mp3" => "audio/mpeg",
                ".wav" => "audio/wav",
                ".ogg" => "audio/ogg",
                ".mp4" => "video/mp4",
                ".avi" => "video/avi",
                ".mov" => "video/mov",
                ".wmv" => "video/wmv",
                _ => "application/octet-stream"
            };
        }

        private void RemoveAttachment(AttachmentRequest attachment)
        {
            attachments.Remove(attachment);
            StateHasChanged();
        }

        // Load attachments for a specific message
        private async Task LoadMessageAttachmentsAsync(MessageModel message)
        {
            try
            {
                if (message.HasAttachments)
                {
                    var messageAttachments = await _messageService.GetMessageAttachmentsAsync(message.MessageId);
                    message.Attachments = messageAttachments.ToList();
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error loading attachments: {ex.Message}", Severity.Warning);
            }
        }


        private async Task DownloadAttachmentAsync(AttachmentModel attachment)
        {
            try
            {
                var fileContent = await _messageService.DownloadAttachmentAsync(attachment.AttachmentId);

                // Use JavaScript to trigger download
                var fileName = attachment.OriginalFileName;
                var base64 = Convert.ToBase64String(fileContent);
                var dataUrl = $"data:{attachment.ContentType};base64,{base64}";

                await JSRuntime.InvokeVoidAsync("downloadFile", dataUrl, fileName);

                Snackbar.Add($"Downloaded {fileName}", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error downloading attachment: {ex.Message}", Severity.Error);
            }
        }




        private bool showReply = false;
        private NewEmailModel replyEmail = new();

        // Simplified reply methods - now using compose modal
        private void CloseReply()
        {
            showReply = false;
            replyEmail = new NewEmailModel();
        }
    }
}