﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Http.HttpResults;
using TeyaWebApp.Components.Layout;

namespace TeyaWebApp.Components.Pages
{
    public partial class DiagnosticImaging : ComponentBase
    {
        private MudDialog _diagnosticImagingDialog;
        private SfGrid<DiagnosticImage> futureImagingGrid { get; set; }
        private List<DiagnosticImage> DiagnosticImagingList { get; set; }
        private SfGrid<DiagnosticImage> todayImagingGrid { get; set; }
        private List<DiagnosticImage> FutureImagingList { get; set; }
        private DiagnosticImage newDiagnosticImaging = new();
        private bool isDialogOpen = false;
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private Guid PatientID { get; set; }
        private Guid? organizationId { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }

        [Inject] ISnackbar SnackBar { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientID = _PatientService.PatientData.Id;
                organizationId = _PatientService.PatientData.OrganizationID;
                DiagnosticImagingList = (await DiagnosticImagingService.GetDiagnosticImagingByIdAsyncAndIsActive(PatientID))
                .OrderBy(DiagnosticImagingRecord => DiagnosticImagingRecord.CreatedDate)
                .ToList();
                ViewHandler();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }
        /// <summary>
        /// To Handle click Outside the SF Grid
        /// </summary>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Update to RichTextEditor
        /// </summary>
        private void ViewHandler()
        {
            string str = "";

            foreach (var member in DiagnosticImagingList)
            {
                str += $"<li><b>{member.Type}:</b> {member.CreatedDate}</li>";
            }

            str += "</ul>";

            editorContent = str;
        }

        private async Task SaveChanges()
        {
            if (string.IsNullOrWhiteSpace(newDiagnosticImaging.DiCompany) || 
               string.IsNullOrWhiteSpace(newDiagnosticImaging.Type) ||
               string.IsNullOrWhiteSpace(newDiagnosticImaging.Lookup) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.OrderName) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.ccResults) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.StartsWith))
            {
                Snackbar.Add("Please enter all required fields.", Severity.Warning);
                return;
            }
            newDiagnosticImaging.PatientId = PatientID;
            newDiagnosticImaging.OrganizationID = organizationId;
            newDiagnosticImaging.CreatedDate = DateTime.Now;
            newDiagnosticImaging.UpdatedDate = DateTime.Now;
            newDiagnosticImaging.CreatedBy = Guid.Parse(User.id);
            newDiagnosticImaging.UpdatedBy = PatientID;
            newDiagnosticImaging.RecordID = new Guid();
            newDiagnosticImaging.IsActive = true;
            await DiagnosticImagingService.CreateDiagnosticImagingAsync(new List<DiagnosticImage> { newDiagnosticImaging });
            DiagnosticImagingList = await DiagnosticImagingService.GetDiagnosticImagingByIdAsyncAndIsActive(PatientID);
            ViewHandler();
            await InvokeAsync(StateHasChanged);
            CloseDialog();
        }



        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add", TooltipText = "Add Medication" },
        };

      
        /// <summary>
        /// Cancel all edit, delete and add operations and close the dialogBox
        /// </summary>
        private async Task CancelChanges()
        {
            await InvokeAsync(StateHasChanged);
            CloseDialog();
        }

        /// <summary>
        /// Open dialog
        /// </summary>
        private void OpenDialog()
        {
            isDialogOpen = true;
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseDialog()
        {
            newDiagnosticImaging = new();
            isDialogOpen = false;
        }
        private async void CancelAction()
        {
            _diagnosticImagingDialog.CloseAsync();
            await InvokeAsync(StateHasChanged);
            StateHasChanged();
        }
    }


}





