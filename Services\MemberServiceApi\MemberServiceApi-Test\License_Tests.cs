﻿
//Unit Tests Using NUnit 3.0 for LicenseController

using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Test
{
    //Base Test Class for LicenseController With Mock Items
    [TestFixture]
    public class LicensesControllerTests                                        
    {
        private Mock<ILicenseQueryHandler<ProductLicense>> _mockQueryHandler;
        private Mock<ILogger<LicensesController>> _mockLogger;
        private Mock<IStringLocalizer<LicensesController>> _mockLocalizer;
        private Mock<ILicenseCommandHandler<ProductLicense>> _mockCommandHandler;
        private LicensesController _controller;

        //Setting up Mock Objects
        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<ILicenseQueryHandler<ProductLicense>>();
            _mockLogger = new Mock<ILogger<LicensesController>>();
            _mockLocalizer = new Mock<IStringLocalizer<LicensesController>>();
            _mockCommandHandler = new Mock<ILicenseCommandHandler<ProductLicense>>();

            _controller = new LicensesController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        
        [Test] 
        public async Task Get_WhenCalled_ReturnsOkWithLicenses()
        {
            // Arrange
            var mockLicenses = new List<ProductLicense>
            {
                new ProductLicense 
                { Id = Guid.NewGuid(),ProductName = "MyProduct",
                Description = "Product description",
                IsLicenseActivated = true },
                new ProductLicense 
                {Id = Guid.NewGuid(), ProductName = "MyProduct", Description = "Product description", IsLicenseActivated = true}
            };

            _mockQueryHandler
                .Setup(q => q.GetLicense())
                .ReturnsAsync(mockLicenses);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockLicenses));
        }

        
        [Test] 
        public async Task GetById_WhenCalledWithValidId_ReturnsOkWithLicense()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var mockLicense = new ProductLicense
            {
                ProductName = "MyProduct",
                Description = "Product description",
                IsLicenseActivated = true
            };

            _mockQueryHandler
                .Setup(q => q.GetLicenseById(licenseId))
                .ReturnsAsync(mockLicense);

            // Act
            var result = await _controller.GetById(licenseId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockLicense));
        }

        
        [Test] 
        public async Task UpdateById_WhenCalledWithValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var license = new ProductLicense
            {
                Id = licenseId,
                ProductName = "UpdatedProduct",
                Description = "Updated description",
                IsLicenseActivated = true
            };

            _mockCommandHandler
                .Setup(handler => handler.UpdateLicense(It.IsAny<ProductLicense>()))
                .Returns(Task.CompletedTask);

            var localizedString = new LocalizedString("UpdateSuccessful", "Update Successful");

            _mockLocalizer
                .Setup(l => l["UpdateSuccessful"])
                .Returns(localizedString);

            // Act
            var result = await _controller.UpdateById(licenseId, license);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value.ToString(), Is.EqualTo("Update Successful"));
        }

        
        [Test] 
        public async Task UpdateById_WhenCalledWithNullLicense_ReturnsBadRequest()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            ProductLicense license = null;  

            var localizedString = new LocalizedString("InvalidLicense", "Invalid License");

            _mockLocalizer
                .Setup(l => l["InvalidLicense"])
                .Returns(localizedString);

            // Act
            var result = await _controller.UpdateById(licenseId, license);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));
            Assert.That(badRequestResult.Value.ToString(), Is.EqualTo("Invalid License")); 
        }

        
        [Test] 
        public async Task DeleteById_WhenCalledWithValidId_ReturnsOkWithMessage()
        {
            // Arrange
            var licenseId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(handler => handler.DeleteLicenseById(licenseId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteById(licenseId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

        
        [Test] 
        public async Task Registration_WhenCalledWithValidRegistrations_ReturnsOkWithMessage()
        {
            // Arrange
            var registrations = new List<ProductLicense>
            {
            new ProductLicense { Id = Guid.NewGuid(), ProductName = "Product1", Description = "Product 1 description", IsLicenseActivated = true },
            new ProductLicense { Id = Guid.NewGuid(), ProductName = "Product2", Description = "Product 2 description", IsLicenseActivated = true }
            };

            _mockCommandHandler
                .Setup(handler => handler.AddLicense(It.IsAny<List<ProductLicense>>()))
                .Returns(Task.CompletedTask);

            var localizedString = new LocalizedString("SuccessfulRegistration", "Registration Successful");
            _mockLocalizer
                .Setup(l => l["SuccessfulRegistration"])
                .Returns(localizedString);

            // Act
            var result = await _controller.Registration(registrations);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value.ToString(), Is.EqualTo("Registration Successful"));
        }

        
        [Test] 
        public async Task Registration_WhenCalledWithEmptyRegistrations_ReturnsBadRequest()
        {
            // Arrange
            var registrations = new List<ProductLicense>();

            var localizedString = new LocalizedString("NoLicense", "No licenses provided");
            _mockLocalizer
                .Setup(l => l["NoLicense"])
                .Returns(localizedString);

            // Act
            var result = await _controller.Registration(registrations);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));
            Assert.That(badRequestResult.Value.ToString(), Is.EqualTo("No licenses provided"));
        }

       
        [Test] 
        public async Task DeleteByEntity_WhenCalledWithValidLicense_ReturnsOkWithMessage()
        {
            // Arrange
            var license = new ProductLicense
            {
                Id = Guid.NewGuid(),
                ProductName = "MyProduct",
                Description = "Product description",
                IsLicenseActivated = true
            };

            _mockCommandHandler
                .Setup(handler => handler.DeleteLicenseByEntity(It.IsAny<ProductLicense>()))
                .Returns(Task.CompletedTask);

            var localizedString = new LocalizedString("DeleteSuccessful", "Delete Successful");
            _mockLocalizer
                .Setup(l => l["DeleteSuccessful"])
                .Returns(localizedString);

            // Act
            var result = await _controller.DeleteByEntity(license);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value.ToString(), Is.EqualTo("Delete Successful"));
        }

       
        [Test] 
        public async Task UpdateAccess_WhenCalledWithValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>
            {
                new ProductLicense { 
                    Id = Guid.NewGuid(),
                    ProductName = "UpdatedProduct",
                    Description = "Updated description",
                    IsLicenseActivated = true 
                },
                new ProductLicense { 
                    Id = Guid.NewGuid(), 
                    ProductName = "Product2",
                    Description = "Updated description", 
                    IsLicenseActivated = false 
                }
            };

            _mockCommandHandler
                .Setup(handler => handler.UpdateLicenseAccessAsync(It.IsAny<List<ProductLicense>>()))
                .ReturnsAsync(true);

            var localizedString = new LocalizedString("AccessUpdateSuccessful", "Access Update Successful");
            _mockLocalizer
                .Setup(l => l["AccessUpdateSuccessful"])
                .Returns(localizedString);

            // Act
            var result = await _controller.UpdateAccess(licenseAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value.ToString(), Is.EqualTo("{ Message = Access Update Successful }"));
        }

       
        [Test] 
        public async Task UpdateAccess_WhenCalledWithNullData_ReturnsBadRequest()
        {
            // Arrange
            List<ProductLicense> licenseAccessUpdates = null;

            var localizedString = new LocalizedString("AccessError", "Access data error");
            _mockLocalizer
                .Setup(l => l["AccessError"])
                .Returns(localizedString);

            // Act
            var result = await _controller.UpdateAccess(licenseAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));
            Assert.That(badRequestResult.Value.ToString(), Is.EqualTo("Access data error"));
        }

        
        [Test]
        public async Task UpdateAccess_WhenUpdateFails_ReturnsNotFound()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>
            {
                new ProductLicense { Id = Guid.NewGuid(), ProductName = "Product2",Description = "Updated description", IsLicenseActivated = true }
            };

            _mockCommandHandler
                .Setup(handler => handler.UpdateLicenseAccessAsync(It.IsAny<List<ProductLicense>>()))
                .ReturnsAsync(false);

            var localizedString = new LocalizedString("Access record not found", "Access record not found");
            _mockLocalizer
                .Setup(l => l["Access record not found"])
                .Returns(localizedString);

            // Act
            var result = await _controller.UpdateAccess(licenseAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>(), "Expected result to be of type NotFoundObjectResult");
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult!.StatusCode, Is.EqualTo(404));
            Assert.That(notFoundResult.Value.ToString(), Is.EqualTo("Access record not found"));
        }
    }
}
