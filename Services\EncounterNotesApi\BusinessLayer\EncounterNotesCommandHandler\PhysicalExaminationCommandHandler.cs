﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class PhysicalExaminationCommandHandler : IPhysicalExaminationCommandHandler<PhysicalExamination>
    {
        private readonly IUnitOfWork _unitOfWork;
        public PhysicalExaminationCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddExamination(List<PhysicalExamination> physicals)
        {
            await _unitOfWork.PhysicalExaminationRepository.AddAsync(physicals);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateExamination(PhysicalExamination Phyexamination)
        {
            await _unitOfWork.PhysicalExaminationRepository.UpdateAsync(Phyexamination);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteExaminationById(Guid id)
        {
            await _unitOfWork.PhysicalExaminationRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteExaminationByEntity(PhysicalExamination physical)
        {
            await _unitOfWork.PhysicalExaminationRepository.DeleteByEntityAsync(physical);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateExaminationList(List<PhysicalExamination> physicalExamination)
        {
            await _unitOfWork.PhysicalExaminationRepository.UpdateRangeAsync(physicalExamination);
            await _unitOfWork.SaveAsync();
        }
    }
}