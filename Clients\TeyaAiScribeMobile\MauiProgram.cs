﻿using Microsoft.Extensions.Logging;
using CommunityToolkit.Maui;
using Plugin.Maui.Audio;
namespace TeyaAiScribeMobile
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder.Services.AddSingleton<IAudioManager>(AudioManager.Current);
            builder.Services.AddTransient<MainPage>();
            builder.UseMauiApp<App>().ConfigureFonts(fonts =>
            {
                // Add your custom fonts here
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                // Add the MaterialIcons font here
                fonts.AddFont("FluentSystemIcons-Filled.ttf", "FluentIcons");
            }).UseMauiCommunityToolkit();
#if DEBUG
            builder.Logging.AddDebug();
#endif
            return builder.Build();
        }
    }
}