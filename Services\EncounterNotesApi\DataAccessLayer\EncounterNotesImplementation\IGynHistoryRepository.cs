﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IGynHistoryRepository<T> where T : class
    {
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> GetByPatientIdAsync(Guid patientId);

        Task<T> GetByIdAsync(Guid id);
        Task AddAsync(IEnumerable<T> entities);
        Task UpdateAsync(T entity);
        Task DeleteByEntityAsync(T entity);
        Task DeleteByIdAsync(Guid id);
        Task UpdateGynHistoryListAsync(List<GynHistory> gynhistory);
    }
}