using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using MessageContracts;
using MessageApi.Controllers;
using Microsoft.AspNetCore.Mvc;
using MessageBusinessLayer;
using MessageBusinessLayer.Services;
using System.Linq;

namespace MessageApi.Tests.Controllers
{
    [TestFixture]
    public class MessageControllerTests
    {
        private Mock<IMessageCommandHandler<Message>> _commandHandlerMock;
        private Mock<IMessageQueryHandler<Message>> _queryHandlerMock;
        private Mock<IAttachmentService> _attachmentServiceMock;
        private Mock<ILogger<MessageController>> _loggerMock;
        private Mock<IStringLocalizer<MessageController>> _localizerMock;
        private Mock<IMessageNotificationService> _notificationServiceMock;
        private MessageController _controller;
        private List<Message> _testMessages;
        private List<MessageAttachment> _testAttachments;

        [SetUp]
        public void Setup()
        {
            _commandHandlerMock = new Mock<IMessageCommandHandler<Message>>();
            _queryHandlerMock = new Mock<IMessageQueryHandler<Message>>();
            _attachmentServiceMock = new Mock<IAttachmentService>();
            _loggerMock = new Mock<ILogger<MessageController>>();
            _localizerMock = new Mock<IStringLocalizer<MessageController>>();
            _notificationServiceMock = new Mock<IMessageNotificationService>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching messages."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["MessageNotFound"]).Returns(new LocalizedString("MessageNotFound", "Message not found."));

            _testMessages = new List<Message>
            {
                new Message
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject",
                    MessageContent = "Test Content",
                    SentDateTime = DateTime.UtcNow,
                    Status = 1,
                    IsDeleted = false,
                    IsArchived = false,
                    HasAttachments = false,
                    AttachmentCount = 0
                },
                new Message
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "Jane Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "John Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Reply Subject",
                    MessageContent = "Reply Content",
                    SentDateTime = DateTime.UtcNow.AddMinutes(-30),
                    Status = 1,
                    IsDeleted = false,
                    IsArchived = false,
                    HasAttachments = true,
                    AttachmentCount = 1
                }
            };

            _testAttachments = new List<MessageAttachment>
            {
                new MessageAttachment
                {
                    AttachmentId = Guid.NewGuid(),
                    MessageId = _testMessages[1].MessageId,
                    FileName = "test.pdf",
                    OriginalFileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileSizeBytes = 1024,
                    BlobFileName = "blob_test.pdf",
                    BlobStorageUrl = "https://test.blob.core.windows.net/attachments/blob_test.pdf",
                    BlobContainerName = "attachments",
                    FileHash = "testhash123",
                    UploadedDateTime = DateTime.UtcNow,
                    IsScanned = true,
                    IsSafe = true
                }
            };

            _controller = new MessageController(
                _commandHandlerMock.Object,
                _queryHandlerMock.Object,
                _attachmentServiceMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _notificationServiceMock.Object
            );
        }

        #region SendMessage Tests

        [Test]
        public async Task SendMessage_ValidRequest_ReturnsOkWithMessageId()
        {
            // Arrange
            var request = new SendMessageRequest
            {
                SenderName = "John Doe",
                SenderEmailId = "<EMAIL>",
                ToEmails = new List<string> { "<EMAIL>" },
                Subject = "Test Subject",
                MessageContent = "Test Content",
                Attachments = null
            };

            var messageId = Guid.NewGuid();
            _commandHandlerMock.Setup(x => x.SendMessage(It.IsAny<Message>(), It.IsAny<List<AttachmentRequest>>()))
                              .ReturnsAsync(messageId);
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync(_testMessages[0]);

            // Act
            var result = await _controller.SendMessage(request);

            // Assert
            result.Should().BeOfType<ActionResult<Guid>>();
            var actionResult = result as ActionResult<Guid>;
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task SendMessage_WithAttachments_ReturnsOkWithMessageId()
        {
            // Arrange
            var attachmentRequest = new AttachmentRequest
            {
                FileName = "test.pdf",
                ContentType = "application/pdf",
                FileContentBase64 = Convert.ToBase64String(new byte[] { 1, 2, 3, 4, 5 })
            };

            var request = new SendMessageRequest
            {
                SenderName = "John Doe",
                SenderEmailId = "<EMAIL>",
                ToEmails = new List<string> { "<EMAIL>" },
                Subject = "Test Subject",
                MessageContent = "Test Content",
                Attachments = new List<AttachmentRequest> { attachmentRequest }
            };

            var messageId = Guid.NewGuid();
            _commandHandlerMock.Setup(x => x.SendMessage(It.IsAny<Message>(), It.IsAny<List<AttachmentRequest>>()))
                              .ReturnsAsync(messageId);
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync(_testMessages[1]);

            // Act
            var result = await _controller.SendMessage(request);

            // Assert
            result.Should().BeOfType<ActionResult<Guid>>();
            var actionResult = result as ActionResult<Guid>;
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task SendMessage_MultipleRecipients_ReturnsOkWithMessageIds()
        {
            // Arrange
            var request = new SendMessageRequest
            {
                SenderName = "John Doe",
                SenderEmailId = "<EMAIL>",
                ToEmails = new List<string> { "<EMAIL>", "<EMAIL>" },
                Subject = "Test Subject",
                MessageContent = "Test Content"
            };

            var messageId1 = Guid.NewGuid();
            var messageId2 = Guid.NewGuid();
            _commandHandlerMock.SetupSequence(x => x.SendMessage(It.IsAny<Message>(), It.IsAny<List<AttachmentRequest>>()))
                              .ReturnsAsync(messageId1)
                              .ReturnsAsync(messageId2);
            _queryHandlerMock.Setup(x => x.GetMessageById(It.IsAny<Guid>()))
                            .ReturnsAsync(_testMessages[0]);

            // Act
            var result = await _controller.SendMessage(request);

            // Assert
            result.Should().BeOfType<ActionResult<Guid>>();
            var actionResult = result as ActionResult<Guid>;
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task SendMessage_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            var request = new SendMessageRequest(); // Empty request
            _controller.ModelState.AddModelError("SenderEmailId", "Required");

            // Act
            var result = await _controller.SendMessage(request);

            // Assert
            result.Should().BeOfType<ActionResult<Guid>>();
            var actionResult = result as ActionResult<Guid>;
            var badRequestResult = actionResult.Result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
        }

        [Test]
        public async Task SendMessage_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var request = new SendMessageRequest
            {
                SenderName = "John Doe",
                SenderEmailId = "<EMAIL>",
                ToEmails = new List<string> { "<EMAIL>" },
                Subject = "Test Subject",
                MessageContent = "Test Content"
            };

            _commandHandlerMock.Setup(x => x.SendMessage(It.IsAny<Message>(), It.IsAny<List<AttachmentRequest>>()))
                              .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.SendMessage(request);

            // Assert
            result.Should().BeOfType<ActionResult<Guid>>();
            var actionResult = result as ActionResult<Guid>;
            var statusResult = actionResult.Result as ObjectResult;
            statusResult.Should().NotBeNull();
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion

        #region GetMessagesByEmail Tests

        [Test]
        public async Task GetMessagesByEmail_ValidEmail_ReturnsOkWithMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            _queryHandlerMock.Setup(x => x.GetMessagesByEmail(email))
                            .ReturnsAsync(_testMessages);

            // Act
            var result = await _controller.GetMessagesByEmail(email);

            // Assert
            result.Should().BeOfType<ActionResult<IEnumerable<Message>>>();
            var actionResult = result as ActionResult<IEnumerable<Message>>;
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var messages = okResult.Value as IEnumerable<Message>;
            messages.Should().NotBeNull();
            messages.Should().HaveCount(2);
        }

        [Test]
        public async Task GetMessagesByEmail_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var email = "<EMAIL>";
            _queryHandlerMock.Setup(x => x.GetMessagesByEmail(email))
                            .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetMessagesByEmail(email);

            // Assert
            result.Should().BeOfType<ActionResult<IEnumerable<Message>>>();
            var actionResult = result as ActionResult<IEnumerable<Message>>;
            var statusResult = actionResult.Result as ObjectResult;
            statusResult.Should().NotBeNull();
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion

        #region GetConversation Tests

        [Test]
        public async Task GetConversation_ValidEmails_ReturnsOkWithMessages()
        {
            // Arrange
            var senderEmail = "<EMAIL>";
            var receiverEmail = "<EMAIL>";
            _queryHandlerMock.Setup(x => x.GetConversation(senderEmail, receiverEmail))
                            .ReturnsAsync(_testMessages);

            // Act
            var result = await _controller.GetConversation(senderEmail, receiverEmail);

            // Assert
            result.Should().BeOfType<ActionResult<IEnumerable<Message>>>();
            var actionResult = result as ActionResult<IEnumerable<Message>>;
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var messages = okResult.Value as IEnumerable<Message>;
            messages.Should().NotBeNull();
            messages.Should().HaveCount(2);
        }

        [Test]
        public async Task GetConversation_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var senderEmail = "<EMAIL>";
            var receiverEmail = "<EMAIL>";
            _queryHandlerMock.Setup(x => x.GetConversation(senderEmail, receiverEmail))
                            .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetConversation(senderEmail, receiverEmail);

            // Assert
            result.Should().BeOfType<ActionResult<IEnumerable<Message>>>();
            var actionResult = result as ActionResult<IEnumerable<Message>>;
            var statusResult = actionResult.Result as ObjectResult;
            statusResult.Should().NotBeNull();
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion

        #region GetMessageById Tests

        [Test]
        public async Task GetMessageById_ExistingId_ReturnsOkWithMessage()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync(_testMessages[0]);

            // Act
            var result = await _controller.GetMessageById(messageId);

            // Assert
            result.Should().BeOfType<ActionResult<Message>>();
            var actionResult = result as ActionResult<Message>;
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var message = okResult.Value as Message;
            message.Should().NotBeNull();
            message.MessageId.Should().Be(messageId);
        }

        [Test]
        public async Task GetMessageById_NonExistingId_ReturnsNotFound()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync((Message)null);

            // Act
            var result = await _controller.GetMessageById(messageId);

            // Assert
            result.Should().BeOfType<ActionResult<Message>>();
            var actionResult = result as ActionResult<Message>;
            var notFoundResult = actionResult.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Test]
        public async Task GetMessageById_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetMessageById(messageId);

            // Assert
            result.Should().BeOfType<ActionResult<Message>>();
            var actionResult = result as ActionResult<Message>;
            var statusResult = actionResult.Result as ObjectResult;
            statusResult.Should().NotBeNull();
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion

        #region MarkMessageAsRead Tests

        [Test]
        public async Task MarkMessageAsRead_ValidId_ReturnsOk()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync(_testMessages[0]);
            _commandHandlerMock.Setup(x => x.MarkMessageAsRead(messageId))
                              .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.MarkMessageAsRead(messageId);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task MarkMessageAsRead_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _commandHandlerMock.Setup(x => x.MarkMessageAsRead(messageId))
                              .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.MarkMessageAsRead(messageId);

            // Assert
            result.Should().BeOfType<ObjectResult>();
            var statusResult = result as ObjectResult;
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion

        #region SoftDeleteMessage Tests

        [Test]
        public async Task SoftDeleteMessage_ValidRequest_ReturnsOk()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var request = new SoftDeleteMessageRequest
            {
                MessageId = messageId,
                DeletedBy = "<EMAIL>",
                Reason = "Test deletion"
            };

            _commandHandlerMock.Setup(x => x.SoftDeleteMessage(messageId, request.DeletedBy, request.Reason))
                              .ReturnsAsync(true);

            // Act
            var result = await _controller.SoftDeleteMessage(messageId, request);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task SoftDeleteMessage_MessageNotFound_ReturnsNotFound()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var request = new SoftDeleteMessageRequest
            {
                MessageId = messageId,
                DeletedBy = "<EMAIL>"
            };

            _commandHandlerMock.Setup(x => x.SoftDeleteMessage(messageId, request.DeletedBy, request.Reason))
                              .ReturnsAsync(false);

            // Act
            var result = await _controller.SoftDeleteMessage(messageId, request);

            // Assert
            result.Should().BeOfType<NotFoundObjectResult>();
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Test]
        public async Task SoftDeleteMessage_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var request = new SoftDeleteMessageRequest
            {
                MessageId = messageId,
                DeletedBy = "<EMAIL>"
            };

            _commandHandlerMock.Setup(x => x.SoftDeleteMessage(messageId, request.DeletedBy, request.Reason))
                              .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.SoftDeleteMessage(messageId, request);

            // Assert
            result.Should().BeOfType<ObjectResult>();
            var statusResult = result as ObjectResult;
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion

        #region ArchiveMessage Tests

        [Test]
        public async Task ArchiveMessage_ValidRequest_ReturnsOk()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var request = new ArchiveMessageRequest
            {
                MessageId = messageId,
                ArchivedBy = "<EMAIL>"
            };

            _commandHandlerMock.Setup(x => x.ArchiveMessage(messageId, request.ArchivedBy))
                              .ReturnsAsync(true);

            // Act
            var result = await _controller.ArchiveMessage(messageId, request);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task ArchiveMessage_MessageNotFound_ReturnsNotFound()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var request = new ArchiveMessageRequest
            {
                MessageId = messageId,
                ArchivedBy = "<EMAIL>"
            };

            _commandHandlerMock.Setup(x => x.ArchiveMessage(messageId, request.ArchivedBy))
                              .ReturnsAsync(false);

            // Act
            var result = await _controller.ArchiveMessage(messageId, request);

            // Assert
            result.Should().BeOfType<NotFoundObjectResult>();
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.StatusCode.Should().Be(404);
        }

        #endregion

        #region GetMessageAttachments Tests

        [Test]
        public async Task GetMessageAttachments_ValidMessageId_ReturnsAttachments()
        {
            // Arrange
            var messageId = _testMessages[1].MessageId;
            _attachmentServiceMock.Setup(x => x.GetMessageAttachmentsAsync(messageId))
                                 .ReturnsAsync(_testAttachments);

            // Act
            var result = await _controller.GetMessageAttachments(messageId);

            // Assert
            result.Should().BeOfType<ActionResult<IEnumerable<MessageAttachment>>>();
            var actionResult = result as ActionResult<IEnumerable<MessageAttachment>>;
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var attachments = okResult.Value as IEnumerable<MessageAttachment>;
            attachments.Should().NotBeNull();
            attachments.Should().HaveCount(1);
        }

        [Test]
        public async Task GetMessageAttachments_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _attachmentServiceMock.Setup(x => x.GetMessageAttachmentsAsync(messageId))
                                 .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetMessageAttachments(messageId);

            // Assert
            result.Should().BeOfType<ActionResult<IEnumerable<MessageAttachment>>>();
            var actionResult = result as ActionResult<IEnumerable<MessageAttachment>>;
            var statusResult = actionResult.Result as ObjectResult;
            statusResult.Should().NotBeNull();
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion

        #region DownloadAttachment Tests

        [Test]
        public async Task DownloadAttachment_ValidId_ReturnsFile()
        {
            // Arrange
            var attachmentId = _testAttachments[0].AttachmentId;
            var fileContent = new byte[] { 1, 2, 3, 4, 5 };

            _attachmentServiceMock.Setup(x => x.GetAttachmentAsync(attachmentId))
                                 .ReturnsAsync(_testAttachments[0]);
            _attachmentServiceMock.Setup(x => x.GetAttachmentContentAsync(attachmentId))
                                 .ReturnsAsync(fileContent);

            // Act
            var result = await _controller.DownloadAttachment(attachmentId);

            // Assert
            result.Should().BeOfType<FileContentResult>();
            var fileResult = result as FileContentResult;
            fileResult.FileContents.Should().BeEquivalentTo(fileContent);
            fileResult.ContentType.Should().Be(_testAttachments[0].ContentType);
            fileResult.FileDownloadName.Should().Be(_testAttachments[0].OriginalFileName);
        }

        [Test]
        public async Task DownloadAttachment_AttachmentNotFound_ReturnsNotFound()
        {
            // Arrange
            var attachmentId = Guid.NewGuid();
            _attachmentServiceMock.Setup(x => x.GetAttachmentAsync(attachmentId))
                                 .ReturnsAsync((MessageAttachment)null);

            // Act
            var result = await _controller.DownloadAttachment(attachmentId);

            // Assert
            result.Should().BeOfType<NotFoundObjectResult>();
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Test]
        public async Task DownloadAttachment_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var attachmentId = Guid.NewGuid();
            _attachmentServiceMock.Setup(x => x.GetAttachmentAsync(attachmentId))
                                 .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DownloadAttachment(attachmentId);

            // Assert
            result.Should().BeOfType<ObjectResult>();
            var statusResult = result as ObjectResult;
            statusResult.StatusCode.Should().Be(500);
        }

        #endregion
    }
}
