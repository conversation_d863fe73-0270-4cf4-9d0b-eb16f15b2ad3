using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using MessageContracts;
using MessageBusinessLayer;
using MessageApi.Controllers;
using Microsoft.AspNetCore.Mvc;
using MessageBusinessLayer.Services;

namespace MessageApi.Tests.Controllers
{
    [TestFixture]
    public class MessageControllerTests
    {
        private Mock<IMessageCommandHandler<Message>> _commandHandlerMock;
        private Mock<IMessageQueryHandler<Message>> _queryHandlerMock;
        private Mock<IAttachmentService> _attachmentServiceMock;
        private Mock<ILogger<MessageController>> _loggerMock;
        private Mock<IStringLocalizer<MessageController>> _localizerMock;
        private Mock<IMessageNotificationService> _notificationServiceMock;
        private MessageController _controller;
        private List<Message> _testMessages;
        private List<MessageAttachment> _testAttachments;

        [SetUp]
        public void Setup()
        {
            _commandHandlerMock = new Mock<IMessageCommandHandler<Message>>();
            _queryHandlerMock = new Mock<IMessageQueryHandler<Message>>();
            _attachmentServiceMock = new Mock<IAttachmentService>();
            _loggerMock = new Mock<ILogger<MessageController>>();
            _localizerMock = new Mock<IStringLocalizer<MessageController>>();
            _notificationServiceMock = new Mock<IMessageNotificationService>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching messages."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["MessageNotFound"]).Returns(new LocalizedString("MessageNotFound", "Message not found."));

            _testMessages = new List<Message>
            {
                new Message 
                { 
                    MessageId = Guid.NewGuid(), 
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject",
                    MessageContent = "Test Content",
                    SentDateTime = DateTime.UtcNow,
                    IsRead = false,
                    IsDeleted = false,
                    IsArchived = false
                },
                new Message 
                { 
                    MessageId = Guid.NewGuid(), 
                    SenderName = "Jane Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "John Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Reply Subject",
                    MessageContent = "Reply Content",
                    SentDateTime = DateTime.UtcNow.AddMinutes(-30),
                    IsRead = true,
                    IsDeleted = false,
                    IsArchived = false
                }
            };

            _testAttachments = new List<MessageAttachment>
            {
                new MessageAttachment
                {
                    AttachmentId = Guid.NewGuid(),
                    MessageId = _testMessages[0].MessageId,
                    FileName = "test.pdf",
                    OriginalFileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileSizeBytes = 1024,
                    BlobFileName = "blob_test.pdf",
                    BlobStorageUrl = "https://test.blob.core.windows.net/attachments/blob_test.pdf",
                    BlobContainerName = "attachments",
                    FileHash = "testhash123",
                    UploadedDateTime = DateTime.UtcNow,
                    IsScanned = true,
                    IsSafe = true
                }
            };

            _controller = new MessageController(
                _commandHandlerMock.Object,
                _queryHandlerMock.Object,
                _attachmentServiceMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _notificationServiceMock.Object
            );
        }

        [Test]
        public async Task SendMessage_ValidRequest_ReturnsOkWithMessageIds()
        {
            // Arrange
            var request = new SendMessageRequest
            {
                SenderName = "John Doe",
                SenderEmailId = "<EMAIL>",
                ToEmails = new List<string> { "<EMAIL>" },
                Subject = "Test Subject",
                MessageContent = "Test Content",
                Attachments = new List<MessageAttachment>()
            };

            var messageId = Guid.NewGuid();
            _commandHandlerMock.Setup(x => x.SendMessage(It.IsAny<Message>(), It.IsAny<List<MessageAttachment>>()))
                              .ReturnsAsync(messageId);
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync(_testMessages[0]);

            // Act
            var result = await _controller.SendMessage(request);

            // Assert
            var actionResult = result as ActionResult<Guid>;
            actionResult.Should().NotBeNull();

            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task GetMessagesByEmail_ValidEmail_ReturnsMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            _queryHandlerMock.Setup(x => x.GetMessagesByEmail(email))
                            .ReturnsAsync(_testMessages);

            // Act
            var result = await _controller.GetMessagesByEmail(email);

            // Assert
            var actionResult = result as ActionResult<IEnumerable<Message>>;
            actionResult.Should().NotBeNull();

            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();

            var messages = okResult.Value as IEnumerable<Message>;
            messages.Should().NotBeNull();
            messages.Should().BeEquivalentTo(_testMessages);
        }

        [Test]
        public async Task GetMessageById_ExistingId_ReturnsMessage()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync(_testMessages[0]);

            // Act
            var result = await _controller.GetMessageById(messageId);

            // Assert
            var actionResult = result as ActionResult<Message>;
            actionResult.Should().NotBeNull();

            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();

            var message = okResult.Value as Message;
            message.Should().NotBeNull();
            message.Should().BeEquivalentTo(_testMessages[0]);
        }

        [Test]
        public async Task GetMessageById_NonExistingId_ReturnsNotFound()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync((Message)null);

            // Act
            var result = await _controller.GetMessageById(messageId);

            // Assert
            var actionResult = result as ActionResult<Message>;
            actionResult.Should().NotBeNull();

            var notFoundResult = actionResult.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Test]
        public async Task MarkMessageAsRead_ValidId_ReturnsOk()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _queryHandlerMock.Setup(x => x.GetMessageById(messageId))
                            .ReturnsAsync(_testMessages[0]);
            _commandHandlerMock.Setup(x => x.MarkMessageAsRead(messageId))
                              .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.MarkMessageAsRead(messageId);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task SoftDeleteMessage_ValidRequest_ReturnsOk()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var request = new SoftDeleteMessageRequest
            {
                MessageId = messageId,
                DeletedBy = "<EMAIL>",
                Reason = "Test deletion"
            };

            _commandHandlerMock.Setup(x => x.SoftDeleteMessage(messageId, request.DeletedBy, request.Reason))
                              .ReturnsAsync(true);

            // Act
            var result = await _controller.SoftDeleteMessage(messageId, request);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.StatusCode.Should().Be(200);
        }

        [Test]
        public async Task GetMessageAttachments_ValidMessageId_ReturnsAttachments()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _attachmentServiceMock.Setup(x => x.GetMessageAttachmentsAsync(messageId))
                                 .ReturnsAsync(_testAttachments);

            // Act
            var result = await _controller.GetMessageAttachments(messageId);

            // Assert
            var actionResult = result as ActionResult<IEnumerable<MessageAttachment>>;
            actionResult.Should().NotBeNull();

            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();

            var attachments = okResult.Value as IEnumerable<MessageAttachment>;
            attachments.Should().NotBeNull();
            attachments.Should().BeEquivalentTo(_testAttachments);
        }

        [Test]
        public async Task DownloadAttachment_ValidId_ReturnsFile()
        {
            // Arrange
            var attachmentId = _testAttachments[0].AttachmentId;
            var fileContent = new byte[] { 1, 2, 3, 4, 5 };
            
            _attachmentServiceMock.Setup(x => x.GetAttachmentAsync(attachmentId))
                                 .ReturnsAsync(_testAttachments[0]);
            _attachmentServiceMock.Setup(x => x.GetAttachmentContentAsync(attachmentId))
                                 .ReturnsAsync(fileContent);

            // Act
            var result = await _controller.DownloadAttachment(attachmentId);

            // Assert
            result.Should().BeOfType<FileContentResult>();
            var fileResult = result as FileContentResult;
            fileResult.FileContents.Should().BeEquivalentTo(fileContent);
            fileResult.ContentType.Should().Be(_testAttachments[0].ContentType);
            fileResult.FileDownloadName.Should().Be(_testAttachments[0].OriginalFileName);
        }

        [Test]
        public async Task GetConversation_ValidEmails_ReturnsConversation()
        {
            // Arrange
            var senderEmail = "<EMAIL>";
            var receiverEmail = "<EMAIL>";
            _queryHandlerMock.Setup(x => x.GetConversation(senderEmail, receiverEmail))
                            .ReturnsAsync(_testMessages);

            // Act
            var result = await _controller.GetConversation(senderEmail, receiverEmail);

            // Assert
            var actionResult = result as ActionResult<IEnumerable<Message>>;
            actionResult.Should().NotBeNull();

            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();

            var messages = okResult.Value as IEnumerable<Message>;
            messages.Should().NotBeNull();
            messages.Should().BeEquivalentTo(_testMessages);
        }

        [Test]
        public async Task ArchiveMessage_ValidRequest_ReturnsOk()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var request = new ArchiveMessageRequest
            {
                MessageId = messageId,
                ArchivedBy = "<EMAIL>"
            };

            _commandHandlerMock.Setup(x => x.ArchiveMessage(messageId, request.ArchivedBy))
                              .ReturnsAsync(true);

            // Act
            var result = await _controller.ArchiveMessage(messageId, request);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.StatusCode.Should().Be(200);
        }
    }
}
