using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace TeyaUIModels.Model
{
    public class AttachmentModel : IModel
    {
        [Key]
        public Guid AttachmentId { get; set; }

        [Required]
        public Guid MessageId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ContentType { get; set; } = string.Empty;

        [Required]
        public long FileSizeBytes { get; set; }

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        // Optional: Store file content as base64 for small files
        public string? FileContentBase64 { get; set; }

        // File metadata
        public DateTime UploadedDateTime { get; set; } = DateTime.UtcNow;
        public string? FileHash { get; set; } // For integrity checking

        // Azure Blob Storage specific (optional)
        public string? BlobStorageUrl { get; set; }
        public string? BlobContainerName { get; set; }
        public string? BlobFileName { get; set; }

        // Security and validation
        public bool IsScanned { get; set; } = false;
        public bool IsSafe { get; set; } = true;
        public string? ScanResult { get; set; }

        // Navigation property
        [ForeignKey("MessageId")]
        [JsonIgnore] // Prevent circular reference during JSON serialization
        public virtual MessageModel? Message { get; set; }

        // UI Helper Methods
        public string GetFileSizeFormatted()
        {
            if (FileSizeBytes < 1024)
                return $"{FileSizeBytes} B";
            if (FileSizeBytes < 1024 * 1024)
                return $"{FileSizeBytes / 1024:F1} KB";
            if (FileSizeBytes < 1024 * 1024 * 1024)
                return $"{FileSizeBytes / (1024 * 1024):F1} MB";
            return $"{FileSizeBytes / (1024 * 1024 * 1024):F1} GB";
        }

        public string GetFileIcon()
        {
            return ContentType?.ToLower() switch
            {
                var ct when ct.StartsWith("image/") => "fas fa-image",
                var ct when ct.StartsWith("video/") => "fas fa-video",
                var ct when ct.StartsWith("audio/") => "fas fa-music",
                "application/pdf" => "fas fa-file-pdf",
                var ct when ct.Contains("word") => "fas fa-file-word",
                var ct when ct.Contains("excel") || ct.Contains("spreadsheet") => "fas fa-file-excel",
                var ct when ct.Contains("powerpoint") || ct.Contains("presentation") => "fas fa-file-powerpoint",
                "text/plain" => "fas fa-file-alt",
                var ct when ct.Contains("zip") || ct.Contains("rar") || ct.Contains("7z") => "fas fa-file-archive",
                _ => "fas fa-file"
            };
        }
    }

    public static class AttachmentConstants
    {
        // Maximum file size (10MB)
        public const long MaxFileSizeBytes = 10 * 1024 * 1024;

        // Allowed file types
        public static readonly string[] AllowedContentTypes = {
            // Images
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp",

            // Documents
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "text/csv",
            "text/html",
            "text/xml",
            "application/json",
            "application/xml",
            "application/javascript",

            // Archives
            "application/zip",
            "application/x-rar-compressed",
            "application/x-7z-compressed",

            // Audio
            "audio/mpeg", "audio/wav", "audio/ogg",

            // Video
            "video/mp4", "video/avi", "video/mov", "video/wmv"
        };

        // Dangerous file extensions to block
        public static readonly string[] BlockedExtensions = {
            ".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js", ".jar", ".msi"
        };
    }
}
