﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using Sprache;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class OrganizationRepository : GenericRepository<Organization>, IOrganizationRepository
    {
        private readonly AccountDatabaseContext _context;

        public OrganizationRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }
        public async Task<List<Organization>> GetOrganizationsByNameAsync(string name)
        {
            var result = await _context.Organization
                .Where(organization => organization.OrganizationName.Contains(name, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }

        public async Task<bool> FindByNameAsync(string organizationName)
        {
            if (string.IsNullOrEmpty(organizationName))
            {
                return false;
            }

            bool result = await _context.Organization.AnyAsync(o => o.OrganizationName == organizationName);
            return result;
        }

    }
}
