﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class AssessmentsCommandHandler:IAssessmentsCommandHandler<AssessmentsData>
    {
        private readonly IUnitOfWork _unitOfWork;

        public AssessmentsCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddAssessments(List<AssessmentsData> _Assessments)
        {
            await _unitOfWork.AssessmentsRepository.AddAsync(_Assessments);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAssessments(AssessmentsData _Assessments)
        {
            await _unitOfWork.AssessmentsRepository.UpdateAsync(_Assessments);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteAssessmentsById(Guid id)
        {
            await _unitOfWork.AssessmentsRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteAssessmentsByEntity(AssessmentsData _Assessments)
        {
            await _unitOfWork.AssessmentsRepository.DeleteByEntityAsync(_Assessments);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAssessmentsList(List<AssessmentsData> _Assessments)
        {
            await _unitOfWork.AssessmentsRepository.UpdateRangeAsync(_Assessments);
            await _unitOfWork.SaveAsync();
        }
    }
}
