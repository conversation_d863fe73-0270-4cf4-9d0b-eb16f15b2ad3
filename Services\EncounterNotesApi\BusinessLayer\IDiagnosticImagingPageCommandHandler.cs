﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IDiagnosticImagingPageCommandHandler<TText>
    {
        Task DeleteDiagnosticImagingById(Guid id);
        Task<bool> UpdateDiagnosticImagingList(List<DiagnosticImagingDTO> diagnosticImagingList);
        Task AddDiagnosticImaging(List<TText> diagnosticImagingList);
        Task UpdateDiagnosticImaging(DiagnosticImagingDTO diagnosticImaging);
    }
}
