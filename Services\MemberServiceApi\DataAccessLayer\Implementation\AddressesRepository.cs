﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class AddressesRepository : GenericRepository<Address>, IAddressesRepository
    {
        private readonly AccountDatabaseContext _context;

        public AddressesRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }


    }
}
