﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;
using MemberServiceBusinessLayer.QueryHandler;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RolesController : ControllerBase
    {
        private readonly IRolesQueryHandler<Role> _rolesQueryHandler;
        private readonly IRolesCommandHandler<Role> _rolesCommandHandler;
        private readonly ILogger<RolesController> _logger;
        private readonly IStringLocalizer<RolesController> _localizer;

        public RolesController(
            IRolesQueryHandler<Role> rolesQueryHandler,
            IRolesCommandHandler<Role> rolesCommandHandler,
            ILogger<RolesController> logger,
            IStringLocalizer<RolesController> localizer)
        {
            _rolesQueryHandler = rolesQueryHandler ?? throw new ArgumentNullException(nameof(rolesQueryHandler));
            _rolesCommandHandler = rolesCommandHandler ?? throw new ArgumentNullException(nameof(rolesCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        /// <summary>
        /// Gets a role by its ID.
        /// </summary>
        /// <param name="id">The ID of the role.</param>
        /// <returns>An IActionResult containing the role or a not found result.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetRoleById(Guid id)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingRoleWithID"], id);
            var role = await _rolesQueryHandler.GetRoleByIdAsync(id);
            if (role == null)
            {
                _logger.LogWarning(_localizer["RoleNotFound"], id);
                response = NotFound(_localizer["RoleNotFoundMessage"]);
            }
            else
            {
                _logger.LogInformation(_localizer["RoleFetchedSuccessfully"], id);
                response = Ok(role);
            }
            return response;
        }

        /// <summary>
        /// Adds a new role.
        /// </summary>
        /// <param name="role">The role to add.</param>
        /// <returns>An IActionResult indicating the result of the operation.</returns>
        [HttpPost]
        public async Task<IActionResult> AddRole([FromBody] Role role)
        {
            IActionResult response;
            if (role == null)
            {
                _logger.LogWarning(_localizer["InvalidRoleData"]);
                response = BadRequest(_localizer["RoleInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewRole"]);
                    await _rolesCommandHandler.AddRoleAsync(new List<Role> { role });
                    _logger.LogInformation(_localizer["RoleAddedSuccessfully"], role.RoleId);
                    response = CreatedAtAction(nameof(GetRoleById), new { id = role.RoleId }, role);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingRole"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Updates an existing role.
        /// </summary>
        /// <param name="id">The ID of the role to update.</param>
        /// <param name="role">The updated role data.</param>
        /// <returns>An IActionResult indicating the result of the operation.</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRole(Guid id, [FromBody] Role role)
        {
            IActionResult response;
            if (role == null || id != role.RoleId)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["RoleInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingRoleWithID"], id);
                    await _rolesCommandHandler.UpdateRoleAsync(role);
                    _logger.LogInformation(_localizer["RoleUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["RoleNotFoundForUpdate"], id);
                    response = NotFound(_localizer["RoleNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingRole"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Deletes a role by its ID.
        /// </summary>
        /// <param name="id">The ID of the role to delete.</param>
        /// <returns>An IActionResult indicating the result of the operation.</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRole(Guid id)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingRoleWithID"], id);
                await _rolesCommandHandler.DeleteRoleAsync(id);
                _logger.LogInformation(_localizer["RoleDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["RoleNotFoundForDeletion"], id);
                response = NotFound(_localizer["RoleNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingRole"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Searches for roles by name.
        /// </summary>
        /// <param name="name">The name of the role to search for.</param>
        /// <returns>An IActionResult containing the roles or a not found result.</returns>
        [HttpGet("search")]
        public async Task<IActionResult> GetRoleByName([FromQuery] string name)
        {
            IActionResult response;
            if (string.IsNullOrWhiteSpace(name))
            {
                _logger.LogWarning(_localizer["RoleNameRequired"]);
                response = BadRequest(_localizer["RoleNameRequiredMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["SearchingRolesByName"], name);
                    var roles = await _rolesQueryHandler.GetRolesByNameAsync(name);

                    if (roles == null || roles.Count == 0)
                    {
                        _logger.LogWarning(_localizer["NoRolesFoundByName"], name);
                        response = NotFound(_localizer["RolesNotFoundMessage"]);
                    }
                    else
                    {
                        _logger.LogInformation(_localizer["RolesFetchedSuccessfully"], name);
                        response = Ok(roles);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorSearchingRoles"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Gets all roles.
        /// </summary>
        /// <returns>An IActionResult containing all roles or a not found result.</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllRoles()
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllRoles"]);
            try
            {
                var roles = await _rolesQueryHandler.GetAllRolesAsync();

                if (roles == null || roles.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoRolesFound"]);
                    response = NotFound(_localizer["RolesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllRolesFetchedSuccessfully"]);
                    response = Ok(roles);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingRoles"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Gets a list of roles by its Organization ID.
        /// Gets a list of roles by its Organization ID.
        /// </summary>
        /// <param name="id">The Organization ID of the role.</param>
        /// <returns>An IActionResult containing the role or a not found result.</returns>
        [HttpGet("Org/{id}")]
        public async Task<IActionResult> GetroleByOrgId(Guid id)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingRoleWithID"], id);
            var role = await _rolesQueryHandler.GetRolesByOrgAsync(id);

            if (role == null)
            {
                _logger.LogWarning(_localizer["RoleNotFoundWithID"], id);
                response = NotFound(_localizer["RoleNotFound"]);
            }
            else
            {
                _logger.LogInformation(_localizer["roleFetchedSuccessfully"], id);
                response = Ok(role);
            }

            return response;
        }
    }
}