﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IImmunizationService
    {
        Task<List<ImmunizationData>> GetImmunizationsByIdAsync(Guid id);
        Task AddImmunizationAsync(List<ImmunizationData> immunization);
        Task DeleteImmunizationAsync(ImmunizationData Data);
        Task UpdateImmunizationAsync(ImmunizationData Immunizations);
        Task<List<ImmunizationData>> GetImmunizationByIdAsyncAndIsActive(Guid id);
        Task UpdateImmunizationListAsync(List<ImmunizationData> immunizationdata);
    }
}
