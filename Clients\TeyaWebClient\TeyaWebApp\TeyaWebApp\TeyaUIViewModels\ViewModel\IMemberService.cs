﻿using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public interface IMemberService
    {
        Task<Member> RegisterMembersAsync(List<Member> members);
        Task<List<Member>> GetMembersForProductAsync(Guid productId);
        Task DeleteMemberByIdAsync(Guid id);
        Task UpdateMemberByIdAsync(Guid memberId, Member member);
        Task<List<string>> GetProviderlistAsync(Guid orgId);
        Task<List<Member>> GetAllMembersAsync();
        Task<Member> GetMemberByIdAsync(Guid memberId);
        Task<List<Member>> SearchMembersAsync(string query);
        Task<string> RegisterMembersContentAsync(List<Member> members);
        Task<List<ProviderPatient>> GetProviderPatientByOrganizationIdAsync(Guid OrganizationId);
        Task<List<ProviderPatient>> GetPatientsByOrganizationIdAsync(Guid OrganizationId);
        Task<bool> HasProductAccess(Guid memberId, Guid productId);
        Task<bool> SearchMembersEmailAsync(string query);
    }
}
