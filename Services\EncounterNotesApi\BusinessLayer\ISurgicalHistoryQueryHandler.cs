﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface ISurgicalHistoryQueryHandler<TText>
    {
        Task<IEnumerable<SurgicalHistory>> GetSurgeryByIdAndIsActive(Guid id);
        Task<IEnumerable<SurgicalHistory>> GetAllSurgeriesById(Guid id);
    }
}