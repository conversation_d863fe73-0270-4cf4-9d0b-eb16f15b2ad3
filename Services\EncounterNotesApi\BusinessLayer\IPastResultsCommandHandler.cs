﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IPastResultsCommandHandler<TText>
    {
        Task DeleteResultsByEntity(PastResults pastresults);
        Task DeleteResultById(Guid id);
        Task AddResult(List<TText> texts);
        Task UpdateResult(PastResults pastResult);
        Task UpdatePastList(List<PastResults> past);
    }
}