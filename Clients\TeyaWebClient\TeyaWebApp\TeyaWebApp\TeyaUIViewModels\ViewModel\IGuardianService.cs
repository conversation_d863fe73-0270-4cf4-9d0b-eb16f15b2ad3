﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IGuardianService
    {
        Task<Guardian?> GetGuardianByIdAsync(Guid id);

        Task<bool> AddG<PERSON>ianAsync(Guardian Guardian);

        Task<bool> UpdateGuardianAsync(Guid id, Guardian guardian);

        Task<bool> DeleteGuardianAsync(Guid id);

        Task<List<Guardian>?> GetGuardianByNameAsync(string name);

        Task<List<Guardian>?> GetAllGuardianAsync();
    }
}
