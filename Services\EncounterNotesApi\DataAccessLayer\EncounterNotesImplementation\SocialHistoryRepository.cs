﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class SocialHistoryRepository : GenericRepository<PatientSocialHistory>, ISocialHistoryRepository
    {
        private readonly RecordDatabaseContext _context;
        public SocialHistoryRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer)
            : base(context, localizer)
        {
            _context = context;
        }
    }
}
