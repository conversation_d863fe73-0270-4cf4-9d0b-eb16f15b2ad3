﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TherapeuticInterventionsController : ControllerBase
    {
        private readonly ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData> _TherapeuticInterventionsDataHandler;
        private readonly ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData> _TherapeuticInterventionsQueryHandler;
        private readonly ILogger<TherapeuticInterventionsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public TherapeuticInterventionsController(
            ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData> medicalDataHandler,
            ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData> medicalQueryHandler,
            ILogger<TherapeuticInterventionsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _TherapeuticInterventionsDataHandler = medicalDataHandler;
            _TherapeuticInterventionsQueryHandler = medicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<TherapeuticInterventionsData>>> GetAllById(Guid id)
        {
            ActionResult<IEnumerable<TherapeuticInterventionsData>> result;

            try
            {
                var data = await _TherapeuticInterventionsQueryHandler.GetAllTherapeuticInterventionsById(id);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpGet("{id:guid}/IsActive")]
        public async Task<ActionResult<IEnumerable<TherapeuticInterventionsData>>> GetAllByIdAndIsActive(Guid id)
        {
            ActionResult<IEnumerable<TherapeuticInterventionsData>> result;

            try
            {
                var diagnosis = await _TherapeuticInterventionsQueryHandler.GetTherapeuticInterventionsByIdAndIsActive(id);
                result = diagnosis != null ? Ok(diagnosis) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpPost]
        [Route("AddTherapeuticInterventions")]
        public async Task<IActionResult> AddTherapeuticInterventions([FromBody] List<TherapeuticInterventionsData> _TherapeuticInterventions)
        {
            IActionResult result;

            if (_TherapeuticInterventions == null || _TherapeuticInterventions.Count == 0)
            {
                result = BadRequest(_localizer["NoLicense"]);
            }
            else
            {
                try
                {
                    await _TherapeuticInterventionsDataHandler.AddTherapeuticInterventions(_TherapeuticInterventions);
                    result = Ok(_localizer["SuccessfulRegistration"]);
                }
                catch (SqlException)
                {
                    result = StatusCode(500, _localizer["DatabaseError"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(500);
                }
            }

            return result;
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] TherapeuticInterventionsData _TherapeuticInterventions)
        {
            IActionResult result;

            if (_TherapeuticInterventions == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _TherapeuticInterventionsDataHandler.DeleteTherapeuticInterventionsByEntity(_TherapeuticInterventions);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(500, _localizer["DeleteLogError"]);
                }
            }

            return result;
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateTherapeuticInterventionsById(Guid id, [FromBody] TherapeuticInterventionsData _TherapeuticInterventions)
        {
            IActionResult result;

            if (_TherapeuticInterventions == null || _TherapeuticInterventions.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _TherapeuticInterventionsDataHandler.UpdateTherapeuticInterventions(_TherapeuticInterventions);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(500, _localizer["UpdateLogError"]);
                }
            }

            return result;
        }

        [HttpPut]
        [Route("UpdateTherapeuticInterventionsList")]
        public async Task<IActionResult> UpdateTherapeuticInterventionsList([FromBody] List<TherapeuticInterventionsData> _TherapeuticInterventions)
        {
            IActionResult result;

            try
            {
                await _TherapeuticInterventionsDataHandler.UpdateTherapeuticInterventionsList(_TherapeuticInterventions);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(500, _localizer["UpdateLogError"]);
            }

            return result;
        }
    }
}