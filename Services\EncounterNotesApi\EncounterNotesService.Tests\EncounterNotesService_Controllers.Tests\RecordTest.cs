﻿using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Tests.EncounterNotesService_Controllers.Tests
{
    [TestFixture]
    public class RecordsControllerTests
    {
        private Mock<IRecordCommandHandler<Record>> _commandHandlerMock;
        private Mock<IRecordQueryHandler<Record>> _queryHandlerMock;
        private Mock<ILogger<RecordsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordsController _controller;
        private List<Record> _testRecords;
        private object _recordDataHandlerMock;

        [SetUp]
        public void Setup()
        {
            _commandHandlerMock = new Mock<IRecordCommandHandler<Record>>();
            _queryHandlerMock = new Mock<IRecordQueryHandler<Record>>();
            _loggerMock = new Mock<ILogger<RecordsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching records."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));

            _testRecords = new List<Record>
            {
                new Record { Id = Guid.NewGuid(), PatientName = "John Doe" },
                new Record { Id = Guid.NewGuid(), PatientName = "Jane Doe" }
            };

            _controller = new RecordsController(
                _commandHandlerMock.Object,
                _queryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
                );
        }

        [Test]
        public async Task Get_ReturnsListOfRecords()
        {
            // Arrange
            _queryHandlerMock.Setup(x => x.GetRecord()).ReturnsAsync(_testRecords);

            // Act
            var result = await _controller.Get();

            // Assert
            // Check if the result is an ActionResult wrapping OkObjectResult
            var actionResult = result as ActionResult<IEnumerable<Record>>;
            actionResult.Should().NotBeNull();

            // Extract the actual result inside OkObjectResult
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();

            // Verify the value of OkObjectResult
            var records = okResult.Value as IEnumerable<Record>;
            records.Should().NotBeNull();
            records.Should().BeEquivalentTo(_testRecords);
        }


        [Test]
        public async Task GetById_ExistingId_ReturnsRecord()
        {
            // Arrange
            var testId = Guid.NewGuid();
            var testRecord = new Record
            {
                Id = testId,
                PatientName = "John Doe",
                DateTime = DateTime.UtcNow,
                //Summary = "Test summary"
            };

            _queryHandlerMock.Setup(x => x.GetRecordById(testId)).ReturnsAsync(testRecord);

            // Act
            var result = await _controller.GetById(testId);

            // Assert
            // Check if the result is an ActionResult wrapping OkObjectResult
            var actionResult = result as ActionResult<Record>;
            actionResult.Should().NotBeNull();

            // Extract the actual result inside OkObjectResult
            var okResult = actionResult.Result as OkObjectResult;
            okResult.Should().NotBeNull();

            // Verify the value of OkObjectResult
            var record = okResult.Value as Record;
            record.Should().NotBeNull();
            record.Should().BeEquivalentTo(testRecord);
        }




        [Test]
        public async Task UpdateById_ValidRecord_ExecutesSuccessfully()
        {
            // Arrange
            var record = _testRecords[0];

            // Act
            var result = await _controller.UpdateById(record.Id, record);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
        }

        [Test]
        public async Task DeleteById_ExistingId_DeletesSuccessfully()
        {
            // Arrange
            var recordId = _testRecords[0].Id;

            // Act
            var result = await _controller.DeleteById(recordId);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
        }

        [Test]
        public async Task DeleteByEntity_ValidRecord_DeletesSuccessfully()
        {
            // Arrange
            var recordToDelete = _testRecords[0];

            // Setup the DeleteRecordByEntity method to return a successful result
            _commandHandlerMock.Setup(x => x.DeleteRecordByEntity(recordToDelete)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteByEntity(recordToDelete);

            // Assert
            // Verify that the result is an OkObjectResult
            result.Should().BeOfType<OkObjectResult>();

            // Verify that the correct localized message is returned
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);
        }

        [Test]
        public async Task EncounterNote_ValidInput_AddsRecordsSuccessfully()
        {
            // Arrange
            var recordDtos = new List<RecordDTO>
            {
                new RecordDTO { Id = Guid.NewGuid(), PatientName = "John Doe" }
            };

            // Act
            var result = await _controller.EncounterNote(recordDtos);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
        }
    }
}