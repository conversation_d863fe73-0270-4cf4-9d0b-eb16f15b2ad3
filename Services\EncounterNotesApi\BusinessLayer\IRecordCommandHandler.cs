﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IRecordCommandHandler<TText>
    {
        Task DeleteRecordByEntity(Record record);
        Task DeleteRecordById(Guid id);
        Task AddRecord(List<TText> texts);
        Task UpdateRecord(Record record);
    }
}
