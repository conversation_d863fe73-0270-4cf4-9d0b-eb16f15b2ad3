﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDiagnosticImagingService
    {
        Task CreateDiagnosticImagingAsync(List<DiagnosticImage> Tasks);
        Task<List<DiagnosticImage>> GetDiagnosticImagingAsync(Guid id);
        Task UpdateDiagnosticImagingList(List<DiagnosticImage> diagnosticImaging);
        Task<List<DiagnosticImage>> GetDiagnosticImagingByIdAsyncAndIsActive(Guid id);
        Task DeleteDiagnosticImagingAsync(Guid taskId);
        Task UpdateDiagnosticImagingAsync(DiagnosticImage diagnosticImaging);
    }
}
