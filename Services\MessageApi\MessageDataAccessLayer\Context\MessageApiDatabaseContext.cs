﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using MessageContracts;

namespace DataAccessLayer.Context
{
    public class MessageApiDatabaseContext : DbContext
    {
        private readonly IStringLocalizer<MessageApiDatabaseContext> _localizer;
        private readonly ILogger<MessageApiDatabaseContext> _logger;

        public MessageApiDatabaseContext(DbContextOptions<MessageApiDatabaseContext> options,
                                         IStringLocalizer<MessageApiDatabaseContext> localizer,
                                         ILogger<MessageApiDatabaseContext> logger)
            : base(options)
        {
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger;
        }

        public DbSet<Message> Messages { get; set; }
        public DbSet<MessageAttachment> MessageAttachments { get; set; }
        public DbSet<User> Users { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            try
            {
                // Configure Message entity
                modelBuilder.Entity<Message>(entity =>
                {
                    entity.ToTable("Messages", "MessageService");
                    entity.HasKey(e => e.MessageId);
                    entity.Property(e => e.MessageId).HasDefaultValueSql("NEWID()");

                    entity.Property(e => e.SenderName).IsRequired().HasMaxLength(100);
                    entity.Property(e => e.SenderEmailId).IsRequired().HasMaxLength(255);
                    entity.Property(e => e.ReceiverName).IsRequired().HasMaxLength(100);
                    entity.Property(e => e.ReceiverEmailId).IsRequired().HasMaxLength(255);
                    entity.Property(e => e.Subject).HasMaxLength(200);
                    entity.Property(e => e.MessageContent).IsRequired().HasMaxLength(2000);
                    entity.Property(e => e.AzureMessageId).HasMaxLength(255);
                    entity.Property(e => e.ConversationId).HasMaxLength(255);

                    // Timestamp fields - specify column type to match database
                    entity.Property(e => e.CreatedAt)
                          .HasColumnType("datetime")
                          .HasDefaultValueSql("GETUTCDATE()");
                    entity.Property(e => e.UpdatedAt)
                          .HasColumnType("datetime")
                          .HasDefaultValueSql("GETUTCDATE()");

                    // Soft Delete fields
                    entity.Property(e => e.IsDeleted).HasDefaultValue(false);
                    entity.Property(e => e.DeletedBy).HasMaxLength(255);

                    // Archive fields
                    entity.Property(e => e.IsArchived).HasDefaultValue(false);
                    entity.Property(e => e.ArchivedBy).HasMaxLength(255);

                    entity.HasIndex(e => e.SenderEmailId);
                    entity.HasIndex(e => e.ReceiverEmailId);
                    entity.HasIndex(e => e.SentDateTime);
                    entity.HasIndex(e => e.ConversationId);
                    entity.HasIndex(e => e.IsDeleted);
                    entity.HasIndex(e => e.IsArchived);

                    // Global query filter to exclude soft-deleted messages by default
                    entity.HasQueryFilter(e => !e.IsDeleted);
                });

                // Configure MessageAttachment entity
                modelBuilder.Entity<MessageAttachment>(entity =>
                {
                    entity.ToTable("MessageAttachments", "MessageService");
                    entity.HasKey(e => e.AttachmentId);
                  

                    entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                    entity.Property(e => e.OriginalFileName).IsRequired().HasMaxLength(255);
                    entity.Property(e => e.ContentType).IsRequired().HasMaxLength(100);
                    entity.Property(e => e.FileSizeBytes).IsRequired();

                    entity.Property(e => e.BlobFileName).IsRequired().HasMaxLength(500);
                    entity.Property(e => e.BlobStorageUrl).IsRequired().HasMaxLength(1000);
                    entity.Property(e => e.BlobContainerName).IsRequired().HasMaxLength(100);

                    // File metadata
                    entity.Property(e => e.FileHash).HasMaxLength(100);
                    entity.Property(e => e.UploadedDateTime).IsRequired();

                    // Legacy field (for database compatibility)
                    entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);

                    // Security and validation
                    entity.Property(e => e.IsScanned).IsRequired();
                    entity.Property(e => e.IsSafe).IsRequired();
                    entity.Property(e => e.ScanResult).HasMaxLength(255);

                    // Indexes for performance
                    entity.HasIndex(e => e.MessageId);
                    entity.HasIndex(e => e.FileName);
                    entity.HasIndex(e => e.ContentType);
                    entity.HasIndex(e => e.UploadedDateTime);
                    entity.HasIndex(e => e.BlobFileName);

                    // Foreign key relationship
                    entity.HasOne(e => e.Message)
                          .WithMany(m => m.Attachments)
                          .HasForeignKey(e => e.MessageId)
                          .OnDelete(DeleteBehavior.Cascade);
                });

                // Configure User entity
                modelBuilder.Entity<User>(entity =>
                {
                    entity.ToTable("User", "MessageService");
                    entity.HasKey(e => e.UserId);
                    entity.Property(e => e.UserId).HasDefaultValueSql("NEWID()");

                    entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                    entity.Property(e => e.EmailId).IsRequired().HasMaxLength(255);
                    entity.Property(e => e.AcsUserId).HasMaxLength(255);
                    entity.Property(e => e.PreferredLanguage).HasMaxLength(50);
                    entity.Property(e => e.TimeZone).HasMaxLength(100);

                    entity.HasIndex(e => e.EmailId).IsUnique();
                    entity.HasIndex(e => e.AcsUserId);
                    entity.HasIndex(e => e.CreatedDate);
                    entity.HasIndex(e => e.IsActive);
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DatabaseError"]);
            }
        }
    }
}
