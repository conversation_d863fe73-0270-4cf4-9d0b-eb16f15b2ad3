﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class VitalCommandHandler : IVitalCommandHandler<Vitals>
    {
        private readonly IUnitOfWork _unitOfWork;
        public VitalCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddVital(List<Vitals> Vitals)
        {
            await _unitOfWork.VitalRepository.AddAsync(Vitals);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateVital(Vitals vital)
        {
            await _unitOfWork.VitalRepository.UpdateAsync(vital);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateVitalList(List<Vitals> vitals)
        {
            await _unitOfWork.VitalRepository.UpdateRangeAsync(vitals);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteVitalById(Guid id)
        {
            await _unitOfWork.VitalRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteVitalByEntity(Vitals Vital)
        {
            await _unitOfWork.VitalRepository.DeleteByEntityAsync(Vital);
            await _unitOfWork.SaveAsync();
        }
    }
}
