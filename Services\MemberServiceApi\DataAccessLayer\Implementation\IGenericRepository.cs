﻿using Contracts;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IGenericRepository<T> where T : class
    {
        // Retrieve all entities
        Task<IEnumerable<T>> GetAllAsync();

        // Retrieve an entity by its primary key
        Task<T> GetByIdAsync(Guid id);

        // Add multiple new entities
        Task AddAsync(IEnumerable<T> entities);

        // Update an existing entity
        Task UpdateAsync(T entity);

        // Delete an existing entity
        Task DeleteByEntityAsync(T entity);

        // Delete an existing id
        Task DeleteByIdAsync(Guid id);

        Task UpdateRangeAsync(IEnumerable<T> entities);

    }
}
