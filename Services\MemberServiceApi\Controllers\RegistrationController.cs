﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Contracts;
using System.Resources;
using System.Reflection;
using System.Globalization;
using Microsoft.Extensions.Localization;
using Microsoft.Identity.Web.Resource;
using Microsoft.AspNetCore.Authorization;
using MemberServiceBusinessLayer;
using Microsoft.EntityFrameworkCore;
using MemberServiceBusinessLayer.QueryHandler;
using System.Text.Json;
using System.Text;
using System.Text.Json.Serialization;

namespace MemberServiceApi.Controllers
{
    //[RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    //[Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class RegistrationController : ControllerBase
    {
        private readonly IMemberCommandHandler<Member> _memberDataHandler;
        private readonly IMemberQueryHandler<Member> _memberQueryHandler;
        private readonly ICheckAccessQueryHandler<ProductAccess> _checkAccessQueryHandler;
        private readonly ILogger<RegistrationController> _logger;
        private readonly IStringLocalizer<RegistrationController> _localizer;
        private readonly IOrganizationsQueryHandler<Organization> _organizationQueryHandler;

        /// <summary>
        /// Initializes a new instance of the <see cref="RegistrationController"/> class.
        /// </summary>
        public RegistrationController(IMemberCommandHandler<Member> dataHandler,
                                      IMemberQueryHandler<Member> queryHandler,
                                      ILogger<RegistrationController> logger,
                                      IStringLocalizer<RegistrationController> localizer,
                                      ICheckAccessQueryHandler<ProductAccess> checkAccessQueryHandler,
                                      IOrganizationsQueryHandler<Organization> organizationQueryHandler)
        {
            _memberDataHandler = dataHandler;
            _logger = logger;
            _memberQueryHandler = queryHandler;
            _localizer = localizer;
            _checkAccessQueryHandler = checkAccessQueryHandler;
            _organizationQueryHandler = organizationQueryHandler;
        }

        /// <summary>
        /// Retrieves all members.
        /// </summary>
        /// <returns>A list of members.</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Member>>> Get()
        {
            try
            {
                var members = await _memberQueryHandler.GetMember();
                return Ok(members);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Retrieves a member by their unique identifier.
        /// </summary>
        /// <param name="id">The unique ID of the member.</param>
        /// <returns>The requested member.</returns>
        [HttpGet("{id:guid}")]
        public async Task<ActionResult<Member>> GetById(Guid id)
        {
            try
            {
                var member = await _memberQueryHandler.GetMemberById(id);
                return Ok(member);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Get Patients belonging to a specific Organization
        /// </summary>
        /// <param name="organizationId"></param>
        /// <returns></returns>
        [HttpGet("organization/patient/{organizationID:guid}")]
        public async Task<IActionResult> GetPatientByOrganizationId(Guid organizationID)
        {
            IActionResult result;
            try
            {
                var providerPatientList = await _memberQueryHandler.GetPatientsByOrganizationId(organizationID);

                result = providerPatientList != null && providerPatientList.Any()
                    ? Ok(providerPatientList)
                    : NotFound(_localizer["NoPatientsFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{ErrorMessage}: {ExceptionMessage}", _localizer["GetLogError"], ex.Message);
                result = StatusCode(500, new { Error = _localizer["InternalServerError"], Details = ex.Message });
            }

            return result;
        }


        /// <summary>
        /// Get ProviderPatient belonging to a specific Organization
        /// </summary>
        /// <param name="organizationId"></param>
        /// <returns></returns>
        [HttpGet("organization/provider/{organizationID:guid}")]
        public async Task<IActionResult> GetProviderByOrganizationId(Guid organizationID)
        {
            IActionResult result;
            try
            {
                var providerPatientList = await _memberQueryHandler.GetProviderPatientByOrganizationId(organizationID);

                result = providerPatientList != null && providerPatientList.Any()
                    ? Ok(providerPatientList)
                    : NotFound(_localizer["NoProvidersFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{ErrorMessage}: {ExceptionMessage}", _localizer["GetLogError"], ex.Message);
                result = StatusCode(500, new { Error = _localizer["InternalServerError"], Details = ex.Message });
            }

            return result;
        }

        /// <summary>
        /// Retrieves patient by id
        /// </summary>
        /// <param name="id">The unique ID of the member.</param>
        /// <returns>The requested member.</returns>
        [HttpGet("/Patient/{id:guid}")]
        public async Task<ActionResult<Patient>> GetPatientById(Guid id)
        {
            try
            {
                var Patient = await _memberQueryHandler.GetPatientdatabyid(id);
                return Ok(Patient);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Updates a member's details.
        /// </summary>
        /// <param name="id">The ID of the member to update.</param>
        /// <param name="member">The updated member data.</param>
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] Member member)
        {
            if (member == null || member.Id != id)
            {
                return BadRequest(_localizer["InvalidMember"]);
            }

            try
            {
                await _memberDataHandler.UpdateMember(member);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Deletes a member by their ID.
        /// </summary>
        /// <param name="id">The unique ID of the member to delete.</param>
        [HttpDelete("{id:guid}")]
        public async Task<IActionResult> DeleteById(Guid id)
        {
            try
            {
                await _memberDataHandler.DeleteMemberById(id);
                return Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Deletes a member using the provided entity.
        /// </summary>
        /// <param name="member">The member entity to delete.</param>
        [HttpDelete("(entity)")]
        public async Task<IActionResult> DeleteByEntity([FromBody] Member member)
        {
            try
            {
                await _memberDataHandler.DeleteMemberByEntity(member);
                return Ok(_localizer["DeleteSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }


        /// <summary>
        /// Searches for members based on a search term.
        /// </summary>
        /// <param name="searchTerm">The term used to search for members.</param>

        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<Member>>> SearchMembers([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest(_localizer["InvalidSearchTerm"]);
            }

            try
            {
                var matchingMembers = await _memberQueryHandler.SearchMembersAsync(searchTerm);
                if (matchingMembers == null || !matchingMembers.Any())
                {
                    return NotFound(_localizer["NoMatchingMembers"]);
                }

                return Ok(matchingMembers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["SearchLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Registers a list of new members.
        /// </summary>
        /// <param name="registrations">A list of member registrations.</param>
        [HttpPost]
        [Route("registration")]
        public async Task<IActionResult> Registration([FromBody] List<MemberDto> registrations)
        {
            if (registrations == null || registrations.Count == 0)
            {
                return BadRequest(_localizer["NoMember"]);
            }

            try
            {
                var duplicateEmails = await _memberDataHandler.RegisterMembersAsync(registrations);

                if (duplicateEmails.Any())
                {
                    return Ok(_localizer["EmailAlreadyExists"]);
                }
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException sqlEx)
            {
                _logger.LogError(sqlEx, _localizer["DatabaseError"] + ": " + sqlEx.Message);
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"] + ": " + ex.Message);
                return StatusCode(500, _localizer["PostLogError"]);
            }
        }

        /// <summary>
        /// Retrieves a list of patients based on their IDs.
        /// </summary>
        /// <param name="patientIds">A list of patient IDs to search for.</param>
        [HttpGet]
        [Route("patientlistbyid")]
        public async Task<ActionResult<IEnumerable<Office_visit_members>>> PatientListById([FromQuery] List<Guid> patientIds)
        {
            if (patientIds == null || !patientIds.Any())
            {
                return BadRequest(_localizer["InvalidPatientIds"]);
            }

            try
            {
                var matchedPatients = await _memberQueryHandler.GetPatientsByIdsAsync(patientIds);

                if (matchedPatients == null || !matchedPatients.Any())
                {
                    return NotFound(_localizer["NoMatchingPatients"]);
                }

                return Ok(matchedPatients);
            }
            catch (SqlException sqlEx)
            {
                _logger.LogError(sqlEx, _localizer["DatabaseError"] + ": " + sqlEx.Message);
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"] + ": " + ex.Message);
                return StatusCode(500, _localizer["PostLogError"]);
            }
        }


        [HttpGet]
        [Route("check-access")]
        public async Task<IActionResult> CheckProductAccess([FromQuery] Guid MemberId, [FromQuery] Guid ProductId)
        {
            IActionResult result;

            try
            {
                // Await the asynchronous call to CheckAccessAsync
                var access = await _checkAccessQueryHandler.CheckAccessAsync(MemberId, ProductId);

                if (access.HasAccess)
                {
                    result = Ok(new { Message = _localizer["AccessGranted"] });
                }
                else
                {
                    result = Ok(new { Message = _localizer["AccessDenied"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["InternalServerError"]);
                result = StatusCode(500, new { Message = _localizer["InternalServerError"] });
            }
            return result;
        }


        [HttpPost]
        [Route("OnAttributeCollectionSubmit")]
        public async Task<AttributeCollectionSubmitResponse> PostAsync([FromBody] JsonDocument requestPayload)
        {
            // Log the entire request payload as a string
            _logger.LogInformation($"Request payload: {requestPayload.RootElement.ToString()}");
            AttributeCollectionSubmitResponse r = new AttributeCollectionSubmitResponse();

            try
            {
                // Get the organization extension name from environment variable
                string orgExtensionName = Environment.GetEnvironmentVariable("ORGANIZATION_EXTENSION_NAME");

                // Extract the organization name from the JSON payload using the dynamic property name
                if (requestPayload.RootElement.TryGetProperty("data", out JsonElement dataElement) &&
                    dataElement.TryGetProperty("userSignUpInfo", out JsonElement userSignUpInfoElement) &&
                    userSignUpInfoElement.TryGetProperty("attributes", out JsonElement attributesElement) &&
                    attributesElement.TryGetProperty(orgExtensionName, out JsonElement orgNameElement) &&
                    orgNameElement.TryGetProperty("value", out JsonElement valueElement))
                {
                    string organizationName = valueElement.GetString();
                    _logger.LogInformation($"Organization name: {organizationName}");

                    // Check if the organization name is missing
                    if (string.IsNullOrEmpty(organizationName))
                    {
                        r.data.actions[0].odatatype = "microsoft.graph.attributeCollectionSubmit.showValidationError";
                        r.data.actions[0].message = "Please fix the following issues to proceed.";

                        // Create and configure attributeErrors
                        r.data.actions[0].attributeErrors = new AttributeCollectionSubmitResponse_AttributeError
                        {
                            OrganizationName = "Organization name is required."
                        };

                        return r;
                    }

                    // Check if the organization name is unique directly against the database
                    bool exists = await _organizationQueryHandler.FindByNameAsync(organizationName);
                    if (exists)
                    {
                        _logger.LogInformation($"Organization name '{organizationName}' already exists.");
                        r.data.actions[0].odatatype = "microsoft.graph.attributeCollectionSubmit.showValidationError";
                        r.data.actions[0].message = "Please fix the following issues to proceed.";

                        r.data.actions[0].attributeErrors = new AttributeCollectionSubmitResponse_AttributeError
                        {
                            OrganizationName = "This organization name is already in use. Please choose a different name."
                        };

                        return r;
                    }

                    // Organization name is unique, proceed with the flow
                    _logger.LogInformation($"Organization name '{organizationName}' is unique, continuing flow.");
                    r.data.actions[0].odatatype = "microsoft.graph.attributeCollectionSubmit.continueWithDefaultBehavior";

                    // If you need to set attributes too
                    r.data.actions[0].attributes = new AttributeCollectionSubmitResponse_Attribute
                    {
                        OrganizationName = organizationName
                    };
                }
                else
                {
                    // Handle missing properties
                    r.data.actions[0].odatatype = "microsoft.graph.attributeCollectionSubmit.showValidationError";
                    r.data.actions[0].message = "Invalid request payload structure.";

                    r.data.actions[0].attributeErrors = new AttributeCollectionSubmitResponse_AttributeError
                    {
                        OrganizationName = "Please ensure the request payload is correctly formatted."
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing request");
                r.data.actions[0].odatatype = "microsoft.graph.attributeCollectionSubmit.showBlockPage";
                r.data.actions[0].message = "An error occurred while validating the organization name. Please try again later.";
            }

            return r;
        }

    }
}
