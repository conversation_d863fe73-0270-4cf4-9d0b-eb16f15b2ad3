﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts
{
    public class Role : IContract
    {
        public Guid RoleId { get; set; }
        public string RoleName { get; set; }
        public DateTime? CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdatedBy { get; set; }
        public Boolean? IsActive { get; set; } = true;
        public Guid? OrganizationId { get; set; }
    }
}
