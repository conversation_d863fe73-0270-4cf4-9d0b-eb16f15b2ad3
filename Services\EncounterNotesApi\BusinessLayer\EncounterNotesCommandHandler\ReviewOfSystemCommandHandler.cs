﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class ReviewOfSystemCommandHandler : IReviewOfSystemCommandHandler<ReviewOfSystem>
    {
        private readonly IUnitOfWork _unitOfWork;

        public ReviewOfSystemCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddReviewOfSystem(List<ReviewOfSystem> reviewOfSystems)
        {
            await _unitOfWork.ReviewOfSystemRepository.AddAsync(reviewOfSystems);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateReviewOfSystem(ReviewOfSystem reviewOfSystem)
        {
            await _unitOfWork.ReviewOfSystemRepository.UpdateAsync(reviewOfSystem);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteReviewOfSystemById(Guid id)
        {
            await _unitOfWork.ReviewOfSystemRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteReviewOfSystemByEntity(ReviewOfSystem reviewOfSystem)
        {
            await _unitOfWork.ReviewOfSystemRepository.DeleteByEntityAsync(reviewOfSystem);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateReviewOfSystemList(List<ReviewOfSystem> reviewOfSystems)
        {
            await _unitOfWork.ReviewOfSystemRepository.UpdateRangeAsync(reviewOfSystems);
            await _unitOfWork.SaveAsync();
        }
    }
}
