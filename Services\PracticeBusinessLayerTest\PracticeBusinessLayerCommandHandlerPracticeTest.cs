﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeBusinessLayer.CommandHandler;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;


namespace PracticeBusinessLayerTest
{
    public class PracticeBusinessLayerCommandHandlerPracticeTest
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<ILogger<PracticeCommandHandler>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<IPracticeRepository> _mockPracticeRepository;
        private readonly PracticeCommandHandler _commandHandler;

        public PracticeBusinessLayerCommandHandlerPracticeTest()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<PracticeCommandHandler>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockPracticeRepository = new Mock<IPracticeRepository>();

            _mockUnitOfWork.Setup(uow => uow.PracticeRepository).Returns(_mockPracticeRepository.Object);

            _commandHandler = new PracticeCommandHandler(_mockConfiguration.Object, _mockUnitOfWork.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task AddTasks_Should_Call_Repository_AddAsync_And_SaveAsync()
        {
            // Arrange
            var tasks = new List<Tasks>
            {
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    PatientName = "P01",
                    TaskType = "Checkup",
                    Subject = "SUB",
                    AssignedTo = "D01",
                    Status = "Pending",
                    CreationDate = DateTime.Now,
                    StartDate = DateTime.Now.AddDays(1),
                    DueDate = DateTime.Now.AddDays(5),
                    CreatedBy = "Admin",
                    Priority = "High",
                    Notes = "SOME NOTES",
                    RecurringAction = false,
                    LastDueDate = DateTime.Now.AddDays(-5),
                    LastVisitDate = DateTime.Now.AddDays(-10),
                    Frequency = 0
                },
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    PatientName = "P01",
                    TaskType = "Surgery",
                    Subject = "SUB",
                    AssignedTo = "D02",
                    Status = "Scheduled",
                    CreationDate = DateTime.Now,
                    StartDate = DateTime.Now.AddDays(2),
                    DueDate = DateTime.Now.AddDays(7),
                    CreatedBy = "Admin",
                    Priority = "Medium",
                    Notes = "ANOTHER NOTES",
                    RecurringAction = true,
                    LastDueDate = DateTime.UtcNow.AddDays(-7),
                    LastVisitDate = DateTime.UtcNow.AddDays(-15),
                    Frequency = 1
                }
            };

            _mockPracticeRepository.Setup(repo => repo.AddAsync(It.Is<List<Tasks>>(x => x.Count == tasks.Count)))
                .Returns(Task.CompletedTask);

            // Act
            await _commandHandler.AddTasks(tasks);

            // Assert
            _mockPracticeRepository.Verify(repo => repo.AddAsync(It.Is<List<Tasks>>(x => x.Count == tasks.Count)), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateTasks_Should_Call_Repository_UpdateAsync_And_SaveAsync()
        {
            // Arrange
            var task = new Tasks
            {
                Id = Guid.NewGuid(),
                PatientName = "P04",
                TaskType = "Checkup",
                Subject = "SUB",
                AssignedTo = "D01",
                Status = "Pending",
                CreationDate = DateTime.Now,
                StartDate = DateTime.Now.AddDays(1),
                DueDate = DateTime.Now.AddDays(5),
                CreatedBy = "Admin",
                Priority = "High",
                Notes = "ANOTHER NOTE",
                RecurringAction = false,
                LastDueDate = DateTime.Now.AddDays(-5),
                LastVisitDate = DateTime.Now.AddDays(-10),
                Frequency = 0
            };

            _mockPracticeRepository.Setup(repo => repo.UpdateAsync(It.Is<Tasks>(t => t.Id == task.Id)))
                .Returns(Task.CompletedTask);

            // Act
            await _commandHandler.UpdateTasks(task);

            // Assert
            _mockPracticeRepository.Verify(repo => repo.UpdateAsync(It.Is<Tasks>(t => t.Id == task.Id)), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteTasksById_Should_Call_Repository_DeleteByIdAsync_And_SaveAsync()
        {
            // Arrange
            var taskId = Guid.NewGuid();

            _mockPracticeRepository.Setup(repo => repo.DeleteByIdAsync(It.Is<Guid>(id => id == taskId)))
                .Returns(Task.CompletedTask);

            // Act
            await _commandHandler.DeleteTasksById(taskId);

            // Assert
            _mockPracticeRepository.Verify(repo => repo.DeleteByIdAsync(It.Is<Guid>(id => id == taskId)), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteTasksByEntity_Should_Call_Repository_DeleteByEntityAsync_And_SaveAsync()
        {
            // Arrange
            var task = new Tasks
            {
                Id = Guid.NewGuid(),
                PatientName = "P01",
                TaskType = "Checkup",
                Subject = "Routine checkup",
                AssignedTo = "D02",
                Status = "Pending",
                CreationDate = DateTime.Now,
                StartDate = DateTime.Now.AddDays(1),
                DueDate = DateTime.Now.AddDays(5),
                CreatedBy = "Admin",
                Priority = "High",
                Notes = "NOTESS",
                RecurringAction = false,
                LastDueDate = DateTime.Now.AddDays(-5),
                LastVisitDate = DateTime.Now.AddDays(-10),
                Frequency = 0
            };

            _mockPracticeRepository.Setup(repo => repo.DeleteByEntityAsync(It.Is<Tasks>(t => t.Id == task.Id)))
                .Returns(Task.CompletedTask);

            // Act
            await _commandHandler.DeleteTasksByEntity(task);

            // Assert
            _mockPracticeRepository.Verify(repo => repo.DeleteByEntityAsync(It.Is<Tasks>(t => t.Id == task.Id)), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }
    }
}
