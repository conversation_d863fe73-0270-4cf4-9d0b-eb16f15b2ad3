﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IRecordRepository : IGenericRepository<Record>
    {
        Task AddRecordsAsync(List<Record> records);
        Task<List<WordTiming>> GetWordTimings(Guid id);

        Task<IEnumerable<Record>> GetByPCPIdAsync(Guid PCPId);
        Task<IEnumerable<Record>> GetByPatientIdAsync(Guid PatientId);
    }
}
