﻿using Azure.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace TeyaUIViewModels.ViewModel
{
    public class TokenService : ITokenService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        // ✅ Constructor Injection for HttpContextAccessor
        public TokenService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        // ✅ Get/Set AccessToken from Session
        public string? AccessToken
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken", value ?? "");
        }

        public string? AccessToken2
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken2");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken2", value ?? "");
        }

        public string? UserDetails
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("UserDetails");
            set => _httpContextAccessor.HttpContext?.Session.SetString("UserDetails", value ?? "");
        }
    }
}
