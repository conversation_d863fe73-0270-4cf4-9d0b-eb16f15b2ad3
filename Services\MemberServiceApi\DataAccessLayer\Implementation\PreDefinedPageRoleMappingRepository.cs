using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class PreDefinedPageRoleMappingRepository : IPreDefinedPageRoleMappingRepository
    {
        private readonly AccountDatabaseContext _context;

        public PreDefinedPageRoleMappingRepository(AccountDatabaseContext context)
        {
            _context = context;
        }

        public async Task AddAsync(PreDefinedPageRoleMapping PreDefinedPageRoleMapping)
        {
            await _context.PreDefinedPageRoleMappings.AddAsync(PreDefinedPageRoleMapping);
        }

        public async Task UpdateAsync(PreDefinedPageRoleMapping PreDefinedPageRoleMapping)
        {
            _context.PreDefinedPageRoleMappings.Update(PreDefinedPageRoleMapping);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var PreDefinedPageRoleMapping = await _context.PreDefinedPageRoleMappings.FindAsync(id);
            if (PreDefinedPageRoleMapping != null)
            {
                _context.PreDefinedPageRoleMappings.Remove(PreDefinedPageRoleMapping);
            }
        }

        public async Task<PreDefinedPageRoleMapping> GetByIdAsync(Guid id)
        {
            var result = await _context.PreDefinedPageRoleMappings.FindAsync(id);
            return result;
        }

        public async Task<List<PreDefinedPageRoleMapping>> GetAllAsync()
        {
            var result = await _context.PreDefinedPageRoleMappings.ToListAsync();
            return result;
        }

        public async Task<List<PreDefinedPageRoleMapping>> GetByPagePathAsync(string pagePath)
        {
            var result = await _context.PreDefinedPageRoleMappings
                .Where(prm => prm.PagePath.Contains(pagePath, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }

        public async Task<List<string>> GetRolesByPagePathAsync(string pagePath)
        {
            if (string.IsNullOrWhiteSpace(pagePath))
                throw new ArgumentException("Page path cannot be null or whitespace.", nameof(pagePath));

            var result = await _context.PreDefinedPageRoleMappings
                .Where(prm => prm.PagePath.ToLower() == pagePath.ToLower() && prm.IsActive && prm.HasAccess)
                .Select(prm => prm.RoleName)
                .Distinct()
                .ToListAsync();
            return result;
        }
    }
}
