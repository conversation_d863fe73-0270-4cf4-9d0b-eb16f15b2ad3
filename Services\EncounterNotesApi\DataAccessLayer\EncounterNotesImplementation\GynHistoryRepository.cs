﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class GynHistoryRepository : GenericRepository<GynHistory>, IGynHistoryRepository<GynHistory>
    {
        private readonly RecordDatabaseContext _context;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;

        public GynHistoryRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context,
                                                                                                                               localizer)
        {
            _context = context;
            _localizer = localizer;
        }

        public async Task AddAsync(IEnumerable<GynHistory> entities)
        {
            await _context.GynHistory.AddRangeAsync(entities);
            await _context.SaveChangesAsync();
        }



        public async Task<GynHistory> GetByIdAsync(Guid id)
        {
            return await _context.GynHistory.FirstOrDefaultAsync((System.Linq.Expressions.Expression<Func<GynHistory, bool>>)(gyn => (bool)(gyn.gynId == id && !gyn.IsDeleted)));
        }

        public async Task<IEnumerable<GynHistory>> GetByPatientIdAsync(Guid patientId)
        {
            return await _context.GynHistory.Where(gyn => gyn.PatientId == patientId && !gyn.IsDeleted).ToListAsync();
        }



        public async Task UpdateAsync(GynHistory entity)
        {
            _context.GynHistory.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteByEntityAsync(GynHistory entity)
        {
            entity.IsDeleted = true;
            _context.GynHistory.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteByIdAsync(Guid gynId)
        {
            var entity = await GetByIdAsync(gynId);
            if (entity != null)
            {
                entity.IsDeleted = true;
                await UpdateAsync(entity);
            }
        }

        public async Task UpdateGynHistoryListAsync(List<GynHistory> gynHistoryList)
        {
            if (gynHistoryList == null || gynHistoryList.Count == 0)
                throw new ArgumentException("Invalid records provided.", nameof(gynHistoryList));

            foreach (var gynHistory in gynHistoryList)
            {
                var existinggynHistory = await _context.GynHistory
                    .FirstOrDefaultAsync((System.Linq.Expressions.Expression<Func<GynHistory, bool>>)(gyn => gyn.gynId == gynHistory.gynId));

                if (existinggynHistory != null)
                {
                    existinggynHistory.Notes = gynHistory.Notes;
                    existinggynHistory.Symptoms = gynHistory.Symptoms;
                    existinggynHistory.OrganizationId = gynHistory.OrganizationId;
                    existinggynHistory.PcpId = gynHistory.PcpId;
                    existinggynHistory.DateOfHistory = gynHistory.DateOfHistory;
                    existinggynHistory.IsDeleted = gynHistory.IsDeleted;
                    _context.GynHistory.Update((GynHistory)existinggynHistory);
                }
            }
            await _context.SaveChangesAsync();
        }


    }
}
