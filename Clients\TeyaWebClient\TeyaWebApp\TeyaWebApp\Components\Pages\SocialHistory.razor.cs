﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Graph.Models;

namespace TeyaWebApp.Components.Pages
{
    public partial class SocialHistory : ComponentBase
    {
        [Inject] public ISocialHistoryService SocialHistoryService { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        private List<PatientSocialHistory> SocialHistoryList { get; set; }
        private List<PatientSocialHistory> AddList = new();
        private List<PatientSocialHistory> DeleteList = new();
        public SfGrid<PatientSocialHistory> SocialHistoryGrid { get; set; }
        public SfRichTextEditor RichTextEditor { get; set; }
        private MudDialog _addMemberDialog;
        private Guid PatientId { get; set; }
        private Guid? orgid { get; set; }
        private string editorContent;
        private bool add = false;
        [Inject] private ISnackbar Snackbar { get; set; }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Social History" }
        };

        /// <summary>
        /// Initializes the component asynchronously and loads patient social history data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientService.PatientData.Id;
            orgid = PatientService.PatientData.OrganizationID;
            await LoadDataAsync();
        }

        /// <summary>
        /// Loads social history data asynchronously for the given patient.
        /// </summary>
        private async Task LoadDataAsync()
        {
            SocialHistoryList = await SocialHistoryService.GetAllByIdAndIsActiveAsync(PatientId);
            editorContent = string.Join("<p>",SocialHistoryList.Select(display => $"<b>{display.CreatedDate.ToString("MM-dd-yyyy")}</b>&emsp;{display.LifestyleHabits}"));
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Opens the add task dialog for adding new social history records.
        /// </summary>
        private async Task OpenAddTaskDialog()
        {
            await _addMemberDialog.ShowAsync();
        }

        /// <summary>
        /// Handles the completion of actions such as Add, Save, and Delete in the social history grid.
        /// </summary>
        /// <param name="args">Action event arguments containing the details of the operation.</param>
        private void ActionCompletedHandler(ActionEventArgs<PatientSocialHistory> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedhistories = args.Data as PatientSocialHistory;
                    var existingItem = AddList.FirstOrDefault(existinghistory => existinghistory.SocialHistoryId == deletedhistories.SocialHistoryId);
                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedhistories.isActive = false;
                        deletedhistories.UpdatedBy = Guid.Parse(User.id);
                        deletedhistories.UpdatedDate = DateTime.Now;
                        DeleteList.Add(deletedhistories);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.SocialHistoryId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = orgid;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.isActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedhistories = args.Data;
                        if (addedhistories != null)
                        {
                            AddList.Add(addedhistories);
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Saves the changes made to the social history records by adding new entries and updating existing ones.
        /// </summary>
        private async Task SaveChanges()
        {
            await SocialHistoryService.AddHistoryListAsync(AddList);
            await SocialHistoryService.UpdateHistoryListAsync(DeleteList);
            await SocialHistoryService.UpdateHistoryListAsync(SocialHistoryList);
            AddList.Clear();
            DeleteList.Clear();
            await LoadDataAsync();
            await _addMemberDialog.CloseAsync();
        }

        /// <summary>
        /// Cancels changes and reloads the original social history data.
        /// </summary>
        private async Task CancelChanges()
        {
            AddList.Clear();
            DeleteList.Clear();
            await LoadDataAsync();
            await _addMemberDialog.CloseAsync();
        }
    }
}
