﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class InsuranceQueryHandler : IInsuranceQueryHandler<Insurance>
    {
        private readonly IUnitOfWork _unitOfWork;

        public InsuranceQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Insurance> GetInsuranceByIdAsync(Guid id)
        {
            var insurance = await _unitOfWork.InsuranceRepository.GetByIdAsync(id);
            return insurance;
        }

        public async Task<List<Insurance>> GetAllInsurancesAsync()
        {
            var insurances = await _unitOfWork.InsuranceRepository.GetAllAsync();
            return insurances.ToList();
        }
    }
}
