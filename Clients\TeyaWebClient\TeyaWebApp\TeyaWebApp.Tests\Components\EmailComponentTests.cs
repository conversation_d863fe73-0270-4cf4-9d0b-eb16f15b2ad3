using Bunit;
using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using FluentAssertions;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Pages;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MudBlazor.Services;
using MudBlazor;

namespace TeyaWebApp.Tests.Components
{
    [TestFixture]
    public class EmailComponentTests : TestContext
    {
        private Mock<ILogger<Email>> _loggerMock;
        private Mock<IStringLocalizer<Email>> _localizerMock;
        private Mock<NavigationManager> _navigationManagerMock;
        private List<MessageModel> _testMessages;

        [SetUp]
        public void Setup()
        {
            _loggerMock = new Mock<ILogger<Email>>();
            _localizerMock = new Mock<IStringLocalizer<Email>>();
            _navigationManagerMock = new Mock<NavigationManager>();

            // Setup test data
            _testMessages = new List<MessageModel>
            {
                new MessageModel
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 1",
                    MessageContent = "Test message content 1",
                    SentDateTime = DateTime.UtcNow,
                    IsRead = false,
                    IsDeleted = false,
                    IsArchived = false,
                    IsImportant = false,
                    HasAttachment = false,
                    Labels = new List<string>()
                },
                new MessageModel
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "Jane Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "John Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 2",
                    MessageContent = "Test message content 2",
                    SentDateTime = DateTime.UtcNow.AddMinutes(-30),
                    IsRead = true,
                    IsDeleted = false,
                    IsArchived = false,
                    IsImportant = true,
                    HasAttachment = true,
                    Labels = new List<string> { "Important" }
                }
            };

            // Register services
            Services.AddSingleton(_loggerMock.Object);
            Services.AddSingleton(_localizerMock.Object);
            Services.AddSingleton(_navigationManagerMock.Object);
            Services.AddMudServices();
            Services.AddLogging();
        }

        [Test]
        public void Email_Component_RendersCorrectly()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            component.Should().NotBeNull();
            component.Find(".email-app").Should().NotBeNull();
            component.Find(".sidebar").Should().NotBeNull();
            component.Find(".main").Should().NotBeNull();
        }

        [Test]
        public void Email_Sidebar_ContainsComposeButton()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var composeButton = component.Find(".compose-btn");
            composeButton.Should().NotBeNull();
            composeButton.TextContent.Should().Contain("Compose");
        }

        [Test]
        public void Email_Menu_ContainsExpectedItems()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var menu = component.Find(".menu");
            menu.Should().NotBeNull();
            
            // Check for menu items
            var menuItems = component.FindAll(".menu-text");
            menuItems.Should().NotBeEmpty();
            
            // Verify specific menu items exist
            var menuTexts = menuItems.Select(item => item.TextContent).ToList();
            menuTexts.Should().Contain("Inbox");
            menuTexts.Should().Contain("Sent");
            menuTexts.Should().Contain("Drafts");
            menuTexts.Should().Contain("Trash");
        }

        [Test]
        public void Email_Toolbar_ContainsTabsAndSearch()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var toolbar = component.Find(".toolbar");
            toolbar.Should().NotBeNull();
            
            var tabs = component.Find(".tabs");
            tabs.Should().NotBeNull();
            
            var searchBox = component.Find(".search-box");
            searchBox.Should().NotBeNull();
        }

        [Test]
        public void Email_ComposeButton_ShowsComposeModal()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            
            // Click compose button
            composeButton.Click();

            // Assert
            var composeContainer = component.Find(".compose-container");
            composeContainer.Should().NotBeNull();
        }

        [Test]
        public void Email_ComposeModal_ContainsRequiredFields()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Assert
            var composeContainer = component.Find(".compose-container");
            composeContainer.Should().NotBeNull();
            
            // Check for required fields
            var toField = component.Find("input[placeholder*='Recipients']");
            toField.Should().NotBeNull();
            
            var subjectField = component.Find("input[placeholder*='Subject']");
            subjectField.Should().NotBeNull();
            
            var messageField = component.Find("textarea[placeholder*='Compose your message']");
            messageField.Should().NotBeNull();
        }

        [Test]
        public void Email_TabSwitching_WorksCorrectly()
        {
            // Act
            var component = RenderComponent<Email>();
            
            // Find and click unread tab
            var unreadTab = component.Find(".tab:contains('Unread')");
            unreadTab.Click();

            // Assert
            var activeTab = component.Find(".tab.active");
            activeTab.Should().NotBeNull();
            activeTab.TextContent.Should().Contain("Unread");
        }

        [Test]
        public void Email_SearchInput_AcceptsText()
        {
            // Act
            var component = RenderComponent<Email>();
            var searchInput = component.Find(".search-input");
            
            // Type in search box
            searchInput.Change("test search");

            // Assert
            searchInput.GetAttribute("value").Should().Be("test search");
        }

        [Test]
        public void Email_MenuItemSelection_UpdatesActiveState()
        {
            // Act
            var component = RenderComponent<Email>();
            
            // Find and click sent menu item
            var sentMenuItem = component.Find("[data-folder='sent']");
            sentMenuItem.Click();

            // Assert
            var activeMenuItem = component.Find(".menu .active");
            activeMenuItem.Should().NotBeNull();
            activeMenuItem.GetAttribute("data-folder").Should().Be("sent");
        }

        [Test]
        public void Email_FileUpload_IsPresent()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Assert
            var fileInput = component.Find("input[type='file']");
            fileInput.Should().NotBeNull();
            
            var uploadButton = component.Find(".file-upload-btn");
            uploadButton.Should().NotBeNull();
            uploadButton.TextContent.Should().Contain("Add Attachment");
        }

        [Test]
        public void Email_SendButton_IsPresent()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Assert
            var sendButton = component.Find(".send-btn");
            sendButton.Should().NotBeNull();
            sendButton.TextContent.Should().Contain("Send");
        }

        [Test]
        public void Email_CloseCompose_HidesModal()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();
            
            // Verify modal is shown
            var composeContainer = component.Find(".compose-container");
            composeContainer.Should().NotBeNull();
            
            // Click close button
            var closeButton = component.Find(".btn-icon");
            closeButton.Click();

            // Assert
            var composeContainers = component.FindAll(".compose-container");
            composeContainers.Should().BeEmpty();
        }

        [Test]
        public void Email_ResponsiveLayout_WorksCorrectly()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var sidebar = component.Find(".sidebar");
            sidebar.Should().NotBeNull();
            
            var main = component.Find(".main");
            main.Should().NotBeNull();
            
            var emailList = component.Find(".email-list");
            emailList.Should().NotBeNull();
            
            var emailDetail = component.Find(".email-detail");
            emailDetail.Should().NotBeNull();
        }

        [Test]
        public void Email_MudBlazorComponents_AreRendered()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            // Check for MudBlazor components
            var mudContainer = component.FindAll("div").FirstOrDefault(x => x.GetClasses().Contains("mud-container"));
            mudContainer.Should().NotBeNull();
            
            // Check for MudPaper components
            var mudPapers = component.FindAll("div").Where(x => x.GetClasses().Contains("mud-paper"));
            mudPapers.Should().NotBeEmpty();
        }

        [TearDown]
        public void TearDown()
        {
            Dispose();
        }
    }
}
