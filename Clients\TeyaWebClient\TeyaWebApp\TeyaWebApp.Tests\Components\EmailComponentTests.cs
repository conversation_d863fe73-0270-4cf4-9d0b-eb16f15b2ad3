using Bunit;
using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using FluentAssertions;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Pages;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MudBlazor.Services;
using MudBlazor;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.JSInterop;
using TeyaWebApp.TeyaAIScribeResource;

namespace TeyaWebApp.Tests.Components
{
    [TestFixture]
    public class EmailComponentTests : TestContext
    {
        private Mock<ILogger<MessageModel>> _loggerMock;
        private Mock<IStringLocalizer<TeyaAIScribeResource.TeyaAIScribeResource>> _localizerMock;
        private Mock<NavigationManager> _navigationManagerMock;
        private Mock<IMessageService> _messageServiceMock;
        private Mock<ActiveUser> _activeUserMock;
        private Mock<IJSRuntime> _jsRuntimeMock;
        private Mock<IConfiguration> _configurationMock;
        private List<MessageModel> _testMessages;

        [SetUp]
        public void Setup()
        {
            _loggerMock = new Mock<ILogger<MessageModel>>();
            _localizerMock = new Mock<IStringLocalizer<TeyaAIScribeResource.TeyaAIScribeResource>>();
            _navigationManagerMock = new Mock<NavigationManager>();
            _messageServiceMock = new Mock<IMessageService>();
            _activeUserMock = new Mock<ActiveUser>();
            _jsRuntimeMock = new Mock<IJSRuntime>();
            _configurationMock = new Mock<IConfiguration>();

            // Setup localizer strings for email functionality
            _localizerMock.Setup(x => x["NewMessageReceived"])
                         .Returns(new LocalizedString("NewMessageReceived", "New message received"));
            _localizerMock.Setup(x => x["MessageReadStatusChanged"])
                         .Returns(new LocalizedString("MessageReadStatusChanged", "Message read status changed"));
            _localizerMock.Setup(x => x["RefreshMessagesUpdated"])
                         .Returns(new LocalizedString("RefreshMessagesUpdated", "Messages refreshed"));
            _localizerMock.Setup(x => x["MissingRecipientOrContent"])
                         .Returns(new LocalizedString("MissingRecipientOrContent", "Missing recipient or content"));
            _localizerMock.Setup(x => x["FormClearedAfterSuccessfulSend"])
                         .Returns(new LocalizedString("FormClearedAfterSuccessfulSend", "Form cleared after successful send"));
            _localizerMock.Setup(x => x["FailedToSendMessage"])
                         .Returns(new LocalizedString("FailedToSendMessage", "Failed to send message"));
            _localizerMock.Setup(x => x["ErrorSendingMessage"])
                         .Returns(new LocalizedString("ErrorSendingMessage", "Error sending message"));

            // Setup active user
            _activeUserMock.SetupGet(x => x.displayName).Returns("Test User");
            _activeUserMock.SetupGet(x => x.mail).Returns("<EMAIL>");

            // Setup test data
            _testMessages = new List<MessageModel>
            {
                new MessageModel
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 1",
                    MessageContent = "Test message content 1",
                    SentDateTime = DateTime.UtcNow,
                    IsRead = false,
                    IsDeleted = false,
                    IsArchived = false,
                    IsImportant = false,
                    HasAttachment = false,
                    Labels = new List<string>()
                },
                new MessageModel
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "Jane Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "John Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 2",
                    MessageContent = "Test message content 2",
                    SentDateTime = DateTime.UtcNow.AddMinutes(-30),
                    IsRead = true,
                    IsDeleted = false,
                    IsArchived = false,
                    IsImportant = true,
                    HasAttachment = true,
                    Labels = new List<string> { "Important" }
                }
            };

            // Register services
            Services.AddSingleton(_loggerMock.Object);
            Services.AddSingleton(_localizerMock.Object);
            Services.AddSingleton(_navigationManagerMock.Object);
            Services.AddSingleton(_messageServiceMock.Object);
            Services.AddSingleton(_activeUserMock.Object);
            Services.AddSingleton(_jsRuntimeMock.Object);
            Services.AddSingleton(_configurationMock.Object);
            Services.AddMudServices();
            Services.AddLogging();
        }

        [Test]
        public void Email_Component_RendersCorrectly()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            component.Should().NotBeNull();
            component.Find(".email-app").Should().NotBeNull();
            component.Find(".sidebar").Should().NotBeNull();
            component.Find(".main").Should().NotBeNull();
        }

        [Test]
        public void Email_Sidebar_ContainsComposeButton()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var composeButton = component.Find(".compose-btn");
            composeButton.Should().NotBeNull();
            composeButton.TextContent.Should().Contain("Compose");
        }

        [Test]
        public void Email_Menu_ContainsExpectedItems()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var menu = component.Find(".menu");
            menu.Should().NotBeNull();

            // Check for menu items
            var menuItems = component.FindAll(".menu-text");
            menuItems.Should().NotBeEmpty();

            // Verify specific menu items exist
            var menuTexts = menuItems.Select(item => item.TextContent).ToList();
            menuTexts.Should().Contain("Inbox");
            menuTexts.Should().Contain("Sent");
            menuTexts.Should().Contain("Drafts");
            menuTexts.Should().Contain("Trash");
        }

        [Test]
        public void Email_Toolbar_ContainsTabsAndSearch()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var toolbar = component.Find(".toolbar");
            toolbar.Should().NotBeNull();

            var tabs = component.Find(".tabs");
            tabs.Should().NotBeNull();

            var searchBox = component.Find(".search-box");
            searchBox.Should().NotBeNull();
        }

        [Test]
        public void Email_ComposeButton_ShowsComposeModal()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");

            // Click compose button
            composeButton.Click();

            // Assert
            var composeContainer = component.Find(".compose-container");
            composeContainer.Should().NotBeNull();
        }

        [Test]
        public void Email_ComposeModal_ContainsRequiredFields()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Assert
            var composeContainer = component.Find(".compose-container");
            composeContainer.Should().NotBeNull();

            // Check for required fields
            var toField = component.Find("input[placeholder*='Recipients']");
            toField.Should().NotBeNull();

            var subjectField = component.Find("input[placeholder*='Subject']");
            subjectField.Should().NotBeNull();

            var messageField = component.Find("textarea[placeholder*='Compose your message']");
            messageField.Should().NotBeNull();
        }

        [Test]
        public void Email_TabSwitching_WorksCorrectly()
        {
            // Act
            var component = RenderComponent<Email>();

            // Find and click unread tab
            var unreadTab = component.Find(".tab:contains('Unread')");
            unreadTab.Click();

            // Assert
            var activeTab = component.Find(".tab.active");
            activeTab.Should().NotBeNull();
            activeTab.TextContent.Should().Contain("Unread");
        }

        [Test]
        public void Email_SearchInput_AcceptsText()
        {
            // Act
            var component = RenderComponent<Email>();
            var searchInput = component.Find(".search-input");

            // Type in search box
            searchInput.Change("test search");

            // Assert
            searchInput.GetAttribute("value").Should().Be("test search");
        }

        [Test]
        public void Email_MenuItemSelection_UpdatesActiveState()
        {
            // Act
            var component = RenderComponent<Email>();

            // Find and click sent menu item
            var sentMenuItem = component.Find("[data-folder='sent']");
            sentMenuItem.Click();

            // Assert
            var activeMenuItem = component.Find(".menu .active");
            activeMenuItem.Should().NotBeNull();
            activeMenuItem.GetAttribute("data-folder").Should().Be("sent");
        }

        [Test]
        public void Email_FileUpload_IsPresent()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Assert
            var fileInput = component.Find("input[type='file']");
            fileInput.Should().NotBeNull();

            var uploadButton = component.Find(".file-upload-btn");
            uploadButton.Should().NotBeNull();
            uploadButton.TextContent.Should().Contain("Add Attachment");
        }

        [Test]
        public void Email_SendButton_IsPresent()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Assert
            var sendButton = component.Find(".send-btn");
            sendButton.Should().NotBeNull();
            sendButton.TextContent.Should().Contain("Send");
        }

        [Test]
        public void Email_CloseCompose_HidesModal()
        {
            // Act
            var component = RenderComponent<Email>();
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Verify modal is shown
            var composeContainer = component.Find(".compose-container");
            composeContainer.Should().NotBeNull();

            // Click close button
            var closeButton = component.Find(".btn-icon");
            closeButton.Click();

            // Assert
            var composeContainers = component.FindAll(".compose-container");
            composeContainers.Should().BeEmpty();
        }

        [Test]
        public void Email_ResponsiveLayout_WorksCorrectly()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            var sidebar = component.Find(".sidebar");
            sidebar.Should().NotBeNull();

            var main = component.Find(".main");
            main.Should().NotBeNull();

            var emailList = component.Find(".email-list");
            emailList.Should().NotBeNull();

            var emailDetail = component.Find(".email-detail");
            emailDetail.Should().NotBeNull();
        }

        [Test]
        public void Email_MudBlazorComponents_AreRendered()
        {
            // Act
            var component = RenderComponent<Email>();

            // Assert
            // Check for MudBlazor components
            var mudContainer = component.FindAll("div").FirstOrDefault(x => x.GetClasses().Contains("mud-container"));
            mudContainer.Should().NotBeNull();

            // Check for MudPaper components
            var mudPapers = component.FindAll("div").Where(x => x.GetClasses().Contains("mud-paper"));
            mudPapers.Should().NotBeEmpty();
        }

        [Test]
        public async Task Email_SendMessage_UsesLocalizerForSuccessMessage()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _messageServiceMock.Setup(x => x.SendMessageAsync(It.IsAny<SendMessageRequest>()))
                              .ReturnsAsync(messageId);
            _messageServiceMock.Setup(x => x.GetMessageByIdAsync(messageId))
                              .ReturnsAsync(_testMessages[0]);

            var component = RenderComponent<Email>();

            // Act - Simulate sending a message
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Fill in form fields
            var toField = component.Find("input[placeholder*='Recipients']");
            toField.Change("<EMAIL>");

            var subjectField = component.Find("input[placeholder*='Subject']");
            subjectField.Change("Test Subject");

            var messageField = component.Find("textarea[placeholder*='Compose your message']");
            messageField.Change("Test message content");

            var sendButton = component.Find(".send-btn");
            sendButton.Click();

            // Wait for async operations
            await Task.Delay(100);

            // Assert
            _localizerMock.Verify(x => x["FormClearedAfterSuccessfulSend"], Times.AtLeastOnce);
        }

        [Test]
        public async Task Email_SendMessage_UsesLocalizerForErrorMessage()
        {
            // Arrange
            _messageServiceMock.Setup(x => x.SendMessageAsync(It.IsAny<SendMessageRequest>()))
                              .ThrowsAsync(new Exception("Test error"));

            var component = RenderComponent<Email>();

            // Act - Simulate sending a message that fails
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            // Fill in form fields
            var toField = component.Find("input[placeholder*='Recipients']");
            toField.Change("<EMAIL>");

            var messageField = component.Find("textarea[placeholder*='Compose your message']");
            messageField.Change("Test message content");

            var sendButton = component.Find(".send-btn");
            sendButton.Click();

            // Wait for async operations
            await Task.Delay(100);

            // Assert
            _localizerMock.Verify(x => x["ErrorSendingMessage"], Times.AtLeastOnce);
        }

        [Test]
        public async Task Email_RefreshMessages_UsesLocalizerForUpdateMessage()
        {
            // Arrange
            _messageServiceMock.Setup(x => x.GetMessagesByEmailAsync(It.IsAny<string>()))
                              .ReturnsAsync(_testMessages);
            _messageServiceMock.Setup(x => x.GetDeletedMessagesAsync(It.IsAny<string>()))
                              .ReturnsAsync(new List<MessageModel>());

            var component = RenderComponent<Email>();

            // Act - Trigger refresh (this would normally be called by SignalR)
            await component.InvokeAsync(async () =>
            {
                // Simulate the refresh method being called
                var refreshMethod = component.Instance.GetType().GetMethod("RefreshMessagesAsync",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (refreshMethod != null)
                {
                    await (Task)refreshMethod.Invoke(component.Instance, null);
                }
            });

            // Assert
            _localizerMock.Verify(x => x["RefreshMessagesUpdated"], Times.AtLeastOnce);
        }

        [Test]
        public void Email_LocalizerKeys_AreProperlyConfigured()
        {
            // Act & Assert - Verify all required localizer keys are set up
            _localizerMock.Verify(x => x["NewMessageReceived"], Times.Never); // Not called during setup
            _localizerMock.Verify(x => x["MessageReadStatusChanged"], Times.Never); // Not called during setup

            // Verify the localizer mock returns expected values
            var newMessageText = _localizerMock.Object["NewMessageReceived"];
            newMessageText.Value.Should().Be("New message received");

            var errorText = _localizerMock.Object["ErrorSendingMessage"];
            errorText.Value.Should().Be("Error sending message");
        }

        [Test]
        public async Task Email_MarkAsRead_UsesLogger()
        {
            // Arrange
            var message = _testMessages[0];
            message.IsRead = false;

            _messageServiceMock.Setup(x => x.MarkMessageAsReadAsync(message.MessageId))
                              .Returns(Task.CompletedTask);

            var component = RenderComponent<Email>();

            // Act - Select a message (which should mark it as read)
            var emailItem = component.Find(".email-item");
            emailItem.Click();

            // Wait for async operations
            await Task.Delay(100);

            // Assert
            _messageServiceMock.Verify(x => x.MarkMessageAsReadAsync(message.MessageId), Times.Once);
        }

        [Test]
        public void Email_ValidationMessages_UseLocalizer()
        {
            // Arrange
            var component = RenderComponent<Email>();

            // Act - Try to send empty message
            var composeButton = component.Find(".compose-btn");
            composeButton.Click();

            var sendButton = component.Find(".send-btn");
            sendButton.Click();

            // Assert - Should use localizer for validation message
            _localizerMock.Verify(x => x["MissingRecipientOrContent"], Times.AtLeastOnce);
        }

        [TearDown]
        public void TearDown()
        {
            Dispose();
        }
    }
}
