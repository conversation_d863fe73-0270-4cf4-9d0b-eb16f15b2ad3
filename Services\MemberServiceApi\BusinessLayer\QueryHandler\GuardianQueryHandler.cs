﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class GuardianQueryHandler : IGuardianQueryHandler<Guardian>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GuardianQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Guardian> GetGuardianByIdAsync(Guid id)
        {
            var Guardian = await _unitOfWork.GuardianRepository.GetByIdAsync(id);
            return Guardian;
        }



        public async Task<List<Guardian>> GetAllGuardianAsync()
        {
            var Guardians = await _unitOfWork.GuardianRepository.GetAllAsync();
            return Guardians.ToList();
        }
    }
}
