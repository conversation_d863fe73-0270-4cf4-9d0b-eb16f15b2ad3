﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using AppointmentContracts;
using Microsoft.Extensions.Localization;
using BusinessLayer;
using Microsoft.EntityFrameworkCore;
using System;
using AppointmentDataAccessLayer.AppointmentContext;
using BusinessLayer.QueryHandler;
using Appointments.AppointmentResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace Appointments.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AppointmentsController : ControllerBase
    {
        private readonly IAppointmentCommandHandler<Appointment> _AppointmentDataHandler;
        private readonly IAppointmentQueryHandler<Appointment> _AppointmentQueryHandler;
        private readonly ILogger<AppointmentsController> _logger;
        private readonly IStringLocalizer<AppointmentStrings> _localizer;

        public AppointmentsController(
            IAppointmentCommandHandler<Appointment> dataHandler,
            IAppointmentQueryHandler<Appointment> queryHandler,
            ILogger<AppointmentsController> logger,
            IStringLocalizer<AppointmentStrings> localizer)
        {
            _AppointmentDataHandler = dataHandler;
            _AppointmentQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Retrieves all appointments.
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var appointments = await _AppointmentQueryHandler.GetAppointment();
                return Ok(appointments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(int.Parse(_localizer["500"]));
            }
        }

        /// <summary>
        /// Retrieves appointments for a specific user.
        /// </summary>
        /// <param name="userId">User ID</param>
        [HttpGet("user/{userId:guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> GetByUserId(Guid userId,Guid OrgID,bool Subscription)
        {
            try
            {
                var appointmentsByUser = await _AppointmentQueryHandler.GetAppointmentByUserId(userId,OrgID,Subscription);
                return Ok(appointmentsByUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(int.Parse(_localizer["500"]));
            }
        }

        /// <summary>
        /// Retrieves appointments by date.
        /// </summary>
        /// <param name="date">Appointment date</param>
        [HttpGet("{date:datetime}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> GetByDate(DateTime date,Guid OrgID, bool Subscription)
        {
            try
            {
                var appointmentsByDate = await _AppointmentQueryHandler.GetAppointmentByDate(date,OrgID,Subscription);
                return Ok(appointmentsByDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(int.Parse(_localizer["500"]));
            }
        }

        /// <summary>
        /// Retrieves an appointment by its ID.
        /// </summary>
        /// <param name="Subscription">Subscription flag</param>
        /// <param name="id">Appointment ID</param>
        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> GetById(bool Subscription, Guid id, Guid OrgID)
        {
            try
            {
                var appointment = await _AppointmentQueryHandler.GetAppointmentById(Subscription, id, OrgID);
                return Ok(appointment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(int.Parse(_localizer["500"]));
            }
        }

        /// <summary>
        /// Updates an appointment by ID.
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="appointment">Updated appointment data</param>
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] Appointment appointment)
        {
            if (appointment == null || appointment.Id != id)
                return BadRequest(_localizer["InvalidAppointment"]);

            try
            {
                await _AppointmentDataHandler.UpdateAppointment(appointment, id);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(int.Parse(_localizer["500"]));
            }
        }

        /// <summary>
        /// Deletes an appointment by ID.
        /// </summary>
        /// <param name="Subscription">Subscription flag</param>
        /// <param name="id">Appointment ID</param>
        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> DeleteById(bool Subscription, Guid id, Guid OrgID)
        {
            try
            {
                await _AppointmentDataHandler.DeleteAppointmentById(Subscription, id, OrgID);
                return Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(int.Parse(_localizer["500"]));
            }
        }

        /// <summary>
        /// Adds multiple appointment records.
        /// </summary>
        /// <param name="appointmentrecords">List of appointment records</param>
        [HttpPost]
        [Route("appointmentRecord")]
        public async Task<IActionResult> AppointmentRecord([FromBody] List<Appointment> appointmentrecords)
        {
            if (appointmentrecords == null || appointmentrecords.Count == 0)
                return BadRequest(_localizer["NoLicense"]);

            try
            {
                await _AppointmentDataHandler.AddAppointment(appointmentrecords, appointmentrecords[0].OrganisationId);
                var firstAppointmentId = appointmentrecords.FirstOrDefault()?.Id;
                return Ok(new { Message = _localizer["SuccessfulRegistration"], FirstAppointmentId = firstAppointmentId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(int.Parse(_localizer["500"]));
            }
        }

        /// <summary>
        /// Adds a shard request.
        /// </summary>
        /// <param name="request">Shard request data</param>
        [HttpPost("add")]
        public IActionResult AddShard([FromBody] ShardRequest request)
        {
            try
            {
                _AppointmentDataHandler.AddShard(request);
                return Ok(_localizer["ShardAdded"]);
            }
            catch (Exception ex)
            {
                return StatusCode(int.Parse(_localizer["500"]), ex);
            }
        }
    }
}
