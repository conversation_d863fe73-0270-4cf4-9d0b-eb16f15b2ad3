﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public interface IObHistoryQueryHandler<T> where T : class
    {
        Task<IEnumerable<T>> GetAllAsync();
        Task<T> GetByIdAsync(Guid id);
        Task<IEnumerable<T>> GetByPatientIdAsync(Guid patientId);
    }
}
