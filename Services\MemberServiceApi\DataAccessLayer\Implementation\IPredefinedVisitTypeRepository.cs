﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;

namespace MemberServiceDataAccessLayer.Implementation
{
    /// <summary>
    /// Repository implementation for handling PredefinedVisitType-related database operations.
    /// </summary>
    public class PredefinedVisitTypeRepository : GenericRepository<PredefinedVisitType>, IPredefinedVisitTypeRepository
    {
        private readonly AccountDatabaseContext _context;

        /// <summary>
        /// Initializes a new instance of the <see cref="PredefinedVisitTypeRepository"/> class.
        /// </summary>
        /// <param name="context">Database context for PredefinedVisitType-related operations.</param>
        public PredefinedVisitTypeRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }
    }
}
