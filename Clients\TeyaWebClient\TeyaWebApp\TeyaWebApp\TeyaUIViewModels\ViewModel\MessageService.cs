using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using DotNetEnv;

namespace TeyaUIViewModels.ViewModel
{
    public interface IMessageService
    {
        Task<Guid> SendMessageAsync(SendMessageRequest request);
        Task<List<MessageModel>> GetMessagesByEmailAsync(string email);
        Task<List<MessageModel>> GetConversationAsync(string senderEmail, string receiverEmail);
        Task<MessageModel?> GetMessageByIdAsync(Guid messageId);
        Task<List<MessageModel>> GetUnreadMessagesAsync(string email);
        Task<List<MessageModel>> GetSentMessagesAsync(string email);
        Task<List<MessageModel>> GetReceivedMessagesAsync(string email);
        Task<bool> MarkMessageAsReadAsync(Guid messageId);
        Task<bool> DeleteMessageAsync(Guid messageId);
        Task<List<AttachmentModel>> GetMessageAttachmentsAsync(Guid messageId);
        Task<AttachmentModel?> GetAttachmentAsync(Guid attachmentId);
        Task<byte[]> DownloadAttachmentAsync(Guid attachmentId);
        Task<bool> DeleteAttachmentAsync(Guid attachmentId);

        // Soft Delete Methods
        Task<bool> SoftDeleteMessageAsync(Guid messageId, string deletedBy, string? reason = null);
        Task<bool> RestoreMessageAsync(Guid messageId, string restoredBy, string? reason = null);
        Task<List<MessageModel>> GetDeletedMessagesAsync(string email);

        // Archive Methods
        Task<bool> ArchiveMessageAsync(Guid messageId, string archivedBy);
        Task<bool> UnarchiveMessageAsync(Guid messageId, string unarchivedBy);

        // Important Methods
        Task<bool> ToggleImportantAsync(Guid messageId, bool isImportant, string updatedBy);
    }

    public class MessageService : IMessageService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<MessageService> _localizer;
        private readonly ILogger<MessageService> _logger;
        private readonly string _messageApiUrl;
        private readonly ITokenService _tokenService;

        public MessageService(
            HttpClient httpClient,
            IConfiguration configuration,
            IStringLocalizer<MessageService> localizer,
            ILogger<MessageService> logger,
            ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _tokenService = tokenService;

            Env.Load();
            _messageApiUrl = Environment.GetEnvironmentVariable("MessageApiURL") ?? "http://localhost/MessageApi";
        }

        public async Task<Guid> SendMessageAsync(SendMessageRequest request)
        {
            try
            {
                _logger.LogInformation($"SendMessageAsync called with {request.Attachments?.Count ?? 0} attachments");

                // Log attachment details
                if (request.Attachments?.Any() == true)
                {
                    foreach (var attachment in request.Attachments)
                    {
                        _logger.LogInformation($"Attachment: {attachment.FileName}, ContentType: {attachment.ContentType}, Size: {attachment.FileSizeBytes}");
                    }
                }

                var apiUrl = $"{_messageApiUrl}/api/Message/send";
                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogInformation($"Sending request to: {apiUrl}");
                _logger.LogInformation($"Request payload size: {json.Length} characters");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                _logger.LogInformation($"Response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation($"Response content: {responseContent}");

                    var result = JsonSerializer.Deserialize<SendMessageResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    // Return PrimaryMessageId for multiple recipients, or MessageId for single recipient
                    var messageId = result?.PrimaryMessageId != Guid.Empty ? result.PrimaryMessageId : (result?.MessageId ?? Guid.Empty);
                    _logger.LogInformation($"Message sent successfully with ID: {messageId}");
                    return messageId;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to send message. Status: {response.StatusCode}, Content: {errorContent}");

                    // Include the actual error in the exception message for debugging
                    var detailedError = $"HTTP {response.StatusCode}: {errorContent}";
                    throw new HttpRequestException($"MessageSendFailure - {detailedError}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message");
                throw new HttpRequestException(_localizer["MessageSendFailure"]);
            }
        }

        public async Task<List<MessageModel>> GetMessagesByEmailAsync(string email)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/email/{email}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var messages = JsonSerializer.Deserialize<List<MessageModel>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return messages ?? new List<MessageModel>();
                }
                else
                {
                    _logger.LogError($"Failed to get messages for email {email}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting messages for email {email}");
                throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
            }
        }

        public async Task<List<MessageModel>> GetConversationAsync(string senderEmail, string receiverEmail)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/conversation/{senderEmail}/{receiverEmail}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var messages = JsonSerializer.Deserialize<List<MessageModel>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return messages ?? new List<MessageModel>();
                }
                else
                {
                    _logger.LogError($"Failed to get conversation between {senderEmail} and {receiverEmail}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["ConversationRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting conversation between {senderEmail} and {receiverEmail}");
                throw new HttpRequestException(_localizer["ConversationRetrievalFailure"]);
            }
        }

        public async Task<MessageModel?> GetMessageByIdAsync(Guid messageId)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var message = JsonSerializer.Deserialize<MessageModel>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return message;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }
                else
                {
                    _logger.LogError($"Failed to get message {messageId}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting message {messageId}");
                throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
            }
        }

        public async Task<List<MessageModel>> GetUnreadMessagesAsync(string email)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/unread/{email}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var messages = JsonSerializer.Deserialize<List<MessageModel>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return messages ?? new List<MessageModel>();
                }
                else
                {
                    _logger.LogError($"Failed to get unread messages for email {email}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting unread messages for email {email}");
                throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
            }
        }

        public async Task<List<MessageModel>> GetSentMessagesAsync(string email)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/sent/{email}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var messages = JsonSerializer.Deserialize<List<MessageModel>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return messages ?? new List<MessageModel>();
                }
                else
                {
                    _logger.LogError($"Failed to get sent messages for email {email}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting sent messages for email {email}");
                throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
            }
        }

        public async Task<List<MessageModel>> GetReceivedMessagesAsync(string email)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/received/{email}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var messages = JsonSerializer.Deserialize<List<MessageModel>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return messages ?? new List<MessageModel>();
                }
                else
                {
                    _logger.LogError($"Failed to get received messages for email {email}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting received messages for email {email}");
                throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
            }
        }

        public async Task<List<AttachmentModel>> GetMessageAttachmentsAsync(Guid messageId)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}/attachments";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var attachments = JsonSerializer.Deserialize<List<AttachmentModel>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return attachments ?? new List<AttachmentModel>();
                }
                else
                {
                    _logger.LogError($"Failed to get attachments for message {messageId}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["AttachmentRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachments for message {messageId}");
                throw new HttpRequestException(_localizer["AttachmentRetrievalFailure"]);
            }
        }

        public async Task<bool> MarkMessageAsReadAsync(Guid messageId)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}/read";
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to mark message {messageId} as read. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error marking message {messageId} as read");
                return false;
            }
        }

        public async Task<bool> DeleteMessageAsync(Guid messageId)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to permanently delete message {messageId}. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error permanently deleting message {messageId}");
                return false;
            }
        }

        public async Task<bool> SoftDeleteMessageAsync(Guid messageId, string deletedBy, string? reason = null)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}/soft-delete";
                var request = new
                {
                    MessageId = messageId,
                    DeletedBy = deletedBy,
                    Reason = reason
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to soft delete message {messageId}. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error soft deleting message {messageId}");
                return false;
            }
        }

        public async Task<bool> RestoreMessageAsync(Guid messageId, string restoredBy, string? reason = null)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}/restore";
                var request = new
                {
                    MessageId = messageId,
                    RestoredBy = restoredBy,
                    Reason = reason
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to restore message {messageId}. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error restoring message {messageId}");
                return false;
            }
        }

        public async Task<List<MessageModel>> GetDeletedMessagesAsync(string email)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/deleted/{email}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var messages = JsonSerializer.Deserialize<List<MessageModel>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return messages ?? new List<MessageModel>();
                }
                else
                {
                    _logger.LogError($"Failed to get deleted messages for email {email}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting deleted messages for email {email}");
                throw new HttpRequestException(_localizer["MessageRetrievalFailure"]);
            }
        }

        public async Task<AttachmentModel?> GetAttachmentAsync(Guid attachmentId)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/attachment/{attachmentId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var attachment = JsonSerializer.Deserialize<AttachmentModel>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return attachment;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }
                else
                {
                    _logger.LogError($"Failed to get attachment {attachmentId}. Status: {response.StatusCode}");
                    throw new HttpRequestException(_localizer["AttachmentRetrievalFailure"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachment {attachmentId}");
                throw new HttpRequestException(_localizer["AttachmentRetrievalFailure"]);
            }
        }

        public async Task<byte[]> DownloadAttachmentAsync(Guid attachmentId)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/attachment/{attachmentId}/download";
                _logger.LogInformation($"Downloading attachment from: {apiUrl}");

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsByteArrayAsync();
                    _logger.LogInformation($"Successfully downloaded attachment {attachmentId}, size: {content.Length} bytes");
                    return content;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to download attachment {attachmentId}. Status: {response.StatusCode}, Error: {errorContent}");
                    throw new HttpRequestException($"Download failed with status {response.StatusCode}: {errorContent}");
                }
            }
            catch (HttpRequestException)
            {
                throw; // Re-throw HTTP exceptions as-is
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error downloading attachment {attachmentId}");
                throw new HttpRequestException($"Download error: {ex.Message}", ex);
            }
        }

        public async Task<bool> DeleteAttachmentAsync(Guid attachmentId)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/attachment/{attachmentId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return false;
                }
                else
                {
                    _logger.LogError($"Failed to delete attachment {attachmentId}. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting attachment {attachmentId}");
                return false;
            }
        }

        public async Task<bool> ArchiveMessageAsync(Guid messageId, string archivedBy)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}/archive";
                var request = new
                {
                    MessageId = messageId,
                    ArchivedBy = archivedBy
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to archive message {messageId}. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error archiving message {messageId}");
                return false;
            }
        }

        public async Task<bool> UnarchiveMessageAsync(Guid messageId, string unarchivedBy)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}/unarchive";
                var request = new
                {
                    MessageId = messageId,
                    UnarchivedBy = unarchivedBy
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to unarchive message {messageId}. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error unarchiving message {messageId}");
                return false;
            }
        }

        public async Task<bool> ToggleImportantAsync(Guid messageId, bool isImportant, string updatedBy)
        {
            try
            {
                var apiUrl = $"{_messageApiUrl}/api/Message/{messageId}/toggle-important";
                var request = new
                {
                    MessageId = messageId,
                    IsImportant = isImportant,
                    UpdatedBy = updatedBy
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };

                var accessToken = _tokenService.AccessToken;
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to toggle important status for message {messageId}. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error toggling important status for message {messageId}");
                return false;
            }
        }
    }


    public class SendMessageResponse
    {

        public Guid MessageId { get; set; }


        public List<Guid> MessageIds { get; set; } = new List<Guid>();
        public Guid PrimaryMessageId { get; set; }
        public int RecipientCount { get; set; }

        public string Status { get; set; } = string.Empty;
        public int AttachmentCount { get; set; }
    }
}
