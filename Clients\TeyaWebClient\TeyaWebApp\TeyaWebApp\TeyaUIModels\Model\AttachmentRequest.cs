using System.ComponentModel.DataAnnotations;

namespace TeyaUIModels.Model
{
    public class AttachmentRequest : IModel
    {
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string ContentType { get; set; } = string.Empty;

        [Required]
        public string FileContentBase64 { get; set; } = string.Empty;

        public long FileSizeBytes { get; set; }
    }
}
