﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class VitalController : ControllerBase
    {
        private readonly IVitalCommandHandler<Vitals> _vitalDataHandler;
        private readonly IVitalQueryHandler<Vitals> _vitalQueryHandler;
        private readonly ILogger<VitalController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public VitalController(
            IVitalCommandHandler<Vitals> vitalDataHandler,
            IVitalQueryHandler<Vitals> vitalQueryHandler,
            ILogger<VitalController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _vitalDataHandler = vitalDataHandler;
            _vitalQueryHandler = vitalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get all by ID
        /// </summary>


        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<Vitals>>> GetAllById(Guid id)
        {
            ActionResult result;
            try
            {
                var vitals = await _vitalQueryHandler.GetAllVitalsbyId(id);
                result = vitals != null ? Ok(vitals) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get only Vitals for an ID
        /// </summary>


        [HttpGet("{id:guid}/isActive")]
        public async Task<ActionResult<IEnumerable<Vitals>>> GetAllByIdAndIsActive(Guid id)
        {
            ActionResult result;
            try
            {
                var vitals = await _vitalQueryHandler.GetVitalsByIdAndIsActive(id);
                result = vitals != null ? Ok(vitals) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add List Of new Vitals
        /// </summary>

        [HttpPost]
        [Route("AddVitals")]
        public async Task<IActionResult> Registration([FromBody] List<Vitals> vitals)
        {
            if (vitals == null || vitals.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _vitalDataHandler.AddVital(vitals);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete Vitals
        /// </summary>

        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] Vitals vital)
        {
            IActionResult result;
            if (vital == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _vitalDataHandler.DeleteVitalByEntity(vital);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update Single Vital By PatientID
        /// </summary>

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateVitalById(Guid id, [FromBody] Vitals Vital)
        {
            IActionResult result;
            if (Vital == null || Vital.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _vitalDataHandler.UpdateVital(Vital);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update List of Vitals
        /// </summary>

        [HttpPut]
        [Route("UpdateVitalsList")]
        public async Task<IActionResult> UpdateVitalList([FromBody] List<Vitals> vitals)
        {
            IActionResult result;

            try
            {
                await _vitalDataHandler.UpdateVitalList(vitals);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }
    }
}