﻿namespace EncounterNotesContracts
{
    public class ChiefComplaint : IContract
    {
        public Guid Id { get; set; }
        public Guid PatientId { get; set; }  
        public string Description { get; set; }
        public DateTime DateOfComplaint { get; set; }
        public Guid OrganizationId { get; set; }  
        public Guid? PcpId { get; set; }  
        public bool IsDeleted { get; set; } = false;  
    }
}

