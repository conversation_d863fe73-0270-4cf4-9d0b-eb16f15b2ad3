﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using TeyaWebApp.Model;
using TeyaWebApp.ViewModel;
using DotNetEnv;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using TeyaWebApp.TeyaAIScribeResources;
public class AppointmentService : IAppointmentService
{
    private readonly HttpClient _httpClient;
    private readonly IStringLocalizer<TeyaAIScribeStrings> _localizer;
    private readonly string _AppointmentsUrl;

    public AppointmentService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaAIScribeStrings> localizer)
    {
        _httpClient = httpClient;
        _localizer = localizer;
        Env.Load();
        _AppointmentsUrl = Environment.GetEnvironmentVariable("AppointmentsUrl");
    }

    public async Task<List<Appointment>> GetAppointmentsAsync(DateTime date)
    {
        var dateUrl = $"{_AppointmentsUrl}/{date:yyyy-MM-dd}";
        var response = await _httpClient.GetAsync(dateUrl);

        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadFromJsonAsync<List<Appointment>>();
        }
        else
        {
            throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
        }
    }
    public async Task CreateAppointmentsAsync(List<Appointment> appointments)
    {
        var bodyContent = JsonConvert.SerializeObject(appointments);

        var response = await _httpClient.PostAsync(Environment.GetEnvironmentVariable("AppointmentRegistration"), new StringContent(bodyContent, Encoding.UTF8, "application/json"));

        if (response.IsSuccessStatusCode)
        {
            var responseBody = await response.Content.ReadAsStringAsync();
        }
        else
        {
            throw new HttpRequestException(_localizer["AppointmentRetrievalFailure"]);
        }
    }
    public async Task UpdateAppointmentAsync(Appointment appointment)
    {
        var response = await _httpClient.PutAsJsonAsync($"{_AppointmentsUrl}/{appointment.Id}", appointment);
        response.EnsureSuccessStatusCode();
    }
    public async Task DeleteAppointmentAsync(Guid appointmentId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_AppointmentsUrl}/{appointmentId}");
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            throw new Exception(_localizer["Error"], ex);
        }
    }


}