﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    /// <summary>
    /// This is the Controller to handle the request to display and modify the templates
    /// </summary>
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class TemplatesController : ControllerBase
    {
        private readonly ITemplatesCommandHandler<Templates> _templatesDataHandler;
        private readonly ITemplatesQueryHandler<Templates> _templatesQueryHandler;
        private readonly ILogger<TemplatesController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        /// <summary>
        /// This is a Templates Controller Constructor
        /// </summary>
        /// <param name="dataHandler"></param>
        /// <param name="queryHandler"></param>
        /// <param name="logger"></param>
        /// <param name="localizer"></param>
        public TemplatesController(
            ITemplatesCommandHandler<Templates> dataHandler,
            ITemplatesQueryHandler<Templates> queryHandler,
            ILogger<TemplatesController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _templatesDataHandler = dataHandler;
            _templatesQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer;
        }
        /// <summary>
        /// Get method to get all the templates
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Templates>>> Get()
        {
            ActionResult result;
            try
            {
                var records = await _templatesQueryHandler.GetTemplates();
                result = Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }
        /// <summary>
        /// Get method to display all the templates for the particular id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}")]
        public async Task<ActionResult<Templates>> GetById(Guid id)
        {
            ActionResult result;
            try
            {
                var templates = await _templatesQueryHandler.GetTemplatesById(id);
                result = templates != null ? Ok(templates) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }
        /// <summary>
        /// Updated the templates for a particular user
        /// </summary>
        /// <param name="id"></param>
        /// <param name="templates"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> UpdateData([FromBody] List<Templates> templates)
        {
            IActionResult result;

            try
            {
                await _templatesDataHandler.UpdateTemplates(templates);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }

        [HttpGet("ByPCP/{pcpId:guid}")]
        public async Task<ActionResult<IEnumerable<Record>>> GetByPCPId(Guid pcpId)
        {
            ActionResult result;
            try
            {
                var templates = await _templatesQueryHandler.GetTemplatesByPCPId(pcpId);
                result = templates != null && templates.Any() ? Ok(templates) : NotFound(_localizer["TemplateNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("ByVisit/{pcpId:guid}/{VisitType}")]
        public async Task<ActionResult<IEnumerable<Record>>> GetByPCPIdAndVisitType(Guid pcpId,String VisitType)
        {
            ActionResult result;
            try
            {
                var templates = await _templatesQueryHandler.GetTemplatesByPCPIdAndVisitType(pcpId, VisitType);
                result = templates != null && templates.Any() ? Ok(templates) : NotFound(_localizer["TemplateNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpDelete("{id:guid}")]
        public async Task<IActionResult> DeleteById(Guid id)
        {
            try
            {
                await _templatesDataHandler.DeleteTemplatesById(id);
                return Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }
        /// <summary>
        /// Adding the new templates
        /// </summary>
        /// <param name="templates"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Templates")]
        public async Task<IActionResult> EncounterNote([FromBody] List<Templates> templates)
        {
            IActionResult result;
            try
            {

                await _templatesDataHandler.AddTemplates(templates);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, _localizer["DatabaseError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["PostLogError"]);
            }
            return result;
        }

    }
}
