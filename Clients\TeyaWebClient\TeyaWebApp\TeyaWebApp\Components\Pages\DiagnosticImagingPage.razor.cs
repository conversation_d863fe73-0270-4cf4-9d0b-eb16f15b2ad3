﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Http.HttpResults;
using TeyaWebApp.Components.Layout;
using Syncfusion.Windows.Shared;
using Microsoft.Extensions.Localization;
using TeyaWebApp.TeyaAIScribeResources;

namespace TeyaWebApp.Components.Pages
{
    public partial class DiagnosticImagingPage : ComponentBase
    {
        private List<AssessmentsData> assessmentData { get; set; }
        private DiagnosticImagingDTO DiagnosticImageData = new();
        private SfGrid<AssessmentsData> AssessmentGrid;
        private List<AssessmentsData> SelectedAssessments = new();
        private List<Guid> AssessmentIDs = new();
        private List<DiagnosticImagingDTO> DiagnosticImagingList = new();
        bool isDialogOpen = true;
        private string Reviewed;
        private string Open;
        private Guid PatientID { get; set; }
        private Guid? organizationId { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                Reviewed = Localizer["Reviewed"];
                Open = Localizer["Open"];
                PatientID = _PatientService.PatientData.Id;
                organizationId = _PatientService.PatientData.OrganizationID;
                assessmentData = await AssessmentsService.GetAllByIdAndIsActiveAsync(PatientID);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        /// <summary>
        /// To Handle click Outside the SF Grid
        /// </summary>
        private void HandleSelectionChange(string newValue)
        {
            DiagnosticImageData.Status = newValue;
        }

        private async Task SaveChanges()
        {
            if (string.IsNullOrWhiteSpace(DiagnosticImageData.Status) ||
               string.IsNullOrWhiteSpace(DiagnosticImageData.Provider) ||
               string.IsNullOrWhiteSpace(DiagnosticImageData.Facility) ||
                string.IsNullOrWhiteSpace(DiagnosticImageData.AssignedTo) ||
                string.IsNullOrWhiteSpace(DiagnosticImageData.Facility) ||
                string.IsNullOrWhiteSpace(DiagnosticImageData.Procedgure) ||
                !DiagnosticImageData.OrderDate.HasValue ||
               !DiagnosticImageData.Date.HasValue ||
                string.IsNullOrWhiteSpace(DiagnosticImageData.Result) ||
                string.IsNullOrWhiteSpace(DiagnosticImageData.Reason))
            {
                Snackbar.Add("Please enter all required fields.", Severity.Warning);
                return;
            }
            SelectedAssessments = await AssessmentGrid.GetSelectedRecordsAsync();
            DiagnosticImageData.PatientId = PatientID;
            DiagnosticImageData.OrganizationID = organizationId;
            DiagnosticImageData.CreatedDate = DateTime.Now;
            DiagnosticImageData.UpdatedDate = DateTime.Now;
            DiagnosticImageData.CreatedBy = Guid.Parse(User.id);
            DiagnosticImageData.UpdatedBy = PatientID;
            DiagnosticImageData.RecordID = Guid.NewGuid();
            DiagnosticImageData.IsActive = true;

            if (SelectedAssessments.Any())
            {
                DiagnosticImageData.Assessments = SelectedAssessments
                    .Select(a => new DiagnosticImagingAssessment
                    {
                        ID = Guid.NewGuid(),
                        DiagnosticImagingID = DiagnosticImageData.RecordID,
                        AssessmentID = a.AssessmentsID
                    })
                    .ToList();
            }
            await DiagnosticImagingPageService.CreateDiagnosticImagingAsync(new List<DiagnosticImagingDTO> { DiagnosticImageData });
            DiagnosticImagingList = await DiagnosticImagingPageService.GetDiagnosticImagingByIdAsyncAndIsActive(PatientID);
            AssessmentGrid.ClearRowSelectionAsync();
            DiagnosticImageData = new DiagnosticImagingDTO();
            StateHasChanged();
            await InvokeAsync(StateHasChanged);
        }
        /// <summary>
        /// Cancel all edit, delete and add operations and close the dialogBox
        /// </summary>
        private async Task CancelChanges()
        {
            DiagnosticImageData = new();
            AssessmentGrid.ClearRowSelectionAsync();
            StateHasChanged();
            await InvokeAsync(StateHasChanged);
        }
    }
}