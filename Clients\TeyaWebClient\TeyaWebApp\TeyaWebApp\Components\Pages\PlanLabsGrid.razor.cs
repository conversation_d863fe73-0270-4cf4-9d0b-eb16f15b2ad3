﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class PlanLabsGrid : ComponentBase
    {
        [Parameter] public Guid SelectedAssessmentId { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] SharedNotesService SharedNotesService { get; set; }
        private DateTime? _CreatedDate = DateTime.Now;
        private DateTime? _UpdatedDate = DateTime.Now;

        private List<LabTests> planlabs { get; set; }
        private List<LabTests> planlabsRelatedToAssessments { get; set; } = new List<LabTests>();
        public SfGrid<LabTests> PlanLabsGridRef { get; set; }

        public Guid PatientId { get; set; }
        public List<LabTests> deleteList = new List<LabTests>();
        public List<LabTests> AddList = new List<LabTests>();
        public List<AssessmentsData> Localdata { get; set; } = new List<AssessmentsData>();
        private List<string> AssessmentDiagnosis = new List<string>();
        [Parameter] public EventCallback OnChange { get; set; }
        private void NotifyParentOfChanges()
        {
           
            OnChange.InvokeAsync();
        }
        protected override async Task OnInitializedAsync()
        {
            PatientId = _PatientService.PatientData.Id;
            Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientId))
                .GroupBy(a => a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
            AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();
            planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
            planlabsRelatedToAssessments = planlabs
            .Where(lab => lab.AssessmentId == SelectedAssessmentId)
            .ToList();
            SharedNotesService.OnChange += UpdateAssessments;
        }
        private List<string> OrganizationOptions => new List<string>
        {
            Localizer["Quest Inc"].Value,
            Localizer["Lab Corp"].Value
        };
        private RenderFragment<object> OrganizationEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests labTest) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", OrganizationOptions);
            builder.AddAttribute(2, "Value", labTest.TestOrganization);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    labTest.TestOrganization = value;
                }));
            builder.AddAttribute(4, "Placeholder", "Select Organization");
            builder.CloseComponent();
        };
        private void UpdateAssessments()
        {
            OnInitializedAsync();
            StateHasChanged();  
        }
        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests Labs) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", Labs.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    Labs.AssessmentData = value;
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        Labs.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(Labs.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");
            builder.CloseComponent();
        };

        private async void AddNewHistory()
        {
            var newHistory = new LabTests
            {
                LabTestsId = Guid.NewGuid(),
                PatientId = PatientId,
                PcpId = PatientId,
                OrganizationId = PatientId,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                IsActive = true,
                AssessmentId = SelectedAssessmentId, 
                AssessmentData = Localdata.FirstOrDefault(a => a.AssessmentsID == SelectedAssessmentId)?.Diagnosis
            };

            AddList.Add(newHistory);
            planlabsRelatedToAssessments.Add(newHistory);
            await PlanLabsGridRef.Refresh();
            ResetInputFields();
        }

        private void ResetInputFields()
        {
            _CreatedDate = null;
        }

        public void ActionCompletedHandler(Syncfusion.Blazor.Grids.ActionEventArgs<LabTests> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleteLabTest = args.Data as LabTests;
                var existingItem = AddList.FirstOrDefault(m => m.LabTestsId == deleteLabTest.LabTestsId);
                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
        }

        public void ActionBeginHandler(Syncfusion.Blazor.Grids.ActionEventArgs<LabTests> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        private async Task CancelData()
        {
            deleteList.Clear();
            AddList.Clear();
            planlabsRelatedToAssessments = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);

            ResetInputFields();
            OnInitializedAsync();
            NotifyParentOfChanges();
            await PlanLabsGridRef.Refresh();
            await InvokeAsync(StateHasChanged);
        }

        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _labTestsService.AddLabTestsAsync(AddList);
            }
            await _labTestsService.UpdateLabTestsListAsync(planlabsRelatedToAssessments);
            await _labTestsService.UpdateLabTestsListAsync(deleteList);
            deleteList.Clear();
            AddList.Clear();
            planlabsRelatedToAssessments = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
            NotifyParentOfChanges();
            OnInitializedAsync();
            await InvokeAsync(StateHasChanged);
        }
    }
}
