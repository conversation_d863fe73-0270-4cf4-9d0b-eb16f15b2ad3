# Test attachment retrieval with known message ID
Write-Host "Testing attachment retrieval..." -ForegroundColor Green

$baseUrl = "http://localhost:5000"
$messageId = "108fd813-4f09-46bf-81fc-d40bb2052462"  # From the logs

Write-Host "Testing with Message ID: $messageId"

try {
    # Get attachments
    Write-Host "Getting attachments for message $messageId..."
    $attachments = Invoke-RestMethod -Uri "$baseUrl/api/message/$messageId/attachments" -Method GET
    
    Write-Host "Found $($attachments.Count) attachment(s)" -ForegroundColor Green
    
    if ($attachments.Count -gt 0) {
        $attachment = $attachments[0]
        Write-Host "Attachment Details:" -ForegroundColor Cyan
        Write-Host "  ID: $($attachment.attachmentId)"
        Write-Host "  Original Name: $($attachment.originalFileName)"
        Write-Host "  Blob Name: $($attachment.blobFileName)"
        Write-Host "  Size: $($attachment.fileSizeBytes) bytes"
        Write-Host "  Content Type: $($attachment.contentType)"
        Write-Host "  Blob URL: $($attachment.blobStorageUrl)"
        Write-Host "  Container: $($attachment.blobContainerName)"
        
        # Test download
        Write-Host "Downloading attachment..." -ForegroundColor Yellow
        $attachmentId = $attachment.attachmentId
        
        $downloadResponse = Invoke-RestMethod -Uri "$baseUrl/api/message/attachment/$attachmentId/download" -Method GET
        
        $downloadedFile = "downloaded-test-attachment.txt"
        $downloadResponse | Out-File -FilePath $downloadedFile -Encoding UTF8
        
        Write-Host "Downloaded to: $downloadedFile" -ForegroundColor Green
        Write-Host "SUCCESS! Azure Blob Storage attachment system is working!" -ForegroundColor Green
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
