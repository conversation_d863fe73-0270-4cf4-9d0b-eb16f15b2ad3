﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class MedicalHistoryCommandHandler : IMedicalHistoryCommandHandler<MedicalHistory>
    {
        private readonly IUnitOfWork _unitOfWork;

        public MedicalHistoryCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddMedicalHistory(List<MedicalHistory> medicalHistories)
        {
            await _unitOfWork.MedicalHistoryRepository.AddAsync(medicalHistories);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateMedicalHistory(MedicalHistory medicalHistory)
        {
            await _unitOfWork.MedicalHistoryRepository.UpdateAsync(medicalHistory);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteMedicalHistoryById(Guid id)
        {
            await _unitOfWork.MedicalHistoryRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteMedicalHistoryByEntity(MedicalHistory medicalHistory)
        {
            await _unitOfWork.MedicalHistoryRepository.DeleteByEntityAsync(medicalHistory);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateMedicalHistoryList(List<MedicalHistory> medicalHistories)
        {
            await _unitOfWork.MedicalHistoryRepository.UpdateRangeAsync(medicalHistories);
            await _unitOfWork.SaveAsync();
        }
    }
}