﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IProductCommandHandler<TText>
    {
        Task DeleteProductByEntity(Product product);
        Task DeleteProductById(Guid id);
        Task AddProduct(List<TText> texts);
        Task UpdateProduct(Product product);
    }
}


