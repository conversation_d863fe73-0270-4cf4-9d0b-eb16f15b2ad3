﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class ChiefComplaint
    {
        [Inject]
        private ILogger<ChiefComplaintDTO> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }

        public string Name { get; set; }
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        public List<string> DistinctComplaintSuggestions { get; private set; } = new();

        private List<ChiefComplaintDTO> complaints = new();        
        private List<ChiefComplaintDTO> addedComplaints = new();
        private List<ChiefComplaintDTO> updatedComplaints = new();
        private SfRichTextEditor richTextEditor;
        private MudDialog showBrowsePopup;
        private SfGrid<ChiefComplaintDTO> ComplaintsGrid;
        private string richTextContent = string.Empty;
        private string complaintDescription = string.Empty;
        private List<ChiefComplaintDTO> deleteList = new();
        private bool isInternalUpdate { get; set; } = false;
        private Guid PatientId { get; set; }

        [Parameter]
        public Guid PatientID { get; set; }
        [Parameter]
        public Guid OrgId { get; set; }
        [Parameter]
        public string? Data { get; set; }

        [Parameter]
        public string? TotalText { get; set; }

        [Parameter]
        public EventCallback<string> OnValueChanged { get; set; }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = PatientID;
                
                await LoadComplaintsAsync();
                richTextContent = TotalText;
                await InvokeAsync(StateHasChanged);
                LocalData = await ChiefComplaintService.GetProcessedComplaintsAsync();

            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Initialization error");
            }
        }

        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };

        private async Task AddComplaint()
        {
            if (string.IsNullOrWhiteSpace(complaintDescription))
            {
                Logger.LogWarning("Please enter a complaint description.");
                return;
            }

            var newComplaint = new ChiefComplaintDTO
            {
                Id = Guid.NewGuid(),
                PatientId = PatientId,
                Description = complaintDescription,
                DateOfComplaint = DateTime.Now,
                OrganizationId = OrgId,
                PcpId = Guid.Parse(User.id),
                IsDeleted = false
            };

            try
            {
                complaints.Add(newComplaint);
                addedComplaints.Add(newComplaint);
                await ComplaintsGrid.Refresh();


                var exists = LocalData.Any(c =>
                    c.Description.Equals(complaintDescription, StringComparison.OrdinalIgnoreCase));

                if (!exists)
                {
                    LocalData.Add(newComplaint);
                    await InvokeAsync(StateHasChanged);
                }

                complaintDescription = string.Empty;
                Logger.LogInformation("Complaint added successfully.");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error adding complaint");
            }
        }

        private string GenerateRichTextContent(string Data)
        {
            string complaintsContent =string.Join(" ",
            complaints.OrderByDescending(c => c.DateOfComplaint)
              .Select(c => $"<ul><li style='margin-left: 20px;'><b>{c.DateOfComplaint:yyyy-MM-dd}</b> : {c.Description}</li></ul>"));
            return $@"<div>
            <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
            {Data}
            <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
            {complaintsContent}
            </div>";
        }


        public void ActionBeginHandler(ActionEventArgs<ChiefComplaintDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                args.Data.IsDeleted = true;
                deleteList.Add(args.Data);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!addedComplaints.Contains(args.Data) && !updatedComplaints.Contains(args.Data))
                {
                    updatedComplaints.Add(args.Data);
                }
            }


        }
        private async Task HandleRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            richTextContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            richTextContent = GenerateRichTextContent(Data);
            await InvokeAsync(StateHasChanged);
           
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(richTextContent);
            }
           
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task SaveChanges()
        {
            try
            {
                if (addedComplaints.Any())
                {
                    foreach (var newEntry in addedComplaints)
                    {
                        await ChiefComplaintService.AddAsync(newEntry);
                    }
                    addedComplaints.Clear();
                }
                if (updatedComplaints.Any())
                {
                    await ChiefComplaintService.UpdateComplaintListAsync(updatedComplaints);
                    updatedComplaints.Clear();
                }

                if (deleteList.Any())
                {
                    await ChiefComplaintService.UpdateComplaintListAsync(deleteList);
                    deleteList.Clear();
                }

                await LoadComplaintsAsync();
                await HandleDynamicComponentUpdate();
                CloseBrowsePopup();

                Logger.LogInformation("Changes saved successfully.");
                
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving changes");
            }
        }

        private async Task CancelChanges()
        {
            deleteList.Clear();
            addedComplaints.Clear();
            updatedComplaints.Clear();
            await LoadComplaintsAsync();
            CloseBrowsePopup();
            Logger.LogInformation("Changes canceled.");
        }


        private async Task LoadComplaintsAsync()
        {
            try
            {
                complaints = await ChiefComplaintService.LoadComplaintsAsync(PatientId);

                if (ComplaintsGrid != null)
                    await ComplaintsGrid.Refresh();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading complaints");
            }
        }

        private void CloseBrowsePopup()
        {
            complaintDescription = string.Empty;
            showBrowsePopup?.CloseAsync();
        }

        private async Task OpenBrowsePopupAsync()
        {
            showBrowsePopup.ShowAsync();
            if (ComplaintsGrid != null)
                await ComplaintsGrid.Refresh();

        }
    }
}