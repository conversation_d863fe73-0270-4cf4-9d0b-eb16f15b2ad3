﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;


namespace TeyaWebApp.Components.Pages
{
    public partial class Vitals : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject]
        private PatientService _PatientService { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }

        [Inject] IVitalService _VitalService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        private string editorContent;
        private MudDialog _vitalsdialog;
        private SfRichTextEditor RichTextEditor;
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };
        private List<string> ToolbarItems = new List<string> { "Add" };
        public string temperature { get; set; }
        public string weight { get; set; }
        public string height { get; set; }
        public string pulse { get; set; }
        private bool isInternalUpdate { get; set; } = false;

        public string blood_Pressure { get; set; }
        public SfGrid<PatientVitals> VitalsGrid { get; set; }
        private List<PatientVitals> vitals { get; set; }
        private List<PatientVitals> AddList = new();
        private List<PatientVitals> DeleteList = new();
        [Parameter]
        public Guid PatientID { get; set; }
        [Parameter]
        public Guid OrgId { get; set; }
        [Parameter]
        public string? Data { get; set; }

        [Parameter]
        public string? TotalText { get; set; }

        [Parameter]
        public EventCallback<string> OnValueChanged { get; set; }
        /// <summary>
        /// Open Edit Dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _vitalsdialog.ShowAsync();
        }

        /// <summary>
        /// Close Edit Dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _vitalsdialog.CloseAsync();
        }
        bool add = false;

        /// <summary>
        /// Retrieve Vitals and set rich text editor
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            vitals = await _VitalService.GetVitalsByIdAsyncAndIsActive(PatientID);
            var vitalsContent = string.Join("<p>",
            vitals.OrderByDescending(v => v.CreatedDate)
            .Select(v => $" Created Date: {v.CreatedDate} Temperature: {v.Temperature}, Blood Pressure: {v.BP}, Pulse: {v.Pulse}, Height: {v.Height}, Weight: {v.Weight}"));
            editorContent = $@"<div>
            <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
            {Data}
            <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
            {vitalsContent}
            </div>";
        }

        /// <summary>
        /// Event Handler for add,edit,delete
        /// </summary>
        /// <param name="args"></param>
        private void ActionCompletedHandler(ActionEventArgs<PatientVitals> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedVitals = args.Data as PatientVitals;
                    var existingItem = AddList.FirstOrDefault(v => v.VitalId == deletedVitals.VitalId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedVitals.isActive = false;
                        deletedVitals.UpdatedBy = Guid.Parse(User.id);
                        deletedVitals.UpdatedDate = DateTime.Now;    
                        DeleteList.Add(deletedVitals);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.VitalId = Guid.NewGuid();
                args.Data.PatientId = PatientID;
                args.Data.OrganizationId = OrgId;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.isActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedVitals = args.Data;
                        if (addedVitals != null)
                        {
                            AddList.Add(addedVitals); // Add to AddList
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Handle BackdropClick
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Save changes to database
        /// </summary>
        /// <returns></returns>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await _VitalService.AddVitalAsync(AddList);
            }
            await _VitalService.UpdateVitalsListAsync(DeleteList);
            await _VitalService.UpdateVitalsListAsync(vitals);
            AddList.Clear();
            DeleteList.Clear();
            isInternalUpdate = true;
            var vitalsContent = string.Join("<p>",
            vitals.OrderByDescending(v => v.CreatedDate)
            .Select(v => $"Temperature: {v.Temperature}, Blood Pressure: {v.BP}, Pulse: {v.Pulse}, Height: {v.Height}, Weight: {v.Weight}"));
            editorContent = $@"<div>
            <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
            {Data}
            <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
            {vitalsContent}
            </div>";
            await InvokeAsync(StateHasChanged);
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }
            CloseAddTaskDialog();
        }
        private async Task HandleRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Undo changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelChanges()
        {

            DeleteList.Clear();
            AddList.Clear();
            vitals = await _VitalService.GetVitalsByIdAsyncAndIsActive(PatientID);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }
    }
}