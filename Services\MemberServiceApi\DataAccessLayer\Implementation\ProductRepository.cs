﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class ProductRepository : GenericRepository<Product>, IProductRepository
    {
        private readonly AccountDatabaseContext _context;

        public ProductRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }

    }
}
