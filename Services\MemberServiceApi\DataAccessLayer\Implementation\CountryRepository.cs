﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class CountryRepository : ICountryRepository
    {
        private readonly AccountDatabaseContext _context;

        public CountryRepository(AccountDatabaseContext context)
        {
            _context = context;
        }

        public async Task AddAsync(Country country)
        {
            await _context.Countries.AddAsync(country);
        }

        public async Task UpdateAsync(Country country)
        {
            _context.Countries.Update(country);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var country = await _context.Countries.FindAsync(id);
            if (country != null)
            {
                _context.Countries.Remove(country);
            }
        }

        public async Task<Country> GetByIdAsync(Guid id)
        {
            var result = await _context.Countries.FindAsync(id);
            return result;
        }

        public async Task<List<Country>> GetAllAsync()
        {
            var result = await _context.Countries.ToListAsync();
            return result;
        }

        public async Task<List<Country>> GetByNameAsync(string name)
        {
            var result = await _context.Countries
                .Where(c => c.CountryName.Contains(name, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }
    }
}
