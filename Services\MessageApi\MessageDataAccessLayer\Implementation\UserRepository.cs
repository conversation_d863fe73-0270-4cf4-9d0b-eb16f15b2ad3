using DataAccessLayer.Context;
using MessageContracts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public class UserRepository : GenericRepository<User>, IUserRepository
    {
        public UserRepository(MessageApiDatabaseContext context) : base(context)
        {
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.EmailId.ToLower() == email.ToLower());
        }

        public async Task<User?> GetByAcsUserIdAsync(string acsUserId)
        {
            return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.AcsUserId == acsUserId);
        }

        public async Task<User> GetOrCreateUserAsync(string email, string? name = null)
        {
            var existingUser = await GetByEmailAsync(email);
            if (existingUser != null)
            {
                // Return existing user without updating to avoid circular dependency
                return existingUser;
            }

            // Create new user
            var newUser = new User
            {
                UserId = Guid.NewGuid(),
                EmailId = email,
                Name = name ?? ExtractNameFromEmail(email),
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                LastActiveDateTime = DateTime.UtcNow,
                IsActive = true
            };

            await AddAsync(new[] { newUser });
            return newUser;
        }

        public async Task UpdateAcsUserIdAsync(Guid userId, string acsUserId)
        {
            // Use direct SQL update to avoid Entity Framework tracking conflicts
            await _context.Database.ExecuteSqlRawAsync(
                "UPDATE [MessageService].[User] SET [AcsUserId] = {0}, [UpdatedDate] = {1} WHERE [UserId] = {2}",
                acsUserId, DateTime.UtcNow, userId);
        }

        public async Task UpdateAcsTokenAsync(Guid userId, string accessToken, DateTime expiryTime)
        {
            // Use direct SQL update to avoid Entity Framework tracking conflicts
            await _context.Database.ExecuteSqlRawAsync(
                "UPDATE [MessageService].[User] SET [AcsAccessToken] = {0}, [AcsTokenExpiryTime] = {1}, [UpdatedDate] = {2} WHERE [UserId] = {3}",
                accessToken, expiryTime, DateTime.UtcNow, userId);
        }

        private string ExtractNameFromEmail(string email)
        {
            // Extract name from email (e.g., "<EMAIL>" -> "John Doe")
            var localPart = email.Split('@')[0];
            var nameParts = localPart.Split('.', '_', '-');

            for (int i = 0; i < nameParts.Length; i++)
            {
                if (nameParts[i].Length > 0)
                {
                    nameParts[i] = char.ToUpper(nameParts[i][0]) + nameParts[i].Substring(1).ToLower();
                }
            }

            return string.Join(" ", nameParts);
        }
    }
}
