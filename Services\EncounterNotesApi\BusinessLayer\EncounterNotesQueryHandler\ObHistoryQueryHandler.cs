﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;



namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ObHistoryQueryHandler : IObHistoryQueryHandler<ObHistory>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        public ObHistoryQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public Task<IEnumerable<ObHistory>> GetAllAsync()
        {
            var getAll = _unitOfWork.ObHistoryRepository.GetAllAsync();
            return getAll;
        }

        public Task<ObHistory> GetByIdAsync(Guid id)
        {
            var getById = _unitOfWork.ObHistoryRepository.GetByIdAsync(id);
            return getById;
        }

        public Task<IEnumerable<ObHistory>> GetByPatientIdAsync(Guid patientId)
        {
            var getByPatientId = _unitOfWork.ObHistoryRepository.GetByPatientIdAsync(patientId);
            return getByPatientId;
        }


    }
}
