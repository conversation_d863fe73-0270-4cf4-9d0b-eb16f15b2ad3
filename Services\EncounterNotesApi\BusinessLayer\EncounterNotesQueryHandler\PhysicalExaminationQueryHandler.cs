﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class PhysicalExaminationQueryHandler : IPhysicalExaminationQueryHandler<PhysicalExamination>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PhysicalExaminationQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PhysicalExamination>> GetAllExaminationsById(Guid id)
        {
            var examination = await _unitOfWork.PhysicalExaminationRepository.GetAllExaminationsAsync();

            return examination.Where(PhysExamination => PhysExamination.PatientId == id);
        }
        public async Task<IEnumerable<PhysicalExamination>> GetExaminationByIdAndIsActive(Guid id)
        {
            var examinations = await _unitOfWork.PhysicalExaminationRepository.GetAsync();

            return examinations.Where(PhysExamination => PhysExamination.PatientId == id && PhysExamination.IsActive == true);
        }
    }
}