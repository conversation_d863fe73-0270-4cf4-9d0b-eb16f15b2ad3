using System;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class PageRoleMappingCommandHandler : IPageRoleMappingCommandHandler<PageRoleMapping>
    {
        private readonly IUnitOfWork _unitOfWork;

        public PageRoleMappingCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddPageRoleMappingAsync(PageRoleMapping pageRoleMapping)
        {
            if (pageRoleMapping == null)
                throw new ArgumentNullException(nameof(pageRoleMapping));

            await _unitOfWork.PageRoleMappingRepository.AddAsync(pageRoleMapping);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdatePageRoleMappingAsync(PageRoleMapping pageRoleMapping)
        {
            if (pageRoleMapping == null)
                throw new ArgumentNullException(nameof(pageRoleMapping));

            await _unitOfWork.PageRoleMappingRepository.UpdateAsync(pageRoleMapping);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeletePageRoleMappingAsync(Guid id)
        {
            await _unitOfWork.PageRoleMappingRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
