using Plugin.Maui.Audio;

namespace TeyaAiScribeMobile
{
    public partial class MainPage : ContentPage
    {
        readonly IAudioManager _audioManager;
        readonly IAudioRecorder _audioRecorder;
        IAudioPlayer _audioPlayer; // To play the audio after recording
        IAudioSource _audioSource; // To store the recorded audio source

        public MainPage(IAudioManager audioManager)
        {
            InitializeComponent();

            _audioManager = audioManager;
            _audioRecorder = audioManager.CreateRecorder();
            Shell.Current.FlyoutBehavior = FlyoutBehavior.Flyout;
            BindingContext = this;  // Set the BindingContext to allow command binding in the XAML

        }
        private void OnFlyoutButtonClicked(object sender, EventArgs e)
        {
            Shell.Current.FlyoutIsPresented = !Shell.Current.FlyoutIsPresented;
        }
        public Command UploadCommand => new Command(OnUpload);
        private async void OnUpload()
        {
            try
            {
                // Open the file picker
                var result = await FilePicker.PickAsync();

                // Check if a file was selected
                if (result != null)
                {
                    // You can access the file's path, name, and other properties here
                    string filePath = result.FullPath;
                    string fileName = result.FileName;

                    // Example: Display the selected file path
                    await DisplayAlert("File Selected", $"You selected: {fileName}\nPath: {filePath}", "OK");

                    // Now, you can upload or process the file as needed
                }
                else
                {
                    // Handle the case when no file was selected
                    await DisplayAlert("No file selected", "Please select a file to upload.", "OK");
                }
            }
            catch (Exception ex)
            {
                // Handle any errors that may occur
                await DisplayAlert("Error", $"An error occurred: {ex.Message}", "OK");
            }
        }
        private void OnPauseClicked(object sender, EventArgs e)
        {
            // Add logic for pause action here
        }

        // Empty method for Resume button click
        private void OnResumeClicked(object sender, EventArgs e)
        {
            // Add logic for resume action here
        }
        private void OnStopClicked(object sender, EventArgs e)
        {
            // Add logic for stopping the microphone or the conversation capture process
            DisplayAlert("Stopped", "The conversation has been stopped.", "OK");
        }
        private async void OnFrameTapped(object sender, EventArgs e)
        {
            // Check for microphone permissions
            if (await Permissions.RequestAsync<Permissions.Microphone>() != PermissionStatus.Granted)
            {
                await DisplayAlert("Permission Denied", "Microphone permission is required to record.", "OK");
                return;
            }

            // Start or stop the recording depending on its current state
            if (!_audioRecorder.IsRecording)
            {
                // Start recording
                await _audioRecorder.StartAsync();
                await DisplayAlert("Recording", "Recording started.", "OK");
            }
            else
            {
                // Stop recording after it's been started
                _audioSource = await _audioRecorder.StopAsync();
                await DisplayAlert("Recording Stopped", "Recording stopped. Playing the audio...", "OK");

                // Wait for a moment before playing
                await Task.Delay(1000); // 1 second delay before playing the audio

                // Play the audio after recording
                PlayRecording();
            }
        }

        private void PlayRecording()
        {
            // Create a player and play the recorded audio
            _audioPlayer = _audioManager.CreatePlayer(_audioSource.GetAudioStream());
            _audioPlayer.Play();
        }
    }
}
