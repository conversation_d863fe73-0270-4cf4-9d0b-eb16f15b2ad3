
//Unit Tests Using NUnit 3.0 for RegistrationController

using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Build.Tasks;
using MemberServiceBusinessLayer;


namespace MemberServiceApi.Test
{
    //Base Test Class for RegistrationController With Mock Items
    [TestFixture]
    public class RegistrationControllerTests
    {
        private Mock<IMemberQueryHandler<Member>> _mockQueryHandler;
        private Mock<IOrganizationsQueryHandler<Organization>> _mockOrganizationQueryHandler;

        private Mock<ILogger<RegistrationController>> _mockLogger;
        private Mock<IStringLocalizer<RegistrationController>> _mockLocalizer;
        private Mock<IMemberCommandHandler<Member>> _mockCommandHandler;
        private Mock<ICheckAccessQueryHandler<ProductAccess>> _mockCheckAccessQueryHandler;
        private RegistrationController _controller;

        //Setting up Mock Objects
        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IMemberQueryHandler<Member>>();
            _mockOrganizationQueryHandler = new Mock<IOrganizationsQueryHandler<Organization>>();

            _mockLogger = new Mock<ILogger<RegistrationController>>();
            _mockLocalizer = new Mock<IStringLocalizer<RegistrationController>>();
            _mockCommandHandler = new Mock<IMemberCommandHandler<Member>>();
            _mockCheckAccessQueryHandler = new Mock<ICheckAccessQueryHandler<ProductAccess>>();

            _controller = new RegistrationController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object,
                _mockCheckAccessQueryHandler.Object,
                _mockOrganizationQueryHandler.Object
            );
        }

        
        [Test] 
        public async Task Get_WhenCalled_ReturnsOkWithMembers() 
        {
            // Arrange
            var mockMembers = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), UserName = "JohnDoe", FirstName = "John", LastName = "Doe", Email = "<EMAIL>", IsActive = true },
                new Member { Id = Guid.NewGuid(), UserName = "JaneSmith", FirstName = "Jane", LastName = "Smith", Email = "<EMAIL>", IsActive = true }
            };

            _mockQueryHandler
                .Setup(q => q.GetMember())
                .ReturnsAsync(mockMembers);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockMembers));
        }

       
        [Test] 
        public async Task GetById_WhenCalledWithValidId_ReturnsOkWithMember()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var mockMember = new Member
            {
                Id = memberId,
                UserName = "JohnDoe",
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>"
            };

            _mockQueryHandler
                .Setup(q => q.GetMemberById(memberId))
                .ReturnsAsync(mockMember);

            // Act
            var result = await _controller.GetById(memberId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockMember));
        }

        
        [Test] 
        public async Task UpdateById_WhenCalledWithValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var member = new Member
            {
                Id = memberId,
                UserName = "JohnDoe",
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                IsActive = true
            };

            _mockCommandHandler
                .Setup(handler => handler.UpdateMember(It.IsAny<Member>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateById(memberId, member);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

        
        [Test] 
        public async Task UpdateById_WhenMemberIsNull_ReturnsBadRequest()
        {
            // Arrange
            var memberId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateById(memberId, null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400), "Expected status code to be 400");
        }

        
        [Test] 
        public async Task UpdateById_WhenIdDoesNotMatchMemberId_ReturnsBadRequest()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var mismatchedMember = new Member
            {
                Id = Guid.NewGuid(), 
                UserName = "JohnDoe",
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                IsActive = true
            };

            // Act
            var result = await _controller.UpdateById(memberId, mismatchedMember);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400), "Expected status code to be 400");
        }

        
        [Test] 
        public async Task DeleteById_WhenCalledWithValidId_ReturnsOkWithMessage()
        {
            // Arrange
            var memberId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(handler => handler.DeleteMemberById(memberId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteById(memberId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

       
        [Test] 
        public async Task DeleteByEntity_WhenCalledWithValidMember_ReturnsOkWithMessage()
        {
            // Arrange
            var member = new Member
            {
                Id = Guid.NewGuid(),
                UserName = "JohnDoe",
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                IsActive = true
            };

            _mockCommandHandler
                .Setup(handler => handler.DeleteMemberByEntity(member))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteByEntity(member);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

        
        [Test] 
        public async Task SearchMembers_WhenCalledWithValidSearchTerm_ReturnsOkWithMatchingMembers()
        {
            // Arrange
            var searchTerm = "John";
            var matchingMembers = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe" }
            };

            _mockQueryHandler
                .Setup(q => q.SearchMembersAsync(It.IsAny<string>()))
                .ReturnsAsync(matchingMembers);

            // Act
            var result = await _controller.SearchMembers(searchTerm);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(matchingMembers));
        }

       
        [Test] 
        public async Task SearchMembers_WhenCalledWithInvalidSearchTerm_ReturnsBadRequest()
        {
            // Arrange
            var searchTerm = "";  

            // Act
            var result = await _controller.SearchMembers(searchTerm);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result.Result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));
        }

        
        [Test] 
        public async Task SearchMembers_WhenCalledWithNoMatchingMembers_ReturnsNotFound()
        {
            // Arrange
            var searchTerm = "NonExistingName";  
            var matchingMembers = new List<Member>();  

            _mockQueryHandler
                .Setup(q => q.SearchMembersAsync(It.IsAny<string>()))
                .ReturnsAsync(matchingMembers);

            // Act
            var result = await _controller.SearchMembers(searchTerm);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<NotFoundObjectResult>(), "Expected result to be of type NotFoundObjectResult");
            var notFoundResult = result.Result as NotFoundObjectResult;
            Assert.That(notFoundResult!.StatusCode, Is.EqualTo(404));
        }

        
        [Test] 
        public async Task Registration_WhenCalledWithValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var mockMembers = new List<MemberDto>
            {
                new MemberDto { UserName = "JohnDoe", FirstName = "John", LastName = "Doe", Email = "<EMAIL>" }
            };

            _mockCommandHandler
                .Setup(handler => handler.RegisterMembersAsync(It.IsAny<List<MemberDto>>()))
                .ReturnsAsync(new List<string>());

            // Act
            var result = await _controller.Registration(mockMembers);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

        
        [Test] 
        public async Task Registration_WhenCalledWithNullOrEmptyList_ReturnsBadRequest()
        {
      
            List<MemberDto> mockMembers = null; 

            // Act
            var result = await _controller.Registration(mockMembers);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));
            

            // Arrange - Empty List
            mockMembers = new List<MemberDto>(); 

            // Act
            result = await _controller.Registration(mockMembers);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));
          
        }
    }
}