﻿using System;
using System.Collections.Generic;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class EmployerService : IEmployerService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<EmployerService> _logger;
        private readonly IStringLocalizer<EmployerService> _localizer;
        private readonly string _MemberService;

        public EmployerService(HttpClient httpClient, ILogger<EmployerService> logger, IStringLocalizer<EmployerService> localizer)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL") ?? throw new InvalidOperationException(_localizer["Base URL not configured"]);
        }

        public async Task<Employer?> GetEmployerByIdAsync(Guid id)
        {
            Employer? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Employer/{id}");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<Employer>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch Employer with ID {id}"], id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching Employer with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<bool> AddEmployerAsync(Employer Employer)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_MemberService}/api/Employer", Employer);
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error adding Employer"]);
                throw;
            }

            return result;
        }

        public async Task<bool> UpdateEmployerAsync(Guid id, Employer Employer)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_MemberService}/api/Employer/{id}", Employer);
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error updating Employer with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<bool> DeleteEmployerAsync(Guid id)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.DeleteAsync($"{_MemberService}/api/Employer/{id}");
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error deleting Employer with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<List<Employer>?> GetEmployerByNameAsync(string name)
        {
            List<Employer>? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Employer/search?name={name}");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<Employer>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch Employeres with name {name}"], name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching Employeres with name {name}"], name);
                throw;
            }

            return result;
        }

        public async Task<List<Employer>?> GetAllEmployerAsync()
        {
            List<Employer>? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Employer");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<Employer>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch all Employeres"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching all Employeres"]);
                throw;
            }

            return result;
        }
    }
}
