﻿using System.Text.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Text.RegularExpressions;
using System.ComponentModel.DataAnnotations;
using MudBlazor;
using TeyaWebApp.ViewModel;
using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class ManageProfile
    {
        private Dictionary<string, object> userDetails;
        private UpdatedUserViewModel userProfile = new UpdatedUserViewModel();
        private MudForm form;
        private bool success;
        private Color SaveButtonColor { get; set; } = Color.Primary;
        [Inject] public HttpClient Http { get; set; }
        private bool _isLoading = false;
        private Guid activeUser;
        private bool _isThemeLoaded = false;
        private MudTheme CurrentTheme;
        private ThemeItem SelectedTheme;
        private List<UserTheme> UsersTheme;
        private List<ThemeItem> Themes = new();
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IUserThemeService UserThemeService { get; set; }
        [Inject] public GraphApiService GraphApiService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        [Inject]
        private ILogger<ManageProfile> Logger { get; set; } = default!;

        [Inject]
        private IMemberService memberService { get; set; } = default!;
        private Member memberDetails;

        protected override async Task OnInitializedAsync()
        {
            if (!string.IsNullOrEmpty(TokenService.UserDetails))
            {
                userDetails = JsonSerializer.Deserialize<Dictionary<string, object>>(TokenService.UserDetails);
                userProfile = new UpdatedUserViewModel
                {
                    id = userDetails.GetValueOrDefault(Localizer["ManageId"].Value)?.ToString(),
                    DisplayName = userDetails.GetValueOrDefault(Localizer["ManageDisplayName"].Value)?.ToString(),
                    GivenName = userDetails.GetValueOrDefault(Localizer["ManageGivenName"].Value)?.ToString(),
                    Surname = userDetails.GetValueOrDefault(Localizer["ManageSurname"].Value)?.ToString(),
                    Mail = userDetails.GetValueOrDefault(Localizer["ManageMail"].Value)?.ToString(),
                    UserType = userDetails.GetValueOrDefault(Localizer["ManageUserType"].Value)?.ToString(),
                    JobTitle = userDetails.GetValueOrDefault(Localizer["ManageJobTitle"].Value)?.ToString(),
                    CompanyName = userDetails.GetValueOrDefault(Localizer["ManageCompanyName"].Value)?.ToString(),
                    Department = userDetails.GetValueOrDefault(Localizer["ManageDepartment"].Value)?.ToString(),
                    OfficeLocation = userDetails.GetValueOrDefault(Localizer["ManageOfficeLocation"].Value)?.ToString(),
                    MobilePhone = userDetails.GetValueOrDefault(Localizer["ManageMobilePhone"].Value)?.ToString(),
                    StreetAddress = userDetails.GetValueOrDefault(Localizer["ManageStreetAddress"].Value)?.ToString(),
                    City = userDetails.GetValueOrDefault(Localizer["ManageCity"].Value)?.ToString(),
                    State = userDetails.GetValueOrDefault(Localizer["ManageState"].Value)?.ToString(),
                    PostalCode = userDetails.GetValueOrDefault(Localizer["ManagePostalCode"].Value)?.ToString(),
                    Country = userDetails.GetValueOrDefault(Localizer["ManageCountry"].Value)?.ToString(),
                    ExtensionCompanyName = userDetails.GetValueOrDefault(Localizer["ManageExtensionCompanyName"].Value)?.ToString()
                };

                Guid userId = Guid.Parse(userProfile.id);
                memberDetails = await memberService.GetMemberByIdAsync(userId);
            }
            try
            {
                var themesJson = await Http.GetStringAsync($"{Navigation.BaseUri}themes.json");
                var themes = JsonSerializer.Deserialize<Dictionary<string, ThemeModel>>(JsonDocument.Parse(themesJson).RootElement.GetProperty("themes").ToString());
                foreach (var themeData in themes)
                {
                    var themeModel = themeData.Value;
                    var mudTheme = MapToMudTheme(themeModel);

                    Themes.Add(new ThemeItem
                    {
                        Theme = MapToThemeModel(mudTheme),
                        Name = themeData.Key
                    });
                }

                await GraphApiService.GetLoggedInUserDetailsAsync();
                UsersTheme = (await UserThemeService.GetUserThemesAsync()).ToList();

                if (string.IsNullOrEmpty(User?.id) || !Guid.TryParse(User.id, out Guid parsedUserId))
                {
                    SelectedTheme = Themes.FirstOrDefault();
                    CurrentTheme = MapToMudTheme(SelectedTheme.Theme);
                    _isThemeLoaded = true;
                    return;
                }
                activeUser = parsedUserId;
                var existingUserTheme = UsersTheme.FirstOrDefault(ut => activeUser == ut.UserId);
                SelectedTheme = Themes.FirstOrDefault(t => t.Name == existingUserTheme?.ThemeName);

                if (existingUserTheme == null)
                {
                    SelectedTheme = Themes.FirstOrDefault();
                    var newUserTheme = new UserTheme { UserId = activeUser, ThemeName = SelectedTheme.Name };
                    await UserThemeService.AddUserThemeAsync(newUserTheme);
                }
                CurrentTheme = MapToMudTheme(SelectedTheme.Theme);
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error initializing theme: {ex.Message}");
            }
            finally
            {
                _isThemeLoaded = true;
                StateHasChanged();
            }
        }

        [GeneratedRegex("^[A-Za-z][A-Za-z0-9]{4,14}$")]
        private static partial Regex DisplayNameRegex();

        [GeneratedRegex("^[A-Za-z]+$")]
        private static partial Regex NameRegex();

        [GeneratedRegex("^[A-Za-z]+$")]
        private static partial Regex AlphabetRegex();

        [GeneratedRegex("^\\d+$")]
        private static partial Regex PostalCodeRegex();

        [GeneratedRegex("^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$")]
        private static partial Regex EmailRegex();

        private IEnumerable<string> DisplayNameValidation(string displayName)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(displayName))
            {
                validator.Add(Localizer["Res1UName"]);
            }
            else if (!DisplayNameRegex().IsMatch(displayName))
            {
                validator.Add(Localizer["Res2UName"]);
            }
            return validator;
        }

        private IEnumerable<string> FirstNameValidation(string name)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(name))
            {
                validator.Add(Localizer["Res1UName"]);
            }
            else if (!NameRegex().IsMatch(name))
            {
                validator.Add(Localizer["Res2UName"]);
            }
            return validator;
        }

        private IEnumerable<string> LastNameValidation(string name)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(name))
            {
                validator.Add(Localizer["Res1LName"]);
            }
            else if (!NameRegex().IsMatch(name))
            {
                validator.Add(Localizer["Res2LName"]);
            }
            return validator;
        }

        private IEnumerable<string> AlphabetValidation(string value)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(value))
            {
                validator.Add(Localizer["Res1Field"]);
            }
            else if (!AlphabetRegex().IsMatch(value))
            {
                validator.Add(Localizer["Res2Field"]);
            }
            return validator;
        }

        private IEnumerable<string> PostalCodeValidation(string postalCode)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(postalCode))
            {
                validator.Add(Localizer["Res1PCode"]);
            }
            else if (!PostalCodeRegex().IsMatch(postalCode))
            {
                validator.Add(Localizer["Res2PCode"]);
            }
            return validator;
        }

        private IEnumerable<string> EmailValidation(string email)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(email))
            {
                validator.Add(Localizer["Res1Email"]);
            }
            else if (!EmailRegex().IsMatch(email))
            {
                validator.Add(Localizer["Res2Email"]);
            }
            return validator;
        }

        private async Task Save()
        {
            await form.Validate();
            if (success)
            {
                SaveButtonColor = Color.Success;
                StateHasChanged();
                await Task.Delay(500);
                SaveButtonColor = Color.Primary;
                await ManageProfileSubmit();
                NavigateToChart();
            }
            else
            {
                SaveButtonColor = Color.Error;
                StateHasChanged();
                await Task.Delay(500);
                SaveButtonColor = Color.Primary;
            }
        }

        private void NavigateToChart()
        {
            Navigation.NavigateTo("/Chart");
        }

        private async Task ManageProfileSubmit()
        {
            try
            {
                bool isUpdated = await AuthService.UpdateUserAsync(userProfile);
                if (isUpdated)
                {
                    userDetails[Localizer["Display_Name"]] = userProfile.DisplayName;
                    userDetails[Localizer["Given Name"]] = userProfile.GivenName;
                    userDetails[Localizer["SurName1"]] = userProfile.Surname;
                    userDetails[Localizer["Mail"]] = userProfile.Mail;
                    userDetails[Localizer["UserType"]] = userProfile.UserType;
                    userDetails[Localizer["JobTitle"]] = userProfile.JobTitle;
                    userDetails[Localizer["StreetAddress"]] = userProfile.StreetAddress;
                    userDetails[Localizer["City"]] = userProfile.City;
                    userDetails[Localizer["State"]] = userProfile.State;
                    userDetails[Localizer["PostalCode"]] = userProfile.PostalCode;
                    userDetails[Localizer["Country"]] = userProfile.Country;

                    TokenService.UserDetails = JsonSerializer.Serialize(userDetails);

                    var updatedData = new UpdatedUserViewModel
                    {
                        id = userProfile.id,
                        DisplayName = userProfile.DisplayName,
                        GivenName = userProfile.GivenName,
                        Surname = userProfile.Surname,
                        Mail = userProfile.Mail,
                        StreetAddress = userProfile.StreetAddress,
                        Country = userProfile.Country,
                        City = userProfile.City,
                        State = userProfile.State,
                        PostalCode = userProfile.PostalCode,
                        JobTitle = userProfile.JobTitle,
                        UserType = userProfile.UserType,
                    };

                    Guid userId = Guid.Parse(userProfile.id);
                    var member = new Member
                    {
                        Id = userId,
                        UserName = userProfile.DisplayName,
                        FirstName = userProfile.GivenName,
                        LastName = userProfile.Surname,
                        Email = userProfile.Mail,
                        IsActive = true,
                        Address = new Address
                        {
                            AddressLine1 = userProfile.StreetAddress,
                            PostalCode = userProfile.PostalCode,
                            State = userProfile.State,
                            Country = userProfile.Country,
                        },
                        Country = userProfile.Country,
                        AccessControl = userProfile.UserType,
                        AddressId = userId,
                        RoleID = memberDetails.RoleID,
                        RoleName = memberDetails.RoleName,
                        OrganizationID = memberDetails.OrganizationID,
                        OrganizationName = memberDetails.OrganizationName,
                        InsuranceId = memberDetails.InsuranceId
                    };

                    await memberService.UpdateMemberByIdAsync(member.Id, member);
                    await AuthService.UpdateAsync(updatedData.id, updatedData);

                    Logger.LogInformation(Localizer["UserProfileUpdated"]);
                    StateHasChanged();
                }
                else
                {
                    Logger.LogWarning(Localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["UpdateError"]);
            }
        }


        private async Task DeleteAccount()
        {
            try
            {
                bool result = await AuthService.DeleteUserAsync();
                if (result)
                {
                    Logger.LogInformation(Localizer["AccountDeleted"]);
                }
                else
                {
                    Logger.LogWarning(Localizer["DeleteFailed"]);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["DeleteError"]);
            }
        }

        private async Task OnThemeChanged(ThemeItem themeItem)
        {
            if (themeItem == null || string.IsNullOrEmpty(themeItem.Name))
            {
                return;
            }
            SelectedTheme = themeItem;
            CurrentTheme = MapToMudTheme(SelectedTheme.Theme);
            var userTheme = UsersTheme.FirstOrDefault(ut => activeUser == ut.UserId);
            try
            {
                if (userTheme != null)
                {
                    userTheme.ThemeName = themeItem.Name;
                    await UserThemeService.UpdateUserThemeAsync(userTheme);
                }
                else
                {
                    userTheme = new UserTheme
                    {
                        UserId = activeUser,
                        ThemeName = themeItem.Name
                    };
                    await UserThemeService.AddUserThemeAsync(userTheme);
                    UsersTheme.Add(userTheme);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error updating theme: {ex.Message}");
                SelectedTheme = Themes.FirstOrDefault();
            }
            finally
            {
                _isThemeLoaded = true;
                StateHasChanged();
            }
        }

        private MudTheme MapToMudTheme(ThemeModel themeModel)
        {
            return new MudTheme
            {
                PaletteLight = new PaletteLight
                {
                    Primary = themeModel.PaletteLight.Primary,
                    Secondary = themeModel.PaletteLight.Secondary,
                    AppbarBackground = themeModel.PaletteLight.AppbarBackground,
                    Background = themeModel.PaletteLight.Background,
                    Surface = themeModel.PaletteLight.Surface,
                    DrawerBackground = themeModel.PaletteLight.DrawerBackground,
                    TextPrimary = themeModel.PaletteLight.TextPrimary,
                    TextSecondary = themeModel.PaletteLight.TextSecondary,
                    ActionDefault = themeModel.PaletteLight.ActionDefault,
                    ActionDisabled = themeModel.PaletteLight.ActionDisabled,
                    ActionDisabledBackground = themeModel.PaletteLight.ActionDisabledBackground
                },
                PaletteDark = new PaletteDark
                {
                    Primary = themeModel.PaletteDark.Primary,
                    Secondary = themeModel.PaletteDark.Secondary,
                    AppbarBackground = themeModel.PaletteDark.AppbarBackground,
                    Background = themeModel.PaletteDark.Background,
                    Surface = themeModel.PaletteDark.Surface,
                    DrawerBackground = themeModel.PaletteDark.DrawerBackground,
                    TextPrimary = themeModel.PaletteDark.TextPrimary,
                    TextSecondary = themeModel.PaletteDark.TextSecondary,
                    ActionDefault = themeModel.PaletteDark.ActionDefault,
                    ActionDisabled = themeModel.PaletteDark.ActionDisabled,
                    ActionDisabledBackground = themeModel.PaletteDark.ActionDisabledBackground
                },
                LayoutProperties = new LayoutProperties
                {
                    DrawerWidthLeft = themeModel.LayoutProperties.DrawerWidthLeft,
                    DrawerWidthRight = themeModel.LayoutProperties.DrawerWidthRight,
                    AppbarHeight = themeModel.LayoutProperties.AppbarHeight
                }
            };
        }

        private ThemeModel MapToThemeModel(MudTheme mudTheme)
        {
            return new ThemeModel
            {
                PaletteLight = new ThemePalette
                {
                    Primary = mudTheme.PaletteLight.Primary.Value,
                    Secondary = mudTheme.PaletteLight.Secondary.Value,
                    AppbarBackground = mudTheme.PaletteLight.AppbarBackground.Value,
                    Background = mudTheme.PaletteLight.Background.Value,
                    Surface = mudTheme.PaletteLight.Surface.Value,
                    DrawerBackground = mudTheme.PaletteLight.DrawerBackground.Value,
                    TextPrimary = mudTheme.PaletteLight.TextPrimary.Value,
                    TextSecondary = mudTheme.PaletteLight.TextSecondary.Value,
                    ActionDefault = mudTheme.PaletteLight.ActionDefault.Value,
                    ActionDisabled = mudTheme.PaletteLight.ActionDisabled.Value,
                    ActionDisabledBackground = mudTheme.PaletteLight.ActionDisabledBackground.Value
                },
                PaletteDark = new ThemePalette
                {
                    Primary = mudTheme.PaletteDark.Primary.Value,
                    Secondary = mudTheme.PaletteDark.Secondary.Value,
                    AppbarBackground = mudTheme.PaletteDark.AppbarBackground.Value,
                    Background = mudTheme.PaletteDark.Background.Value,
                    Surface = mudTheme.PaletteDark.Surface.Value,
                    DrawerBackground = mudTheme.PaletteDark.DrawerBackground.Value,
                    TextPrimary = mudTheme.PaletteDark.TextPrimary.Value,
                    TextSecondary = mudTheme.PaletteDark.TextSecondary.Value,
                    ActionDefault = mudTheme.PaletteDark.ActionDefault.Value,
                    ActionDisabled = mudTheme.PaletteDark.ActionDisabled.Value,
                    ActionDisabledBackground = mudTheme.PaletteDark.ActionDisabledBackground.Value
                },
                LayoutProperties = new ThemeLayoutProperties
                {
                    DrawerWidthLeft = mudTheme.LayoutProperties.DrawerWidthLeft,
                    DrawerWidthRight = mudTheme.LayoutProperties.DrawerWidthRight,
                    AppbarHeight = mudTheme.LayoutProperties.AppbarHeight
                }
            };
        }
    }
}