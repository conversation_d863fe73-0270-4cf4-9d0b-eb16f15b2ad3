﻿CREATE TABLE [EncounterNotesService].[MedicalHistory] (
    [MedicalHistoryID]  UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [PatientName]       VARCHAR (255)    NULL,
    [ICDCode]           VARCHAR (50)     NULL,
    [CreatedDate]       DATETIME         DEFAULT (getdate()) NULL,
    [UpdatedDate]       DATETIME         DEFAULT (getdate()) NULL,
    [History]           TEXT             NULL,
    [IsPregnant]        BIT              NULL,
    [IsBreastFeeding]   BIT              NULL,
    [IsHistoryVerified] BIT              NULL,
    [PatientId]         UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId]    UNIQUEIDENTIFIER NULL,
    [PCPId]             UNIQUEIDENTIFIER NULL,
    [IsDeleted]         BIT              DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_MedicalHistory] PRIMARY KEY CLUSTERED ([MedicalHistoryID] ASC, [PatientId] ASC)
);

