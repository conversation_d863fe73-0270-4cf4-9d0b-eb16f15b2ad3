﻿CREATE TABLE [EncounterNotesService].[PatientImmunization] (
    [ImmunizationId] UNIQUEIDENTIFIER NOT NULL,
    [PatientId]      UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId] UNIQUEIDENTIFIER NULL,
    [PCPId]          UNIQUEIDENTIFIER NULL,
    [CreatedDate]    D<PERSON><PERSON>IME         NULL,
    [UpdatedDate]    DATETIME         NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER NOT NULL,
    [Immunizations]  NVARCHAR (MAX)   NOT NULL,
    [GivenDate]      DATETIME         NULL,
    [CPTCode]        NVARCHAR (MAX)   NOT NULL,
    [CVXCode]        NVARCHAR (50)    NOT NULL,
    [CPTDescription] NVARCHAR (MAX)   NOT NULL,
    [Comments]       NVARCHAR (MAX)   NULL,
    [IsActive]       BIT              NULL,
    CONSTRAINT [PK_PatientImmunization] PRIMARY KEY CLUSTERED ([ImmunizationId] ASC, [PatientId] ASC)
);

