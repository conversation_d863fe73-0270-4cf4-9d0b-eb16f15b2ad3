# Test Real Azure Blob Storage Retrieval
Write-Host "Testing Real Azure Blob Storage Retrieval..." -ForegroundColor Green

$baseUrl = "http://localhost:5000"
$messageId = "1b216f2e-9225-437b-9570-7db0d993c5e4"  # From the logs

Write-Host "Testing with Message ID: $messageId"

try {
    # Get attachments
    Write-Host "Getting attachments for message $messageId..."
    $attachments = Invoke-RestMethod -Uri "$baseUrl/api/message/$messageId/attachments" -Method GET
    
    Write-Host "Found $($attachments.Count) attachment(s)" -ForegroundColor Green
    
    if ($attachments.Count -gt 0) {
        $attachment = $attachments[0]
        Write-Host "REAL AZURE BLOB ATTACHMENT DETAILS:" -ForegroundColor Cyan
        Write-Host "  ID: $($attachment.attachmentId)"
        Write-Host "  Original Name: $($attachment.originalFileName)"
        Write-Host "  Blob Name: $($attachment.blobFileName)"
        Write-Host "  Size: $($attachment.fileSizeBytes) bytes"
        Write-Host "  Content Type: $($attachment.contentType)"
        Write-Host "  Blob URL: $($attachment.blobStorageUrl)" -ForegroundColor Yellow
        Write-Host "  Container: $($attachment.blobContainerName)" -ForegroundColor Yellow
        
        # Check if this is a real Azure URL
        if ($attachment.blobStorageUrl -like "*teyarecordingsdev*") {
            Write-Host "SUCCESS! File uploaded to REAL Azure Blob Storage!" -ForegroundColor Green
        } else {
            Write-Host "WARNING: Mock storage being used!" -ForegroundColor Yellow
        }
        
        # Test download
        Write-Host "Downloading attachment from real Azure Blob Storage..." -ForegroundColor Yellow
        $attachmentId = $attachment.attachmentId
        
        $downloadResponse = Invoke-RestMethod -Uri "$baseUrl/api/message/attachment/$attachmentId/download" -Method GET
        
        $downloadedFile = "downloaded-real-azure-attachment.txt"
        $downloadResponse | Out-File -FilePath $downloadedFile -Encoding UTF8
        
        Write-Host "Downloaded to: $downloadedFile" -ForegroundColor Green
        Write-Host "SUCCESS! Real Azure Blob Storage is working perfectly!" -ForegroundColor Green
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
