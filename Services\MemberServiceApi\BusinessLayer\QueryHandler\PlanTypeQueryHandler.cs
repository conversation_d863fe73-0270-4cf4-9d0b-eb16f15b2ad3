﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class PlanTypeQueryHandler : IPlanTypeQueryHandler<PlanType>
    {
        private readonly IUnitOfWork _unitOfWork;

        public PlanTypeQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<PlanType> GetPlanTypeByIdAsync(Guid id)
        {
            var PlanType = await _unitOfWork.PlanTypeRepository.GetByIdAsync(id);
            return PlanType;
        }


        public async Task<List<PlanType>> GetAllPlanTypesAsync()
        {
            var plans = await _unitOfWork.PlanTypeRepository.GetAllAsync();
            return plans.ToList();
        }
    }
}
