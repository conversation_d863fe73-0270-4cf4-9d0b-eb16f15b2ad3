﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface ILicenseCommandHandler<TText>
    {
        Task DeleteLicenseByEntity(ProductLicense license);
        Task DeleteLicenseById(Guid id);
        Task AddLicense(List<TText> texts);
        Task UpdateLicense(ProductLicense license);
        Task<bool> UpdateLicenseAccessAsync(List<ProductLicense> licenseAccessUpdates);
    }
}


