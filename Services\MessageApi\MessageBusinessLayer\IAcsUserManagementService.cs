using MessageContracts;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IAcsUserManagementService
    {
        Task<string> GetOrCreateAcsUserAsync(string email, string? name = null);
        Task<User> GetOrCreateUserAsync(string email, string? name = null);
        Task<string?> GetAcsUserIdAsync(string email);
        Task<bool> IsUserExistsAsync(string email);
        Task UpdateUserLastActiveAsync(string email);
    }
}
