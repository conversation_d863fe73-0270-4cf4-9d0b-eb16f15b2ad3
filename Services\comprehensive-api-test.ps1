# Comprehensive MessageAPI Test Suite
# Tests all endpoints and functionality

$baseUrl = "http://localhost:5000/api/Message"
$testResults = @()

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Method,
        [string]$Url,
        [object]$Body = $null,
        [string]$ContentType = "application/json"
    )

    Write-Host "`n🧪 Testing: $Name" -ForegroundColor Cyan
    Write-Host "   Method: $Method $Url" -ForegroundColor Gray

    try {
        $params = @{
            Uri = $Url
            Method = $Method
        }

        if ($Body) {
            if ($ContentType -eq "application/json") {
                $params.Body = ($Body | ConvertTo-Json -Depth 3)
                $params.ContentType = $ContentType
            } else {
                $params.Body = $Body
                $params.ContentType = $ContentType
            }
        }

        $response = Invoke-RestMethod @params
        Write-Host "   ✅ SUCCESS" -ForegroundColor Green

        $global:testResults += [PSCustomObject]@{
            Test = $Name
            Status = "PASS"
            Response = $response
        }

        return $response
    }
    catch {
        Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red

        $global:testResults += [PSCustomObject]@{
            Test = $Name
            Status = "FAIL"
            Error = $_.Exception.Message
        }

        return $null
    }
}

Write-Host "🚀 Starting Comprehensive MessageAPI Test Suite" -ForegroundColor Yellow
Write-Host "=" * 60

# Test 1: Send Message without Attachment
$message1 = Test-Endpoint -Name "Send Message (No Attachment)" -Method "POST" -Url "$baseUrl/send" -Body @{
    senderName = "Alice Johnson"
    senderEmailId = "<EMAIL>"
    receiverName = "Bob Smith"
    receiverEmailId = "<EMAIL>"
    messageContent = "Hello Bob! This is a test message without attachment."
}

$messageId1 = $message1.messageId

# Test 2: Send Message with Attachment
Write-Host "`n📎 Preparing attachment for test..." -ForegroundColor Yellow
$fileBytes = [System.IO.File]::ReadAllBytes("test-attachment.txt")
$base64Content = [System.Convert]::ToBase64String($fileBytes)

$message2 = Test-Endpoint -Name "Send Message (With Attachment)" -Method "POST" -Url "$baseUrl/send" -Body @{
    senderName = "Bob Smith"
    senderEmailId = "<EMAIL>"
    receiverName = "Alice Johnson"
    receiverEmailId = "<EMAIL>"
    messageContent = "Hi Alice! Please find the attached file."
    attachments = @(
        @{
            fileName = "test-attachment.txt"
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileBytes.Length
        }
    )
}

$messageId2 = $message2.messageId

# Test 3: Get Message by ID
Test-Endpoint -Name "Get Message by ID (No Attachment)" -Method "GET" -Url "$baseUrl/$messageId1"

# Test 4: Get Message by ID (With Attachment)
Test-Endpoint -Name "Get Message by ID (With Attachment)" -Method "GET" -Url "$baseUrl/$messageId2"

# Test 5: Get Messages by Email
Test-Endpoint -Name "Get Messages by Email (Alice)" -Method "GET" -Url "$baseUrl/email/<EMAIL>"

# Test 6: Get Messages by Email (Bob)
Test-Endpoint -Name "Get Messages by Email (Bob)" -Method "GET" -Url "$baseUrl/email/<EMAIL>"

# Test 7: Get Conversation
Test-Endpoint -Name "Get Conversation" -Method "GET" -Url "$baseUrl/conversation/<EMAIL>/<EMAIL>"

# Test 8: Get Unread Messages
Test-Endpoint -Name "Get Unread Messages (Alice)" -Method "GET" -Url "$baseUrl/unread/<EMAIL>"

# Test 9: Get Sent Messages
Test-Endpoint -Name "Get Sent Messages (Alice)" -Method "GET" -Url "$baseUrl/sent/<EMAIL>"

# Test 10: Get Received Messages
Test-Endpoint -Name "Get Received Messages (Bob)" -Method "GET" -Url "$baseUrl/received/<EMAIL>"

# Test 11: Get Message Attachments
if ($messageId2) {
    $attachments = Test-Endpoint -Name "Get Message Attachments" -Method "GET" -Url "$baseUrl/$messageId2/attachments"

    if ($attachments -and $attachments.Count -gt 0) {
        $attachmentId = $attachments[0].attachmentId

        # Test 12: Get Attachment Details
        Test-Endpoint -Name "Get Attachment Details" -Method "GET" -Url "$baseUrl/attachment/$attachmentId"

        # Test 13: Download Attachment
        Write-Host "`n📥 Testing attachment download..." -ForegroundColor Yellow
        try {
            Invoke-WebRequest -Uri "$baseUrl/attachment/$attachmentId/download" -OutFile "test-downloaded-attachment.txt"
            Write-Host "   ✅ Attachment downloaded successfully" -ForegroundColor Green

            $global:testResults += [PSCustomObject]@{
                Test = "Download Attachment"
                Status = "PASS"
                Response = "File downloaded successfully"
            }
        }
        catch {
            Write-Host "   ❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red

            $global:testResults += [PSCustomObject]@{
                Test = "Download Attachment"
                Status = "FAIL"
                Error = $_.Exception.Message
            }
        }
    }
}

# Test 14: Mark Message as Read
if ($messageId1) {
    Test-Endpoint -Name "Mark Message as Read" -Method "PUT" -Url "$baseUrl/$messageId1/read"
}

# Test 15: Send Additional Messages for Testing
Write-Host "`n📧 Sending additional test messages..." -ForegroundColor Yellow

# Send message from Charlie to Alice
Test-Endpoint -Name "Send Message (Charlie to Alice)" -Method "POST" -Url "$baseUrl/send" -Body @{
    senderName = "Charlie Brown"
    senderEmailId = "<EMAIL>"
    receiverName = "Alice Johnson"
    receiverEmailId = "<EMAIL>"
    messageContent = "Hi Alice! This is Charlie. How are you doing?"
}

# Send message from Alice to Charlie
Test-Endpoint -Name "Send Message (Alice to Charlie)" -Method "POST" -Url "$baseUrl/send" -Body @{
    senderName = "Alice Johnson"
    senderEmailId = "<EMAIL>"
    receiverName = "Charlie Brown"
    receiverEmailId = "<EMAIL>"
    messageContent = "Hi Charlie! I am doing great, thanks for asking!"
}

# Test 16: Test Error Handling - Invalid Message ID
Test-Endpoint -Name "Get Invalid Message ID" -Method "GET" -Url "$baseUrl/00000000-0000-0000-0000-000000000000"

# Test 17: Test Error Handling - Invalid Email
Test-Endpoint -Name "Get Messages for Invalid Email" -Method "GET" -Url "$baseUrl/email/<EMAIL>"

# Display Test Results Summary
Write-Host "`n" + "=" * 60 -ForegroundColor Yellow
Write-Host "📊 TEST RESULTS SUMMARY" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow

$passCount = ($global:testResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($global:testResults | Where-Object { $_.Status -eq "FAIL" }).Count
$totalCount = $global:testResults.Count

Write-Host "`n✅ PASSED: $passCount" -ForegroundColor Green
Write-Host "❌ FAILED: $failCount" -ForegroundColor Red
Write-Host "📊 TOTAL:  $totalCount" -ForegroundColor Cyan

if ($failCount -eq 0) {
    Write-Host "`n🎉 ALL TESTS PASSED! MessageAPI is fully functional!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some tests failed. Check the details above." -ForegroundColor Yellow
}

Write-Host "`n📋 Detailed Results:" -ForegroundColor Cyan
$global:testResults | Format-Table -AutoSize

Write-Host "`n🏁 Test Suite Complete!" -ForegroundColor Yellow
