﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface ICurrentMedicationQueryHandler<TText>
    {
        Task<IEnumerable<CurrentMedication>> GetMedicationByIdAndIsActive(Guid id);
        Task<IEnumerable<CurrentMedication>> GetAllMedicationsbyId(Guid id);
    }
}
