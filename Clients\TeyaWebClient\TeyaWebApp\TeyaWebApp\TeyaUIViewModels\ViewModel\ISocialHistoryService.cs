﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ISocialHistoryService
    {
        Task<List<PatientSocialHistory>> GetAllByPatientIdAsync(Guid id);
        Task<List<PatientSocialHistory>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddHistoryListAsync(List<PatientSocialHistory> histories);
        Task UpdateHistoryListAsync(List<PatientSocialHistory> histories);

    }
}
