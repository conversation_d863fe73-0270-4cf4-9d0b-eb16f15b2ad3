﻿using EncounterNotesContracts;
using Microsoft.SemanticKernel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using DotNetEnv;
using System.Net.Http;
using System.Text;
using System.IO;
using Microsoft.AspNetCore.Http;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using EncounterNotesBusinessLayer.EncounterNotesBusinessResource;
using FFMpegCore.Pipes;
using FFMpegCore;
using System.Text.Json;
using FFMpegCore.Enums;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Azure.Storage;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class SpeechDataHandler : ISpeechDataHandler<Speech>
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SpeechDataHandler> _logger;
        private readonly IStringLocalizer<EncounterNotesBusiness> _localizer;
        private readonly RecordDTO _record;
        private readonly IRecordCommandHandler<Record> _recordDataHandler;

        private Templates template { get; set; }
        private List<Templates> templateslist { get; set; }
        private readonly IBlobStorageService _blobStorageService;
        private readonly IKernelService _kernelService;
        private readonly ITemplatesQueryHandler<Templates> _templatesQueryHandler;
        private readonly IPredefinedTemplatesQueryHandler<PredefinedTemplates> _predefinedtemplatesQueryHandler;
        private Dictionary<string, Dictionary<string, string>> sectionData = new Dictionary<string, Dictionary<string, string>>();
        private readonly OpenAIPromptExecutionSettings _promptSettings;
        private readonly IRecordQueryHandler<Record> _recordQueryHandler;
        public SpeechDataHandler(HttpClient httpClient, ILogger<SpeechDataHandler> logger, IStringLocalizer<EncounterNotesBusiness> localizer, RecordDTO recordDTO, IBlobStorageService blobStorageService, IKernelService kernelService, ITemplatesQueryHandler<Templates> templatesQueryHandler, IPredefinedTemplatesQueryHandler<PredefinedTemplates> predefinedtemplatesQueryHandler, IRecordCommandHandler<Record> dataHandler, IRecordQueryHandler<Record> recordQueryHandler)
        {
            _httpClient = httpClient;
            _logger = logger;
            _localizer = localizer;
            _record = recordDTO;
            _blobStorageService = blobStorageService;
            _kernelService = kernelService;
            Env.Load();
            _templatesQueryHandler = templatesQueryHandler;
            _predefinedtemplatesQueryHandler = predefinedtemplatesQueryHandler;
            _recordDataHandler = dataHandler;
            _promptSettings = new OpenAIPromptExecutionSettings
            {
                Temperature = 0.2
            };
            _recordQueryHandler = recordQueryHandler;
        }

        public async Task<Guid?> Handle(SpeechRequest speechRequest)
        {
            var modelName = Environment.GetEnvironmentVariable("OPENAI_MODEL_NAME");
            var endpoint = Environment.GetEnvironmentVariable("OPENAI_ENDPOINT");
            var apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY");

            // Process all speeches in parallel
            Kernel kernel = Kernel.CreateBuilder().AddAzureOpenAIChatCompletion(modelName, endpoint, apiKey).Build();

            // Process only the first speech since that's what's being used
            if (speechRequest.Speeches.Count > 0)
            {
                try
                {
                    await ProcessSpeech(speechRequest, kernel);
                    return await UploadRecord(speechRequest.accessToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["GetLogError"]);
                    speechRequest.Speeches[0].Result = ex.Message;
                }
            }

            return null;
        }

        public async Task GetDefaultTemplateData(Guid recordId,string json, Speech speech, Kernel kernel)
        {
            Record existingRecord = await _recordQueryHandler.GetRecordById(recordId);
            var existingNotes = !string.IsNullOrEmpty(existingRecord.Notes)
        ? JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(existingRecord.Notes)
        : new Dictionary<string, Dictionary<string, string>>();
            // Parse the JSON once
            using JsonDocument doc = JsonDocument.Parse(json);
            JsonElement templateData = doc.RootElement;

            // Build all prompts first
            var extractionTasks = new List<Task<(string headerName, string sectionName, string result)>>();

            foreach (JsonProperty headerProperty in templateData.EnumerateObject())
            {
                string headerName = headerProperty.Name;
                JsonElement headerValue = headerProperty.Value;

                // Initialize the dictionary for this header
                if (!sectionData.ContainsKey(headerName))
                {
                    sectionData[headerName] = new Dictionary<string, string>();
                }

                foreach (JsonProperty sectionProperty in headerValue.EnumerateObject())
                {
                    string sectionName = sectionProperty.Name;

                    // Start building the strict prompt
                    var promptBuilder = new StringBuilder();
                    promptBuilder.Append($"Analyze the following text and extract ONLY the {sectionName} information. ");
                    promptBuilder.Append("DO NOT include the section name or any labels in your response. ");
                    promptBuilder.Append("Present the information in third-person clinical style (e.g., 'Patient reports...'). ");
                    promptBuilder.Append("DO NOT use first-person phrasing. ");
                    promptBuilder.Append("If the text does not contain any information about {sectionName}, ");
                    promptBuilder.Append("return empty string without any additional explanation or commentary.\n\n");

                    // Add any custom instructions from the template
                    if (sectionProperty.Value.TryGetProperty("Instructions", out JsonElement instructionsElement))
                    {
                        string? instructions = instructionsElement.GetString();
                        if (!string.IsNullOrEmpty(instructions))
                        {
                            promptBuilder.Append($"Additional instructions: {instructions}\n");
                        }
                    }

                    // Add section style if specified
                    if (sectionProperty.Value.TryGetProperty("SectionStyle", out JsonElement sectionStyleElement))
                    {
                        string? sectionStyle = sectionStyleElement.GetString();
                        if (sectionStyle != _localizer["Auto"] && !string.IsNullOrEmpty(sectionStyle))
                        {
                            promptBuilder.Append($"Format the output in {sectionStyle} style.\n");
                        }
                    }

                    // Finalize the prompt with the text to analyze
                    promptBuilder.Append($"\nText to analyze: {speech.Result}");

                    // Create a task for this extraction and add to list
                    extractionTasks.Add(ExtractSectionAsync(
                        speech.Result,
                        promptBuilder.ToString(),
                        kernel,
                        headerName,
                        sectionName));
                }
            }

            // Wait for all extractions to complete
            var results = await Task.WhenAll(extractionTasks);
            
            foreach (var (headerName, sectionName, result) in results)
            {

                // Get existing content or initialize
                string currentContent = existingNotes[headerName].TryGetValue(sectionName, out var content)
                    ? content
                    : string.Empty;

                // Format new content with manual heading
                string manualHeading = "<h4 style=\"margin-top: 20px; margin-bottom: 10px;\">Manual Content</h4>";
                string updatedContent;

                if (currentContent.Contains(manualHeading))
                {
                    // Append below existing manual content
                    updatedContent = currentContent.Replace(
                        manualHeading,
                        $"{manualHeading}\n<div>{result}</div>");
                }
                else
                {
                    // Create new manual section
                    updatedContent = $"{manualHeading}\n<div>{result}</div>\n{currentContent}";
                }
                sectionData[headerName][sectionName] = updatedContent;
            }

        }

        private async Task<(string headerName, string sectionName, string result)> ExtractSectionAsync(
            string text, string prompt, Kernel kernel, string headerName, string sectionName)
        {
            string result = await kernel.InvokePromptAsync<string>($"{prompt}{text}", new KernelArguments(_promptSettings));
            return (headerName, sectionName, result);
        }

        public async Task ProcessSpeech(SpeechRequest speechRequest, Kernel kernel)
        {
            Speech speech = speechRequest.Speeches[0];

            // Efficiently process transcription data
            _record.Transcription = speech.TranscribedData;
            _record.WordTimings = speech.Timestamps.Select(t => new WordTimingDTO
            {
                Word = t.Word,
                StartTime = t.StartTime,
                EndTime = t.EndTime
            }).ToList();

            // Get template data and patient name in parallel
            string templateData = await GetTemplateDataAsync(speechRequest.VisitType, speechRequest.PCPId);

            var templateTask = GetDefaultTemplateData(speechRequest.Id, templateData, speech, kernel);
            var patientNameTask = ExtractFromPrompt(speech.Result, _localizer["PatientName"], kernel);

            await Task.WhenAll(templateTask, patientNameTask);

            // Set the record properties
            _record.Id = speechRequest.Id;
            _record.PCPId = speechRequest.PCPId;
            _record.PatientName = await patientNameTask;
            _record.PatientId = speechRequest.PatientId;
            _record.Notes = JsonSerializer.Serialize(sectionData);
            _record.DateTime = DateTime.UtcNow;
        }

        private async Task<string> GetTemplateDataAsync(string visitType, Guid pcpId)
        {
            if (!string.IsNullOrEmpty(visitType))
            {
                templateslist = await _templatesQueryHandler.GetTemplatesByPCPId(pcpId);
                template = templateslist.FirstOrDefault(t => t.IsDefault && t.VisitType == visitType);

                if (template != null)
                {
                    return template.Template;
                }
            }

            // Get default template
            var predefinedTemplates = await _predefinedtemplatesQueryHandler.GetTemplates();
            return predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default").Template;
        }

        private async Task<string> ExtractFromPrompt(string text, string prompt, Kernel kernel)
        {
            return await kernel.InvokePromptAsync<string>($"{prompt}{text}", new KernelArguments(_promptSettings));
        }

        public async Task<string> ExtractFromPrompt(string text, string prompt)
        {
            return await _kernelService.ExtractFromPrompt(text, prompt);
        }

        public async Task<Guid?> UploadRecord(string accessToken)
        {
            Record existingRecord = await _recordQueryHandler.GetRecordById(_record.Id);

            if (existingRecord != null)
            {
                // Update the existing tracked entity instead of creating a new one
                existingRecord.PatientName = _record.PatientName;
                existingRecord.isEditable = true;
                existingRecord.DateTime = _record.DateTime;
                existingRecord.Notes = _record.Notes;
                existingRecord.OrganizationId = _record.OrganizationId;
                existingRecord.PCPId = _record.PCPId;
                existingRecord.PatientId = _record.PatientId;
                existingRecord.Transcription = _record.Transcription;

                // Handle the WordTimings collection properly
                existingRecord.WordTimings?.Clear();
                if (_record.WordTimings != null)
                {
                    if (existingRecord.WordTimings == null)
                        existingRecord.WordTimings = new List<WordTiming>();

                    foreach (var timing in _record.WordTimings)
                    {
                        existingRecord.WordTimings.Add(new WordTiming
                        {
                            Word = timing.Word,
                            StartTime = timing.StartTime,
                            EndTime = timing.EndTime
                        });
                    }
                }

                await _recordDataHandler.UpdateRecord(existingRecord);
            }
            else
            {
                // Create a new record if it doesn't exist
                var encounterNote = new Record
                {
                    Id = _record.Id,
                    PatientName = _record.PatientName,
                    isEditable = true,
                    DateTime = _record.DateTime,
                    Notes = _record.Notes,
                    OrganizationId = _record.OrganizationId,
                    PCPId = _record.PCPId,
                    PatientId = _record.PatientId,
                    Transcription = _record.Transcription,
                    WordTimings = _record.WordTimings?.Select(w => new WordTiming
                    {
                        Word = w.Word,
                        StartTime = w.StartTime,
                        EndTime = w.EndTime
                    }).ToList()
                };

                await _recordDataHandler.AddRecord(new List<Record> { encounterNote });
                return encounterNote.Id;
            }

            return _record.Id;
        }


        public async Task<string> UploadToBlobAsync(MemoryStream fileStream, string fileName)
        {
            _logger.LogInformation(_localizer["UploadingToBlob"]);

            var blobConnectionString = Environment.GetEnvironmentVariable("AZURE_BLOB_CONNECTION_STRING");
            var blobContainerName = Environment.GetEnvironmentVariable("AZURE_BLOB_CONTAINER_NAME");

            var blobServiceClient = new BlobServiceClient(blobConnectionString);
            var containerClient = blobServiceClient.GetBlobContainerClient(blobContainerName);

            // Don't wait for container creation if it already exists
            _ = containerClient.CreateIfNotExistsAsync();

            var blobClient = containerClient.GetBlobClient(fileName);
            var blobHttpHeaders = new BlobHttpHeaders
            {
                ContentType = "audio/webm"
            };

            await blobClient.UploadAsync(fileStream, new BlobUploadOptions
            {
                HttpHeaders = blobHttpHeaders,
                TransferOptions = new StorageTransferOptions
                {
                    InitialTransferSize = 4 * 1024 * 1024, // 4MB chunks
                    MaximumTransferSize = 4 * 1024 * 1024, // Max chunk size
                    MaximumConcurrency = 4                 // Parallel uploads
                }
            });
            return fileName;
        }
    }
}