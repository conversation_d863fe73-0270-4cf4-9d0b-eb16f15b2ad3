using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IAzureCommunicationService
    {
        Task<string> SendMessageAsync(string senderEmail, string receiverEmail, string messageContent);
        Task<string> GetOrCreateConversationAsync(string senderEmail, string receiverEmail);
        Task<IEnumerable<AcsMessage>> GetMessagesFromConversationAsync(string conversationId);
        Task<bool> MarkMessageAsDeliveredAsync(string messageId);
    }

    public class AcsMessage
    {
        public string Id { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string SenderDisplayName { get; set; } = string.Empty;
        public System.DateTimeOffset CreatedOn { get; set; }
        public string MessageType { get; set; } = string.Empty;
    }
}
