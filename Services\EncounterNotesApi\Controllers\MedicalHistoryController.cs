﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class MedicalHistoryController : ControllerBase
    {
        private readonly IMedicalHistoryCommandHandler<MedicalHistory> _medicalDataHandler;
        private readonly IMedicalHistoryQueryHandler<MedicalHistory> _medicalQueryHandler;
        private readonly ILogger<MedicalHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public MedicalHistoryController(
            IMedicalHistoryCommandHandler<MedicalHistory> medicalDataHandler,
            IMedicalHistoryQueryHandler<MedicalHistory> medicalQueryHandler,
            ILogger<MedicalHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _medicalDataHandler = medicalDataHandler;
            _medicalQueryHandler = medicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<MedicalHistory>>> GetAllById(Guid id)
        {
            try
            {
                var data = await _medicalQueryHandler.GetAllMedicalHistoriesById(id);
                return data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        [HttpGet("{id:guid}/IsActive")]
        public async Task<ActionResult<IEnumerable<MedicalHistory>>> GetAllByIdAndIsActive(Guid id)
        {
            try
            {
                var history = await _medicalQueryHandler.GetMedicalHistoryByIdAndIsActive(id);
                return history != null ? Ok(history) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        [HttpPost]
        [Route("AddMedicalHistory")]
        public async Task<IActionResult> AddMedicalHistory([FromBody] List<MedicalHistory> medicalHistories)
        {
            if (medicalHistories == null || medicalHistories.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _medicalDataHandler.AddMedicalHistory(medicalHistories);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete]
        [Route("DeleteMedicalHistory")]
        public async Task<IActionResult> DeleteByEntity([FromBody] MedicalHistory medicalHistory)
        {
            if (medicalHistory == null)
            {
                return BadRequest(_localizer["InvalidRecord"]);
            }

            try
            {
                await _medicalDataHandler.DeleteMedicalHistoryByEntity(medicalHistory);
                return Ok(_localizer["DeleteSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500, _localizer["DeleteLogError"]);
            }
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateMedicalHistoryById(Guid id, [FromBody] MedicalHistory medicalHistory)
        {
            if (medicalHistory == null || medicalHistory.PatientId != id)
            {
                return BadRequest(_localizer["InvalidRecord"]);
            }

            try
            {
                await _medicalDataHandler.UpdateMedicalHistory(medicalHistory);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500, _localizer["UpdateLogError"]);
            }
        }

        [HttpPut]
        [Route("UpdateMedicalHistoryList")]
        public async Task<IActionResult> UpdateMedicalHistoryList([FromBody] List<MedicalHistory> medicalHistories)
        {
            try
            {
                await _medicalDataHandler.UpdateMedicalHistoryList(medicalHistories);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500, _localizer["UpdateLogError"]);
            }
        }
    }
}