﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class SurgicalRepository : GenericRepository<SurgicalHistory>, ISurgicalRepository
    {
        private readonly RecordDatabaseContext _context;

        public SurgicalRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<SurgicalHistory>> GetAllSurgeriesAsync()
        {
            return await _context.SurgicalHistories
                .AsNoTracking()
                .ToListAsync();
        }
    }
}