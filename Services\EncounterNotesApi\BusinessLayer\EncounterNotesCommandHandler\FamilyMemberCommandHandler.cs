﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class FamilyMemberCommandHandler : IFamilyMemberCommandHandler<FamilyMember>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<FamilyMemberCommandHandler> _logger;

        public FamilyMemberCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<FamilyMemberCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Add new Member
        /// </summary>
        public async Task AddFamilyMember(List<FamilyMember> familyMemberList)
        {
            await _unitOfWork.FamilyMemberRepository.AddAsync(familyMemberList);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Update an existing member
        /// </summary>
        public async Task UpdateFamilyMember(FamilyMember familyMember)
        {
            await _unitOfWork.FamilyMemberRepository.UpdateAsync(familyMember);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Delete an existing Member By Id
        /// </summary>
        public async Task DeleteFamilyMemberById(Guid id)
        {
            await _unitOfWork.FamilyMemberRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Update an existing List of Family Members
        /// </summary>
        public async Task<bool> UpdateFamilyMemberList(List<FamilyMember> FamilyMemberList)
        {
            var existingRecords = await _unitOfWork.FamilyMemberRepository.GetAsync();

            foreach (var update in FamilyMemberList)
            {
                var recordToUpdate = existingRecords.FirstOrDefault(r => r.RecordID == update.RecordID);

                if (recordToUpdate != null)
                {
                    recordToUpdate.Relation = update.Relation;
                    recordToUpdate.Notes = update.Notes;
                    recordToUpdate.DOB = update.DOB;
                    recordToUpdate.Age = update.Age;
                    recordToUpdate.Status = update.Status;
                    recordToUpdate.IsActive = update.IsActive;

                    await _unitOfWork.FamilyMemberRepository.UpdateAsync(recordToUpdate);
                }
            }

            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
