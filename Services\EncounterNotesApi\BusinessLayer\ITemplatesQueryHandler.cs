﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITemplatesQueryHandler<TText>
    {
        Task<IEnumerable<TText>> GetTemplates();
        Task<TText> GetTemplatesById(Guid id);

        Task<List<TText>> GetTemplatesByPCPId(Guid id);

        Task<List<Templates>> GetTemplatesByPCPIdAndVisitType(Guid id, String VisitType);
    }
}