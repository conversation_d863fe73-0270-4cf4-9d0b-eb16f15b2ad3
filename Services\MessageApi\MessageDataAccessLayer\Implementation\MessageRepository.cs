using DataAccessLayer.Context;
using MessageContracts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public class MessageRepository : GenericRepository<Message>, IMessageRepository
    {
        public MessageRepository(MessageApiDatabaseContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Message>> GetMessagesByEmailAsync(string email)
        {
            return await _context.Set<Message>()
                .Where(m => m.SenderEmailId.Equals(email, StringComparison.OrdinalIgnoreCase) ||
                           m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(m => m.SentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetConversationAsync(string senderEmail, string receiverEmail)
        {
            return await _context.Set<Message>()
                .Where(m =>
                    (m.SenderEmailId.Equals(senderEmail, StringComparison.OrdinalIgnoreCase) &&
                     m.ReceiverEmailId.Equals(receiverEmail, StringComparison.OrdinalIgnoreCase)) ||
                    (m.SenderEmailId.Equals(receiverEmail, StringComparison.OrdinalIgnoreCase) &&
                     m.ReceiverEmailId.Equals(senderEmail, StringComparison.OrdinalIgnoreCase)))
                .OrderBy(m => m.SentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetUnreadMessagesAsync(string email)
        {
            return await _context.Set<Message>()
                .Where(m => m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase) &&
                           m.Status != 3) // 3 = Read status
                .OrderByDescending(m => m.SentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetSentMessagesAsync(string email)
        {
            return await _context.Set<Message>()
                .Where(m => m.SenderEmailId.Equals(email, StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(m => m.SentDateTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetReceivedMessagesAsync(string email)
        {
            return await _context.Set<Message>()
                .Where(m => m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(m => m.SentDateTime)
                .ToListAsync();
        }

        public async Task<Message?> GetMessageWithAttachmentsAsync(Guid messageId)
        {
            return await _context.Set<Message>()
                .Include(m => m.Attachments)
                .FirstOrDefaultAsync(m => m.MessageId == messageId);
        }

        public async Task<IEnumerable<Message>> GetMessagesWithAttachmentsByEmailAsync(string email)
        {

            return await _context.Set<Message>()
                .Include(m => m.Attachments)
                .Where(m => m.SenderEmailId.ToLower() == email.ToLower() ||
                            m.ReceiverEmailId.ToLower() == email.ToLower())
                .OrderByDescending(m => m.SentDateTime)
                .ToListAsync();

        }
    }
}
