﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class PrescriptionMedicationQueryHandler : IPrescriptionMedicationQueryHandler<PrescriptionMedication>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PrescriptionMedicationQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PrescriptionMedication>> GetMedicationByIdAndIsActive(Guid id)
        {
            var medications = await _unitOfWork.PrescriptionMedicationRepository.GetAsync();

            return medications.Where(CurrMedication => CurrMedication.PatientId == id && CurrMedication.isActive == true);
        }

        public async Task<IEnumerable<PrescriptionMedication>> GetAllMedicationsbyId(Guid id)
        {
            var medications = await _unitOfWork.PrescriptionMedicationRepository.GetAsync();

            return medications.Where(PMedication => PMedication.PatientId == id);
        }
    }
}
