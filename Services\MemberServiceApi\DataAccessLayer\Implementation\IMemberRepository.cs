﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IMemberRepository : IGenericRepository<Member>
    {
        Task<List<string>> GetExistingEmailsAsync(List<string?> newEmails);
        Task<IEnumerable<Member>> SearchMembersAsync(string searchTerm);
        Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds);
        Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid organizationId);
        Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid organizationId);
    }
}
