﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface ICurrentMedicationCommandHandler<TText>
    {
        Task DeleteMedicationByEntity(CurrentMedication currentmedication);
        Task DeleteMedicationById(Guid id);
        Task AddMedication(List<TText> texts);
        Task UpdateMedication(CurrentMedication currentmedication);
        Task UpdateMedicationList(List<CurrentMedication> Medications);
    }
}
