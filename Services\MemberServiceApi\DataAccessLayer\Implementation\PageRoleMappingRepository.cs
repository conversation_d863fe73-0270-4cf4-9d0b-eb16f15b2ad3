using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class PageRoleMappingRepository : IPageRoleMappingRepository
    {
        private readonly AccountDatabaseContext _context;

        public PageRoleMappingRepository(AccountDatabaseContext context)
        {
            _context = context;
        }

        public async Task AddAsync(PageRoleMapping pageRoleMapping)
        {
            await _context.PageRoleMappings.AddAsync(pageRoleMapping);
        }

        public async Task UpdateAsync(PageRoleMapping pageRoleMapping)
        {
            _context.PageRoleMappings.Update(pageRoleMapping);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var pageRoleMapping = await _context.PageRoleMappings.FindAsync(id);
            if (pageRoleMapping != null)
            {
                _context.PageRoleMappings.Remove(pageRoleMapping);
            }
        }

        public async Task<PageRoleMapping> GetByIdAsync(Guid id)
        {
            var result = await _context.PageRoleMappings.FindAsync(id);
            return result;
        }

        public async Task<List<PageRoleMapping>> GetAllAsync()
        {
            var result = await _context.PageRoleMappings.ToListAsync();
            return result;
        }

        public async Task<List<PageRoleMapping>> GetByPagePathAsync(string pagePath)
        {
            var result = await _context.PageRoleMappings
                .Where(prm => prm.PagePath.Contains(pagePath, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }

        public async Task<List<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId)
        {
            if (string.IsNullOrWhiteSpace(pagePath))
                throw new ArgumentException("Page path cannot be null or whitespace.", nameof(pagePath));

            var result = await _context.PageRoleMappings
                .Where(prm => prm.PagePath.ToLower() == pagePath.ToLower() && prm.IsActive && prm.HasAccess && prm.OrganizationId == OrganizationId)
                .Select(prm => prm.RoleName)
                .Distinct()
                .ToListAsync();
            return result;
        }
    }
}
