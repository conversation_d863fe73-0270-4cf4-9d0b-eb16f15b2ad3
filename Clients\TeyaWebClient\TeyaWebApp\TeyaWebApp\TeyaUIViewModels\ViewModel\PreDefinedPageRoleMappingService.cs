﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Services
{
    public class PreDefinedPageRoleMappingService : IPreDefinedPageRoleMappingService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<PreDefinedPageRoleMappingService> _localizer;
        private readonly ILogger<PreDefinedPageRoleMappingService> _logger;

        public PreDefinedPageRoleMappingService(HttpClient httpClient, IStringLocalizer<PreDefinedPageRoleMappingService> localizer, ILogger<PreDefinedPageRoleMappingService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        public async Task<IEnumerable<PreDefinedPageRoleMappingData>> GetPreDefinedPageRoleMappingsAsync()
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PreDefinedPageRoleMapping";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<PreDefinedPageRoleMappingData>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllPreDefinedPageRoleMappings"]);
                throw;
            }
        }

        public async Task<PreDefinedPageRoleMappingData> GetPreDefinedPageRoleMappingByIdAsync(Guid id)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/PreDefinedPageRoleMapping/{id}";
                var accessToken = _tokenService.AccessToken;
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<PreDefinedPageRoleMappingData>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPreDefinedPageRoleMappingById"], id);
                throw;
            }
        }

        public async Task AddPreDefinedPageRoleMappingAsync(PreDefinedPageRoleMappingData PreDefinedPageRoleMapping)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var bodyContent = JsonSerializer.Serialize(PreDefinedPageRoleMapping);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/PreDefinedPageRoleMapping";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddPreDefinedPageRoleMappingFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingPreDefinedPageRoleMapping"]);
                throw;
            }
        }

        public async Task UpdatePreDefinedPageRoleMappingAsync(PreDefinedPageRoleMappingData PreDefinedPageRoleMapping)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PreDefinedPageRoleMapping/{PreDefinedPageRoleMapping.Id}";
                var bodyContent = JsonSerializer.Serialize(PreDefinedPageRoleMapping);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PreDefinedPageRoleMappingUpdatedSuccessfully"], PreDefinedPageRoleMapping.Id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PreDefinedPageRoleMappingUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingPreDefinedPageRoleMapping"]);
                throw;
            }
        }

        public async Task DeletePreDefinedPageRoleMappingByIdAsync(Guid id)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PreDefinedPageRoleMapping/{id}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PreDefinedPageRoleMappingDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PreDefinedPageRoleMappingDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingPreDefinedPageRoleMapping"], id);
                throw;
            }
        }
        public async Task<IEnumerable<PreDefinedPageRoleMappingData>> GetPagesByRoleNameAsync(string roleName)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/PreDefinedPageRoleMapping/role/{roleName}";
                var accessToken = _tokenService.AccessToken;
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<IEnumerable<PreDefinedPageRoleMappingData>>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetPagesByRoleIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPagesByRoleId"], roleName);
                throw;
            }
        }
        public async Task<IEnumerable<string>> GetRolesByPagePathAsync(string pagePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pagePath))
                {
                    _logger.LogWarning(_localizer["PagePathRequired"]);
                    throw new ArgumentException(_localizer["PagePathRequiredMessage"]);
                }
                var apiUrl = $"{_MemberService}/api/PreDefinedPageRoleMapping/roles-by-pagepath?pagePath={Uri.EscapeDataString(pagePath)}";
                var accessToken = _tokenService.AccessToken;
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to fetch roles. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RolesNotFoundMessage"]);
                }

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<string>>(responseData, options) ?? new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingRoles"], pagePath);
                throw;
            }
        }
    }
}
