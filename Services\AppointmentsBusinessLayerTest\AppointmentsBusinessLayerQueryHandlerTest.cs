﻿using Moq;
using AppointmentContracts;
using AppointmentDataAccessLayer.AppointmentImplementation;
using BusinessLayer.QueryHandler;
using Microsoft.Extensions.Configuration;
using Xunit;

namespace AppointmentsBusinessLayerTest
{
    public class AppointmentsBusinessLayerQueryHandlerTest
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly AppointmentQueryHandler _handler;

        public AppointmentsBusinessLayerQueryHandlerTest()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockConfiguration = new Mock<IConfiguration>();

            _handler = new AppointmentQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Fact]
        public async Task GetAppointment_ShouldReturnAppointments()
        {
            // Arrange
            var appointments = new List<Appointment>
            {
                new Appointment
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),

                    FacilityId =  Guid.NewGuid(),

                    OrganisationId = Guid.NewGuid(),
                    StartTime = DateTime.Now,
                    EndTime = DateTime.Now.AddHours(1),
                    AppointmentDate = DateTime.Today,
                    CreatedDate = DateTime.Now.AddDays(-5),
                    UpdatedDate = DateTime.Now,
                    Subscription = true
                },
                new Appointment
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),

                    FacilityId = Guid.NewGuid(),

                    OrganisationId = Guid.NewGuid(),
                    StartTime = DateTime.Now.AddDays(1),
                    EndTime = DateTime.Now.AddDays(1).AddHours(1),
                    AppointmentDate = DateTime.Today.AddDays(1),
                    CreatedDate = DateTime.Now.AddDays(-3),
                    UpdatedDate = DateTime.Now.AddDays(-1),
                    Subscription = false
                }
            };

            _mockUnitOfWork.Setup(uow => uow.AppointmentRepository.GetAllAsync()).ReturnsAsync(appointments);

            // Act
            var result = await _handler.GetAppointment();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            _mockUnitOfWork.Verify(uow => uow.AppointmentRepository.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetAppointmentByDate_ShouldReturnAppointmentsForGivenDate()
        {
            // Arrange
            var date = DateTime.Today;
            var appointments = new List<Appointment>
            {
                new Appointment
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),
                    FacilityId = Guid.NewGuid(),
                    OrganisationId = Guid.NewGuid(),
                    StartTime = date,
                    EndTime = date.AddHours(1),
                    AppointmentDate = date,
                    CreatedDate = DateTime.Now.AddDays(-2),
                    UpdatedDate = DateTime.Now,
                    Subscription = true
                }
            };

            _mockUnitOfWork.Setup(uow => uow.AppointmentRepository.GetByDateAsync(date, appointments[0].OrganisationId, appointments[0].Subscription)).ReturnsAsync(appointments);

            // Act
            var result = await _handler.GetAppointmentByDate(date, appointments[0].OrganisationId, appointments[0].Subscription);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            _mockUnitOfWork.Verify(uow => uow.AppointmentRepository.GetByDateAsync(date, appointments[0].OrganisationId, appointments[0].Subscription), Times.Once);
        }

        [Fact]
        public async Task GetAppointmentById_ShouldReturnAppointmentForGivenId()
        {
            // Arrange
            var shardId = Guid.NewGuid();
            var subscription = true;
            var appointment = new Appointment
            {
                Id = shardId,
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddHours(1),
                AppointmentDate = DateTime.Today,
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now,
                Subscription = true
            };

            _mockUnitOfWork.Setup(uow => uow.AppointmentRepository.GetByIdAsync(subscription, shardId, appointment.OrganisationId)).ReturnsAsync(appointment);

            // Act
            var result = await _handler.GetAppointmentById(subscription, shardId, appointment.OrganisationId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(shardId, result.Id);
            _mockUnitOfWork.Verify(uow => uow.AppointmentRepository.GetByIdAsync(subscription, shardId, appointment.OrganisationId), Times.Once);
        }
    }
}
