﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class TemplatesQueryHandler : ITemplatesQueryHandler<Templates>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public TemplatesQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Templates>> GetTemplates()
        {
            var templates = await _unitOfWork.TemplatesRepository.GetAsync();
            return templates;
        }

        public async Task<Templates> GetTemplatesById(Guid id)
        {
            return await _unitOfWork.TemplatesRepository.GetByIdAsync(id);
        }

        public async Task<List<Templates>> GetTemplatesByPCPId(Guid id)
        {
            return await _unitOfWork.TemplatesRepository.GetTemplatesByPCPId(id);
        }

        public async Task<List<Templates>> GetTemplatesByPCPIdAndVisitType(Guid id,String VisitType)
        {
            return await _unitOfWork.TemplatesRepository.GetTemplatesByPCPIdAndVisitType(id,VisitType);
        }
    }
}