﻿@page "/Security"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "SecurityAccessPolicy")]
@using TeyaUIViewModels.ViewModel
@layout Admin
@inject IProductService ProductService
@inject IMemberService MemberService
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel

<GenericCard Heading="Security Settings">
    @if (Products != null && Products.Any())
    {
        var searchTool = new List<string>() { "Search" };
        <SfGrid DataSource="@Products" Toolbar="searchTool" AllowPaging="true" AllowSorting="true" Style="margin-left: 2%; margin-right: 2%; padding: 10px;">
            <GridPageSettings PageSize="7" PageSizes="true"></GridPageSettings>
            <GridColumns>
                <GridColumn Field=@nameof(Product.Name) HeaderText="Name" TextAlign="TextAlign.Center"></GridColumn>
                <GridColumn Field=@nameof(Product.Description) HeaderText="Description" TextAlign="TextAlign.Center"></GridColumn>
                <GridColumn Field=@nameof(Product.Byproduct) HeaderText="Byproduct" TextAlign="TextAlign.Center"></GridColumn>
            </GridColumns>
            <GridEvents TValue="Product" RowSelected="@OnRowSelected"></GridEvents>
        </SfGrid>
    }

    @if (SelectedProduct != null && Members != null)
    {
        <MudText Typo="Typo.h6" style="margin-top: 2%; margin-left: 2%; padding-bottom: 2%;">Members with access to @SelectedProduct.Name</MudText>
        <SfGrid DataSource="@Members" AllowPaging="true" AllowSorting="true" Style="margin-left: 2%; margin-right: 2%; padding: 10px;">
            <GridColumns>
                <GridColumn Field=@nameof(Member.UserName) HeaderText="Username" TextAlign="TextAlign.Center"></GridColumn>
                <GridColumn HeaderText="Has Access" TextAlign="TextAlign.Center">
                    <Template>
                        <div class="checkbox-container">
                            <MudCheckBox @bind-Value="((Member)context).IsActive" T="bool" @onchange="(e) => UpdateMemberAccessLocally((Member)context, ((ChangeEventArgs)e).Value)">
                            </MudCheckBox>
                        </div>
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>

        <MudGrid>
            <MudItem xs="12" class="d-flex justify-end mt-16">
                <MudButton Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save" Color="Color.Info" Size="Size.Small" OnClick="SaveAllMemberAccess">
                    Save
                </MudButton>
            </MudItem>
        </MudGrid>
    }
</GenericCard>