﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class DiagnosticImagingPageCommandHandler : IDiagnosticImagingPageCommandHandler<DiagnosticImagingDTO>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DiagnosticImagingPageCommandHandler> _logger;

        public DiagnosticImagingPageCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<DiagnosticImagingPageCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Add new Diagnostic Imaging
        /// </summary>
        public async Task AddDiagnosticImaging(List<DiagnosticImagingDTO> diagnosticImagingList)
        {
            await _unitOfWork.DiagnosticImagingPageRepository.AddAsync(diagnosticImagingList);
            if (diagnosticImagingList[0].Assessments != null)
            {
                await _unitOfWork.DiagnosticImagingAssessmentRepository.AddAsync(diagnosticImagingList[0].Assessments);
            }
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Update an existing Diagnostic Imaging
        /// </summary>
        public async Task UpdateDiagnosticImaging(DiagnosticImagingDTO diagnosticImaging)
        {
            await _unitOfWork.DiagnosticImagingPageRepository.UpdateAsync(diagnosticImaging);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Delete an existing Diagnostic Imaging By Id
        /// </summary>
        public async Task DeleteDiagnosticImagingById(Guid id)
        {
            await _unitOfWork.DiagnosticImagingPageRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Update an existing List of Diagnostic Imaging
        /// </summary>
        public async Task<bool> UpdateDiagnosticImagingList(List<DiagnosticImagingDTO> diagnosticImagingList)
        {
            var existingRecords = await _unitOfWork.DiagnosticImagingPageRepository.GetAsync();

            foreach (var update in diagnosticImagingList)
            {
                var recordToUpdate = existingRecords.FirstOrDefault(r => r.RecordID == update.RecordID);

                if (recordToUpdate != null)
                {
                    recordToUpdate.AssignedTo = update.AssignedTo;
                    recordToUpdate.InHouse = update.InHouse;
                    recordToUpdate.FutureOrder = update.FutureOrder;
                    recordToUpdate.Result = update.Result;
                    recordToUpdate.ClassicalInfo = update.ClassicalInfo;
                    recordToUpdate.InternalNotes = update.InternalNotes;
                    recordToUpdate.Facility = update.Facility;
                    recordToUpdate.InHouse = update.InHouse;
                    recordToUpdate.Status = update.Status;
                    recordToUpdate.Procedgure = update.Procedgure;
                    recordToUpdate.Provider = update.Provider;
                    recordToUpdate.HigherPriority = update.HigherPriority;
                    recordToUpdate.OrderDate = update.OrderDate;
                    recordToUpdate.Recieved = update.Recieved;
                    recordToUpdate.Reason = update.Reason;
                    recordToUpdate.IsActive = update.IsActive;

                    await _unitOfWork.DiagnosticImagingPageRepository.UpdateAsync(recordToUpdate);
                }
            }

            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
