﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Identity.Web;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Azure.Messaging.ServiceBus;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer;

namespace MemberServiceApi
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            DotNetEnv.Env.Load();
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();

            // Swagger configuration
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Swagger Azure AD for MemberService API", Version = "v1" });
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Description = "Oauth2.0 which uses AuthorizationCode flow",
                    Name = "oauth2.0",
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__AuthorizationUrl")),
                            TokenUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__TokenUrl")),
                            Scopes = new Dictionary<string, string>
                            {
                                { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope"), "Access API as User" }
                            }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                        new[] { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope") }
                    }
                });
            });
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAllOrigins",
                    builder =>
                    {
                        builder.AllowAnyOrigin()
                               .AllowAnyMethod()
                               .AllowAnyHeader();
                    });
            });
            // Localization configuration
            services.AddLocalization();
            // Authentication configuration
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = Environment.GetEnvironmentVariable("AzureAd__Authority");
                    options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidIssuer = Environment.GetEnvironmentVariable("AzureAd__Authority"),
                        ValidAudience = Environment.GetEnvironmentVariable("AzureAd__ClientId")
                    };
                });
            // Dependency Injection configuration
            // In Startup.cs ConfigureServices or Program.cs
            services.AddControllers()
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.Converters.Add(
                        new AttributeCollectionConverter<AttributeCollectionSubmitResponse_Attribute>());
                    options.JsonSerializerOptions.Converters.Add(
                        new AttributeCollectionConverter<AttributeCollectionSubmitResponse_AttributeError>());
                });
            services.AddScoped<IAddressesQueryHandler<Address>, AddressesQueryHandler>();
            services.AddScoped<IAddressesCommandHandler<Address>, AddressesCommandHandler>();
            services.AddScoped<IFacilityQueryHandler<Facility>, FacilityQueryHandler>();
            services.AddScoped<IFacilityCommandHandler<Facility>, FacilityCommandHandler>();
            services.AddScoped<IProductQueryHandler<Product>, ProductQueryHandler>();
            services.AddScoped<IProductCommandHandler<Product>, ProductCommandHandler>();
            services.AddScoped<ILicenseQueryHandler<ProductLicense>, LicenseQueryHandler>();
            services.AddScoped<ILicenseCommandHandler<ProductLicense>, LicenseCommandHandler>();
            services.AddScoped<IMemberCommandHandler<Member>, MemberCommandHandler>();
            services.AddScoped<IInsuranceCommandHandler<Insurance>, InsuranceCommandHandler>();
            services.AddScoped<IInsuranceQueryHandler<Insurance>, InsuranceQueryHandler>();
            services.AddScoped<IMemberQueryHandler<Member>, MemberQueryHandler>();
            services.AddScoped<IProductMembersQueryHandler<ProductUserAccess>, ProductMembersQueryHandler>();
            services.AddScoped<IUserAccessCommandHandler<ProductUserAccess>, UserAccessCommandHandler>();
            services.AddScoped<IRolesCommandHandler<Role>, RolesCommandHandler>();
            services.AddScoped<IRolesQueryHandler<Role>, RolesQueryHandler>();
            services.AddScoped<IOrganizationsCommandHandler<Organization>, OrganizationsCommandHandler>();
            services.AddScoped<IOrganizationsQueryHandler<Organization>, OrganizationsQueryHandler>();
            services.AddScoped<IUpToDateCommandHandler<UpToDate>, UpToDateCommandHandler>();
            services.AddScoped<IUpToDateQueryHandler<UpToDate>, UpToDateQueryHandler>();
            services.AddScoped<UserThemeRepository>();
            services.AddScoped<IUserThemeCommandHandler<UserTheme>, UserThemeCommandHandler>();
            services.AddScoped<IUserThemeQueryHandler<UserTheme>, UserThemeQueryHandler>();
            services.AddScoped<ICheckAccessQueryHandler<ProductAccess>, CheckAccessQueryHandler>();
            services.AddScoped<IPageRoleMappingQueryHandler<PageRoleMapping>, PageRoleMappingQueryHandler>();
            services.AddScoped<IPageRoleMappingCommandHandler<PageRoleMapping>, PageRoleMappingCommandHandler>();
            services.AddScoped<IPreDefinedPageRoleMappingQueryHandler<PreDefinedPageRoleMapping>, PreDefinedPageRoleMappingQueryHandler>();
            services.AddScoped<IPreDefinedPageRoleMappingCommandHandler<PreDefinedPageRoleMapping>, PreDefinedPageRoleMappingCommandHandler>();
            services.AddScoped<IVisitTypeRepository, VisitTypeRepository>();
            services.AddScoped<IVisitTypeQueryHandler<VisitType>, VisitTypeQueryHandler>();
            services.AddScoped<IVisitTypeCommandHandler<VisitType>, VisitTypeCommandHandler>();
            services.AddScoped<IVisitStatusRepository, VisitStatusRepository>();
            services.AddScoped<IVisitStatusQueryHandler<VisitStatus>, VisitStatusQueryHandler>();
            services.AddScoped<IVisitStatusCommandHandler<VisitStatus>, VisitStatusCommandHandler>();
            services.AddScoped<IPageRoleMappingQueryHandler<PageRoleMapping>, PageRoleMappingQueryHandler>();
            services.AddScoped<IPagePathCommandHandler<PagePath>, PagePathCommandHandler>();
            services.AddScoped<IPagePathQueryHandler<PagePath>, PagePathQueryHandler>();
            services.AddScoped<ICountryQueryHandler<Country>, CountryQueryHandler>();
            services.AddScoped<ICountryCommandHandler<Country>, CountryCommandHandler>();
            services.AddScoped<IPredefinedVisitTypeQueryHandler<PredefinedVisitType>, PredefinedVisitTypeQueryHandler>();
            services.AddScoped<IRoleslistQueryHandler<Rolesdata>, RoleslistQueryHandler>();
            services.AddScoped<IGuardianQueryHandler<Guardian>, GuardianQueryHandler>();
            services.AddScoped<IGuardianCommandHandler<Guardian>, GuardianCommandHandler>();
            services.AddScoped<IEmployerQueryHandler<Employer>, EmployerQueryHandler>();
            services.AddScoped<IEmployerCommandHandler<Employer>, EmployerCommandHandler>();

            services.AddScoped<IUserLicenseCommandHandler<UserLicense>, UserLicenseCommandHandler>();
            services.AddScoped<IUserLicenseQueryHandler<UserLicense>, UserLicenseQueryHandler>();
            services.AddScoped<IPlanTypeQueryHandler<PlanType>, PlanTypeQueryHandler>();
            services.AddScoped<IPlanTypeCommandHandler<PlanType>, PlanTypeCommandHandler>();


            services.AddDbContext<AccountDatabaseContext>(options =>
                options.UseSqlServer(Environment.GetEnvironmentVariable("DatabaseConnectionString"))
                       .EnableSensitiveDataLogging()
                       .LogTo(Console.WriteLine, LogLevel.Information));
            services.AddTransient<IUnitOfWork, UnitOfWork>();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment() || env.IsProduction())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.OAuthClientId(Environment.GetEnvironmentVariable("SwaggerAzureAd__ClientId"));
                    c.OAuthUsePkce();
                    c.OAuthScopeSeparator(" ");
                    string swaggerJsonBasePath = string.IsNullOrWhiteSpace(c.RoutePrefix) ? "." : "..";
                    c.SwaggerEndpoint($"{swaggerJsonBasePath}/swagger/v1/swagger.json", "MemberServiceApi v1");
                });
            }
            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseCors("AllowAllOrigins");
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
