﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel;

public class ObHistoryService : IObHistoryService
{
    private readonly HttpClient _httpClient;
    private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
    private readonly string _obHistoryUrl;
    private readonly ITokenService _tokenService;
    private readonly ILogger<ObHistoryService> _logger;

    public ObHistoryService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService, ILogger<ObHistoryService> logger)
    {
        _httpClient = httpClient;
        _localizer = localizer;
        _logger = logger;

        _obHistoryUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
        _tokenService = tokenService;
    }

    public async Task<List<ObHistoryDTO>> GetAllObHistoriesAsync()
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            return await _httpClient.GetFromJsonAsync<List<ObHistoryDTO>>(_obHistoryUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving OB histories");
            throw new Exception("Error retrieving OB histories: " + ex.Message);
        }
    }

    public async Task AddAsync(ObHistoryDTO obHistoryDto)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PostAsJsonAsync($"{_obHistoryUrl}/api/ObHistory", obHistoryDto);
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding OB history");
            throw new Exception("Error adding OB history: " + ex.Message);
        }
    }

    public async Task UpdateObHistoryAsync(Guid id, ObHistoryDTO obHistoryDto)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PutAsJsonAsync($"{_obHistoryUrl}/api/ObHistory/{id}", obHistoryDto);
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating OB history");
            throw new Exception("Error updating OB history: " + ex.Message);
        }
    }

    public async Task DeleteObHistoryAsync(Guid id)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.DeleteAsync($"{_obHistoryUrl}/api/ObHistory/{id}");
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting OB history");
            throw new Exception("Error deleting OB history: " + ex.Message);
        }
    }

    public async Task<IEnumerable<ObHistoryDTO>> GetByPatientIdAsync(Guid patientId)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            return await _httpClient.GetFromJsonAsync<IEnumerable<ObHistoryDTO>>($"{_obHistoryUrl}/api/ObHistory/patient/{patientId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving OB histories for PatientId {patientId}");
            throw new Exception($"Error retrieving OB histories for PatientId {patientId}: " + ex.Message);
        }
    }

    public async Task UpdateObHistoryListAsync(List<ObHistoryDTO> obHistories)
    {
        try
        {
            if (obHistories == null || !obHistories.Any())
            {
                throw new ArgumentException("OB history list is empty.", nameof(obHistories));
            }

            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PutAsJsonAsync($"{_obHistoryUrl}/api/ObHistory/bulk-update", obHistories);
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk update of OB histories");
            throw new Exception("Error during bulk update of OB histories: " + ex.Message);
        }
    }
    public async Task<List<ObHistoryDTO>> LoadObHistoriesAsync(Guid patientId)
    {
        try
        {

            var obHistories = await GetByPatientIdAsync(patientId);


            return obHistories.Select(h => new ObHistoryDTO
            {
                obId = h.obId,
                PatientId = h.PatientId,
                Symptoms = h.Symptoms,
                Notes = h.Notes,
                DateOfComplaint = h.DateOfComplaint,
                OrganizationId = h.OrganizationId,
                PcpId = h.PcpId,
                IsDeleted = h.IsDeleted
            }).ToList();
        }
        catch (Exception ex)
        {

            throw new Exception("Error loading OB histories: " + ex.Message);
        }
    }

}