﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IFamilyMemberService
    {
        Task CreateMemberAsync(List<FamilyMember> Tasks);
        Task<List<FamilyMember>> GetMemberAsync(Guid id);
        Task UpdateFamilyMemberList(List<FamilyMember> familyMembers);
        Task<List<FamilyMember>> GetFamilyMemberByIdAsyncAndIsActive(Guid id);
        Task DeleteMemberAsync(Guid taskId);
        Task UpdateMemberAsync(FamilyMember familyMember);
    }
}
