﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using System.Diagnostics.Eventing.Reader;

namespace TeyaWebApp.Components.Pages
{
    public partial class ObstetricHistory
    {
        [Inject]
        private ILogger<ObHistoryDTO> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }

        private SfRichTextEditor richTextEditor;
        private MudDialog showBrowsePopup;
        private SfGrid<ObHistoryDTO> ObHistoryGrid;
        private string richTextContent = string.Empty;
        private string symptoms = string.Empty;
        private string notes = string.Empty;
        private Guid PatientId { get; set; }
        private List<ObHistoryDTO> obHistories = new();
        private List<ObHistoryDTO> addedObHistories = new();
        private List<ObHistoryDTO> updatedObHistories = new();
        private List<ObHistoryDTO> deletedObHistories = new();
        private bool add = false;



        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = PatientService.PatientData.Id;
                await LoadObHistoriesAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Initialization error");
            }
        }

        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };



        public void ActionCompletedHandler(ActionEventArgs<ObHistoryDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                deletedObHistories.Add(args.Data);
                args.Data.IsDeleted = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.obId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = (Guid)PatientService.PatientData.OrganizationID;
                args.Data.PcpId = Guid.Parse(User.id);
                args.Data.DateOfComplaint = DateTime.Now;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    addedObHistories.Add(args.Data);
                    add = false;
                }
                if (!addedObHistories.Contains(args.Data) && !updatedObHistories.Contains(args.Data))
                {
                    updatedObHistories.Add(args.Data);
                }
            }

        }

        private async Task SaveChanges()
        {
            try
            {
                // Handle new entries
                if (addedObHistories.Any())
                {
                    foreach (var newEntry in addedObHistories)
                    {
                        await ObHistoryService.AddAsync(newEntry);
                    }
                    addedObHistories.Clear();
                }

                // Handle updates
                if (updatedObHistories.Any())
                {
                    await ObHistoryService.UpdateObHistoryListAsync(updatedObHistories);
                    updatedObHistories.Clear();
                }

                // Handle deletes
                if (deletedObHistories.Any())
                {
                    await ObHistoryService.UpdateObHistoryListAsync(deletedObHistories);
                    deletedObHistories.Clear();
                }

                await LoadObHistoriesAsync();
                richTextContent = GenerateRichTextContent();
                await richTextEditor.RefreshUIAsync();
                CloseBrowsePopup();
                Snackbar.Add(Localizer["Changes saved successfully!"], Severity.Success);
                
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving changes");
                Snackbar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task CancelChanges()
        {
            addedObHistories.Clear();
            updatedObHistories.Clear();
            deletedObHistories.Clear();
            await LoadObHistoriesAsync();
            CloseBrowsePopup();
            Snackbar.Add(Localizer["Changes discarded"], Severity.Info);
        }

        private async Task LoadObHistoriesAsync()
        {
            try
            {
                obHistories = await ObHistoryService.LoadObHistoriesAsync(PatientId);

                richTextContent = GenerateRichTextContent();

                if (ObHistoryGrid != null)
                {
                    await ObHistoryGrid.Refresh();
                }
                   

                if (richTextEditor != null)
                    await richTextEditor.RefreshUIAsync();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading OB Histories");
            }
        }

        private string GenerateRichTextContent() => string.Join(" ",
            obHistories.OrderByDescending(o => o.DateOfComplaint)
                .Select(o => $"<ul><li style='margin-left: 20px;'><b>{o.DateOfComplaint:yyyy-MM-dd}</b> : {o.Symptoms} => {o.Notes}</li></ul>"));


        private void CloseBrowsePopup()
        {
            symptoms = string.Empty;
            notes = string.Empty;
            showBrowsePopup?.CloseAsync();
        }

        private async Task OpenBrowsePopup() => await showBrowsePopup?.ShowAsync();
    }
}