﻿using AppointmentContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BusinessLayer
{
    public interface IAppointmentCommandHandler<TText>
    {
        Task DeleteAppointmentById(bool Subscription,Guid shardId, Guid OrgID);
        Task AddAppointment(List<TText> texts, Guid shardId);
        Task UpdateAppointment(Appointment appointment,Guid shardId);
        void AddShard(ShardRequest request);
    }
}
