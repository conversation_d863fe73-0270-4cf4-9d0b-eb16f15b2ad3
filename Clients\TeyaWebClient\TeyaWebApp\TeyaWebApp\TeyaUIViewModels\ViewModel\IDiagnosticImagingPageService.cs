﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDiagnosticImagingPageService
    {
        Task CreateDiagnosticImagingAsync(List<DiagnosticImagingDTO> Tasks);
        Task CreateAssessmentAsync(List<DiagnosticImagingAssessment> Tasks);
        Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingAsync(Guid id);
        Task UpdateDiagnosticImagingList(List<DiagnosticImagingDTO> diagnosticImaging);
        Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAsyncAndIsActive(Guid id);
        Task<List<DiagnosticImagingAssessment>> GetAssessmentyById(Guid id);
        Task DeleteDiagnosticImagingAsync(Guid taskId);
        Task UpdateDiagnosticImagingAsync(DiagnosticImagingDTO diagnosticImaging);
    }
}
