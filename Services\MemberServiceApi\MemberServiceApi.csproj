﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>cb418fd1-7f39-4243-bed3-f2087682a149</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>.</DockerfileContext>
  </PropertyGroup>
	<PropertyGroup>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
	</PropertyGroup>
  <ItemGroup>
    <Compile Remove="BusinessLayer\**" />
    <Compile Remove="ClassLibrary1\**" />
    <Compile Remove="Contracts\**" />
    <Compile Remove="DataAccessLayer\**" />
    <Compile Remove="MemberServiceApi-Test\**" />
    <Compile Remove="MemberServiceApiBusinessLayerTestCases\**" />
    <Compile Remove="MemberServiceApiControllerTestCases\**" />
    <Compile Remove="MemberServiceApiDataAccessLayerTestCases\**" />
    <Compile Remove="User.DataAccess\**" />
    <Content Remove="BusinessLayer\**" />
    <Content Remove="ClassLibrary1\**" />
    <Content Remove="Contracts\**" />
    <Content Remove="DataAccessLayer\**" />
    <Content Remove="MemberServiceApi-Test\**" />
    <Content Remove="MemberServiceApiBusinessLayerTestCases\**" />
    <Content Remove="MemberServiceApiControllerTestCases\**" />
    <Content Remove="MemberServiceApiDataAccessLayerTestCases\**" />
    <Content Remove="User.DataAccess\**" />
    <EmbeddedResource Remove="BusinessLayer\**" />
    <EmbeddedResource Remove="ClassLibrary1\**" />
    <EmbeddedResource Remove="Contracts\**" />
    <EmbeddedResource Remove="DataAccessLayer\**" />
    <EmbeddedResource Remove="MemberServiceApi-Test\**" />
    <EmbeddedResource Remove="MemberServiceApiBusinessLayerTestCases\**" />
    <EmbeddedResource Remove="MemberServiceApiControllerTestCases\**" />
    <EmbeddedResource Remove="MemberServiceApiDataAccessLayerTestCases\**" />
    <EmbeddedResource Remove="User.DataAccess\**" />
    <None Remove="BusinessLayer\**" />
    <None Remove="ClassLibrary1\**" />
    <None Remove="Contracts\**" />
    <None Remove="DataAccessLayer\**" />
    <None Remove="MemberServiceApi-Test\**" />
    <None Remove="MemberServiceApiBusinessLayerTestCases\**" />
    <None Remove="MemberServiceApiControllerTestCases\**" />
    <None Remove="MemberServiceApiDataAccessLayerTestCases\**" />
    <None Remove="User.DataAccess\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="2.19.1" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="2.19.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="NUnit" Version="4.3.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.3" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="BusinessLayer\MemberServiceBusinessLayer.csproj" />
    <ProjectReference Include="Contracts\Contracts.csproj" />
    <ProjectReference Include="DataAccessLayer\MemberServiceDataAccessLayer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Strings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Strings.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\Strings.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
		

</Project>
