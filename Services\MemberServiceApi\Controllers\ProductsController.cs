﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging; // For logging
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Contracts;
using Microsoft.Extensions.Localization;
using MemberServiceBusinessLayer;
using Microsoft.EntityFrameworkCore;
using System;
using DataAccessLayer.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace ProductServiceApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ProductsController : ControllerBase
    {
        private readonly IProductCommandHandler<Product> _productDataHandler;
        private readonly IProductQueryHandler<Product> _productQueryHandler;
        private readonly ILogger<ProductsController> _logger;
        private readonly IStringLocalizer<ProductsController> _localizer; 
        private readonly IProductMembersQueryHandler<ProductUserAccess> _productMembersQueryHandler;
        private readonly IUserAccessCommandHandler<ProductUserAccess> _accessCommandHandler;


        public ProductsController(
            IProductCommandHandler<Product> dataHandler,
            IProductQueryHandler<Product> queryHandler,
            ILogger<ProductsController> logger,
            IStringLocalizer<ProductsController> localizer,
            IProductMembersQueryHandler<ProductUserAccess> productmembersqueryhandler,
            IUserAccessCommandHandler<ProductUserAccess> accesscommandhandler
            )
        {
            _productDataHandler = dataHandler;
            _productQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer; // Assigning localizer
            _productMembersQueryHandler = productmembersqueryhandler;
            _accessCommandHandler=accesscommandhandler;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Product>>> Get()
        {
            try
            {
                var products = await _productQueryHandler.GetProduct();
                return Ok(products);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        [HttpGet("{id:guid}")]
        public async Task<ActionResult<Product>> GetById(Guid id)
        {
            try
            {
                var product = await _productQueryHandler.GetProductById(id);
                return Ok(product);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] Product product)
        {
            if (product == null || product.Id != id)
            {
                return BadRequest(_localizer["InvalidProduct"]);
            }

            try
            {
                await _productDataHandler.UpdateProduct(product);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id:guid}")]
        public async Task<IActionResult> DeleteById(Guid id)
        {
            try
            {
                await _productDataHandler.DeleteProductById(id);
                return Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete("(entity)")]
        public async Task<IActionResult> DeleteByEntity([FromBody] Product product)
        {
            try
            {
                await _productDataHandler.DeleteProductByEntity(product);
                return Ok(_localizer["DeleteSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        [HttpPost]
        [Route("registration")]
        public async Task<IActionResult> Registration([FromBody] List<ProductRegistrationDto> registrations)
        {
            if (registrations == null || registrations.Count == 0)
            {
                return BadRequest(_localizer["NoProduct"]);
            }

            try
            {
                var products = registrations.Select(reg => new Product
                {
                    Id = Guid.NewGuid(), 
                    Name = reg.Name,
                    Description = reg.Description,
                    ByProduct = reg.ByProduct,
                }).ToList();

                await _productDataHandler.AddProduct(products);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }




        [HttpGet("{productId}/members")]
        public async Task<ActionResult<IEnumerable<Member>>> GetMembersForProduct(Guid productId)
        {
            
                var members = await _productMembersQueryHandler.GetProductMembers(productId);


                if (members == null || !members.Any())
                {
                    return NotFound();
                }

                return Ok(members);
            
           
        }

        // POST: api/Product/{productId}/updateAccess
        [HttpPut("updateAccess")]
        public async Task<IActionResult> UpdateAccess([FromQuery] Guid productId, [FromBody] List<MemberAccessUpdate> memberAccessUpdates)
        {
            if (productId == Guid.Empty || memberAccessUpdates == null)
            {
                return BadRequest(_localizer["AccessError"]);
            }
            var success = await _accessCommandHandler.UpdateUserAccessAsync(productId, memberAccessUpdates);

            if (success)
            {
                return Ok(new { Message = _localizer["AccessUpdateSuccessful"] });
            }
            else
            {
                return NotFound(_localizer["Access record not found"]);
            }
        }

    }
}

