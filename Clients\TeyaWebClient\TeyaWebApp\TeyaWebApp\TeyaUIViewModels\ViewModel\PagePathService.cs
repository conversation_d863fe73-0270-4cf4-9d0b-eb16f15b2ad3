﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Services
{
    public class PagePathService : IPagePathService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<PagePathService> _localizer;
        private readonly ILogger<PagePathService> _logger;

        public PagePathService(HttpClient httpClient, IStringLocalizer<PagePathService> localizer, ILogger<PagePathService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        public async Task<IEnumerable<PagePath>> GetPagePathsAsync()
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PagePath";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<PagePath>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllPagePaths"]);
                throw;
            }
        }

        public async Task<PagePath> GetPagePathByIdAsync(Guid id)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/PagePath/{id}";
                var accessToken = _tokenService.AccessToken;
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<PagePath>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPagePathById"], id);
                throw;
            }
        }

        public async Task AddPagePathAsync(PagePath pagePath)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var bodyContent = JsonSerializer.Serialize(pagePath);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/PagePath";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddPagePathFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingPagePath"]);
                throw;
            }
        }

        public async Task UpdatePagePathAsync(PagePath pagePath)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PagePath/{pagePath.PageId}";
                var bodyContent = JsonSerializer.Serialize(pagePath);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PagePathUpdatedSuccessfully"], pagePath.PageId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PagePathUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingPagePath"]);
                throw;
            }
        }

        public async Task DeletePagePathByIdAsync(Guid id)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PagePath/{id}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PagePathDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PagePathDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingPagePath"], id);
                throw;
            }
        }
    }
}
