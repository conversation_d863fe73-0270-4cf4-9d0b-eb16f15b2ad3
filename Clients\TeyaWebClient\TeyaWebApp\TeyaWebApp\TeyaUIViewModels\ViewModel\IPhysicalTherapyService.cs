﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPhysicalTherapyService
    {
        Task<List<PhysicalTherapyData>> GetAllByIdAsync(Guid id);
        Task<List<PhysicalTherapyData>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddPhysicalTherapyAsync(List<PhysicalTherapyData> medicalHistories);
        Task UpdatePhysicalTherapyAsync(PhysicalTherapyData _PhysicalTherapy);
        Task UpdatePhysicalTherapyListAsync(List<PhysicalTherapyData> medicalHistories);
        Task DeletePhysicalTherapyByEntityAsync(PhysicalTherapyData _PhysicalTherapy);
    }
}