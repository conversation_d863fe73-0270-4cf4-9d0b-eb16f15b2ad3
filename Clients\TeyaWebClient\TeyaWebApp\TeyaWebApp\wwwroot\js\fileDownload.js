// File download functionality for TeyaWebApp
window.downloadFile = (dataUrl, fileName) => {
    try {
        // Create a temporary anchor element
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = fileName;
        
        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log(`File download initiated: ${fileName}`);
    } catch (error) {
        console.error('Error downloading file:', error);
        alert(`Error downloading file: ${error.message}`);
    }
};

// Alternative download method using blob
window.downloadFileFromBytes = (base64Data, fileName, contentType) => {
    try {
        // Convert base64 to blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: contentType });
        
        // Create download URL
        const url = window.URL.createObjectURL(blob);
        
        // Create and trigger download
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        
        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        console.log(`File download completed: ${fileName}`);
    } catch (error) {
        console.error('Error downloading file from bytes:', error);
        alert(`Error downloading file: ${error.message}`);
    }
};

// Download file from URL
window.downloadFileFromUrl = async (url, fileName) => {
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        
        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        
        console.log(`File download completed from URL: ${fileName}`);
    } catch (error) {
        console.error('Error downloading file from URL:', error);
        alert(`Error downloading file: ${error.message}`);
    }
};
