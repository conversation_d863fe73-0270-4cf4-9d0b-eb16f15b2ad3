using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    /// <summary>
    /// Mock implementation of Azure Blob Storage for testing purposes
    /// Stores files in memory and simulates blob storage operations
    /// </summary>
    public class MockBlobStorageService : IAzureBlobStorageService
    {
        private readonly ILogger<MockBlobStorageService> _logger;
        private readonly string _containerName;
        
        // In-memory storage for mock blobs
        private static readonly ConcurrentDictionary<string, MockBlob> _mockBlobs = new();

        public MockBlobStorageService(IConfiguration configuration, ILogger<MockBlobStorageService> logger)
        {
            _logger = logger;
            _containerName = Environment.GetEnvironmentVariable("AZURE_BLOB_CONTAINER_NAME") 
                ?? configuration["AzureBlobStorage:ContainerName"] 
                ?? "message-attachments";

            _logger.LogInformation($"MockBlobStorageService initialized with container: {_containerName}");
        }

        public async Task<string> UploadAttachmentAsync(byte[] content, string fileName, string contentType)
        {
            try
            {
                // Generate unique blob name
                var blobFileName = $"{Guid.NewGuid()}_{fileName}";
                
                _logger.LogInformation($"[MOCK] Uploading attachment to blob: {blobFileName}, Size: {content.Length} bytes");

                // Create mock blob
                var mockBlob = new MockBlob
                {
                    BlobName = blobFileName,
                    Content = content,
                    ContentType = contentType,
                    OriginalFileName = fileName,
                    UploadedDateTime = DateTime.UtcNow,
                    ContainerName = _containerName
                };

                // Store in memory
                _mockBlobs[blobFileName] = mockBlob;

                // Simulate async operation
                await Task.Delay(50);

                _logger.LogInformation($"[MOCK] Successfully uploaded attachment to blob: {blobFileName}");
                return blobFileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[MOCK] Failed to upload attachment to blob storage: {fileName}");
                throw;
            }
        }

        public async Task<byte[]> DownloadAttachmentAsync(string blobFileName)
        {
            try
            {
                _logger.LogInformation($"[MOCK] Downloading attachment from blob: {blobFileName}");

                if (!_mockBlobs.TryGetValue(blobFileName, out var mockBlob))
                {
                    throw new FileNotFoundException($"[MOCK] Blob not found: {blobFileName}");
                }

                // Simulate async operation
                await Task.Delay(25);

                _logger.LogInformation($"[MOCK] Successfully downloaded attachment from blob: {blobFileName}, Size: {mockBlob.Content.Length} bytes");
                return mockBlob.Content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[MOCK] Failed to download attachment from blob storage: {blobFileName}");
                throw;
            }
        }

        public async Task<bool> DeleteAttachmentAsync(string blobFileName)
        {
            try
            {
                _logger.LogInformation($"[MOCK] Deleting attachment from blob: {blobFileName}");

                // Simulate async operation
                await Task.Delay(25);

                var removed = _mockBlobs.TryRemove(blobFileName, out _);
                
                if (removed)
                {
                    _logger.LogInformation($"[MOCK] Successfully deleted attachment from blob: {blobFileName}");
                }
                else
                {
                    _logger.LogWarning($"[MOCK] Blob not found for deletion: {blobFileName}");
                }

                return removed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[MOCK] Failed to delete attachment from blob storage: {blobFileName}");
                return false;
            }
        }

        public async Task<bool> AttachmentExistsAsync(string blobFileName)
        {
            try
            {
                // Simulate async operation
                await Task.Delay(10);
                
                var exists = _mockBlobs.ContainsKey(blobFileName);
                _logger.LogInformation($"[MOCK] Blob exists check for {blobFileName}: {exists}");
                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[MOCK] Failed to check if attachment exists in blob storage: {blobFileName}");
                return false;
            }
        }

        public string GetAttachmentUrl(string blobFileName)
        {
            // Return a mock URL
            var mockUrl = $"https://mockstorageaccount.blob.core.windows.net/{_containerName}/{blobFileName}";
            _logger.LogInformation($"[MOCK] Generated blob URL: {mockUrl}");
            return mockUrl;
        }

        /// <summary>
        /// Get statistics about mock storage (for testing/debugging)
        /// </summary>
        public static MockStorageStats GetStats()
        {
            var totalSize = 0L;
            var count = _mockBlobs.Count;
            
            foreach (var blob in _mockBlobs.Values)
            {
                totalSize += blob.Content.Length;
            }

            return new MockStorageStats
            {
                BlobCount = count,
                TotalSizeBytes = totalSize,
                Blobs = _mockBlobs.Keys.ToList()
            };
        }

        /// <summary>
        /// Clear all mock blobs (for testing)
        /// </summary>
        public static void ClearAll()
        {
            _mockBlobs.Clear();
        }
    }

    public class MockBlob
    {
        public string BlobName { get; set; } = string.Empty;
        public byte[] Content { get; set; } = Array.Empty<byte>();
        public string ContentType { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public DateTime UploadedDateTime { get; set; }
        public string ContainerName { get; set; } = string.Empty;
    }

    public class MockStorageStats
    {
        public int BlobCount { get; set; }
        public long TotalSizeBytes { get; set; }
        public List<string> Blobs { get; set; } = new();
    }
}
