using Azure.Messaging.ServiceBus;
using Contracts;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using DotNetEnv;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class MemberCommandHandler : IMemberCommandHandler<Member>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        public MemberCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            Env.Load();
        }

        public async Task AddMember(List<Member> members)
        {
            await _unitOfWork.MemberRepository.AddAsync(members);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateMember(Member member)
        {
            var members = new List<MemberDto>();
            await _unitOfWork.MemberRepository.UpdateAsync(member);
            await _unitOfWork.SaveAsync();
            var updatedMemberDto = new MemberDto
            {
                PCPName = member.PCPName,
                PCPId = member.PCPId,
                Id = member.Id,
                UserName = member.UserName
            };
            members.Add(updatedMemberDto);
        }

        public async Task DeleteMemberById(Guid id)
        {
            await _unitOfWork.MemberRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
            var members = new List<MemberDto>();
            var deletedMemberDto = new MemberDto
            {
                PCPName = null,
                PCPId = null,
                Id = id,
                UserName = null
            };
            members.Add(deletedMemberDto);
        }

        public async Task DeleteMemberByEntity(Member member)
        {
            await _unitOfWork.MemberRepository.DeleteByEntityAsync(member);
            await _unitOfWork.SaveAsync();
            var members = new List<MemberDto>();
            var deletedMemberDto = new MemberDto
            {
                PCPName = member.PCPName,
                PCPId = member.PCPId,
                Id = member.Id,
                UserName = member.UserName
            };

            members.Add(deletedMemberDto);
        }

        public async Task<List<string>> RegisterMembersAsync(List<MemberDto> registrations)
        {
            var result = new List<string>();

            var newEmails = registrations.Select(dto => dto.Email).ToList();

            var existingEmails = await _unitOfWork.MemberRepository.GetExistingEmailsAsync(newEmails);

            if (existingEmails.Any())
            {
                result = existingEmails;
            }
            else
            {
                var members = registrations.Select(dto => new Member
                {
                    Id = dto.Id,
                    UserName = dto.UserName,
                    Email = dto.Email,
                    IsActive = dto.IsActive,
                    FirstName = dto.FirstName,
                    LastName = dto.LastName,
                    MiddleName = dto.MiddleName,
                    Suffix = dto.Suffix,
                    PhoneNumber = dto.PhoneNumber,
                    DateOfBirth = dto.DateOfBirth,
                    Country = dto.Country,
                    PreferredName = dto.PreferredName,
                    ExternalID = dto.ExternalID,
                    MaritalStatus = dto.MaritalStatus,
                    SexualOrientation = dto.SexualOrientation,
                    PreviousNames = dto.PreviousNames,
                    Language = dto.Language,
                    Ethnicity = dto.Ethnicity,
                    Race = dto.Race,
                    Nationality = dto.Nationality,
                    FamilySize = dto.FamilySize,
                    FinancialReviewDate = dto.FinancialReviewDate,
                    MonthlyIncome = dto.MonthlyIncome,
                    ReferralSource = dto.ReferralSource,
                    Religion = dto.Religion,
                    DateDeceased = dto.DateDeceased,
                    ReasonDeceased = dto.ReasonDeceased,
                    FederalTaxId = dto.FederalTaxId,
                    DEANumber = dto.DEANumber,
                    UPIN = dto.UPIN,
                    NPI = dto.NPI,
                    ProviderType = dto.ProviderType,
                    MainMenuRole = dto.MainMenuRole,
                    PatientMenuRole = dto.PatientMenuRole,
                    Supervisor = dto.Supervisor,
                    JobDescription = dto.JobDescription,
                    Taxonomy = dto.Taxonomy,
                    NewCropERxRole = dto.NewCropERxRole,
                    AccessControl = dto.AccessControl,
                    AdditionalInfo = dto.AdditionalInfo,
                    DefaultBillingFacility = dto.DefaultBillingFacility,
                    RoleID = dto.RoleID,
                    OrganizationID = dto.OrganizationID,
                    OrganizationName = dto.OrganizationName,
                    PCPId = dto.PCPId,
                    PCPName = dto.PCPName,
                    AddressId = dto.AddressId,
                    InsuranceId = dto.InsuranceId,
                    GuardianId = dto.GuardianId,
                    EmployerId = dto.EmployerId,
                    SSN = dto.SSN,
                    RoleName = dto.RoleName,
                    ProfileImageUrl =dto.ProfileImageUrl
                }).ToList();
                await _unitOfWork.MemberRepository.AddAsync(members);
                await _unitOfWork.SaveAsync();
            }

            return result;
        }
    }
}
