﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ImmunizationQueryHandler : IImmunizationQueryHandler<Immunization>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public ImmunizationQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Immunization>> GetAllImmunizationsById(Guid id)
        {
            var immunization = await _unitOfWork.ImmunizationRepository.GetAllImmunizationsAsync();

            return immunization.Where(Immuni => Immuni.PatientId == id);
        }
        public async Task<IEnumerable<Immunization>> GetImmunizationByIdAndIsActive(Guid id)
        {
            var immunizations = await _unitOfWork.ImmunizationRepository.GetAsync();

            return immunizations.Where(ImmunizaTion => ImmunizaTion.PatientId == id && ImmunizaTion.IsActive == true);
        }
    }
}