﻿using AppointmentContracts;
using AppointmentDataAccessLayer.AppointmentContext;
using AppointmentDataAccessLayer.DataAccessLayerResources;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AppointmentDataAccessLayer.AppointmentImplementation
{
    public class AppointmentRepository : GenericRepository<Appointment>, IAppointmentRepository
    {
        public AppointmentRepository(ShardMapManagerService shardMapManagerService,
            
                                      IStringLocalizer<DataAccessLayerStrings> localizer,
                                      ILogger<AppointmentDatabaseContext> logger)
            : base(shardMapManagerService, localizer, logger) 
        {
        }
    }
}