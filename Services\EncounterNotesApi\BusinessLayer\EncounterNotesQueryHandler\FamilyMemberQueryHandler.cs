﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class FamilyMemberQueryHandler : IFamilyMemberQueryHandler<FamilyMember>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        public FamilyMemberQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        ///  Get All Family Member by Id 
        /// </summary>
        public async Task<List<FamilyMember>> GetFamilyMembersById(Guid id)
        {
            try
            {
                return await _unitOfWork.FamilyMemberRepository.GetFamilyMembersById(id);

            }
            catch (Exception ex) 
            { 
                 Console.WriteLine(ex.Message);
                return new List<FamilyMember>();  
            }
        }

        /// <summary>
        ///  Get Active Family Member by Id 
        /// </summary>
        public async Task<IEnumerable<FamilyMember>> GetFamilyMemberByIdAndIsActive(Guid id)
        {
            var familyMembers = await _unitOfWork.FamilyMemberRepository.GetFamilyMembersById(id);

            return familyMembers.Where(familyMember => familyMember.PatientId == id && familyMember.IsActive == true);
        }
    }
}
