﻿using System;
using System.Collections.Generic;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class GuardianService : IGuardianService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<GuardianService> _logger;
        private readonly IStringLocalizer<GuardianService> _localizer;
        private readonly string _MemberService;

        public GuardianService(HttpClient httpClient, ILogger<GuardianService> logger, IStringLocalizer<GuardianService> localizer)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL") ?? throw new InvalidOperationException(_localizer["Base URL not configured"]);
        }

        public async Task<Guardian?> GetGuardianByIdAsync(Guid id)
        {
            Guardian? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Guardian/{id}");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<Guardian>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch Guardian with ID {id}"], id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching Guardian with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<bool> AddGuardianAsync(Guardian Guardian)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_MemberService}/api/Guardian", Guardian);
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error adding Guardian"]);
                throw;
            }

            return result;
        }

        public async Task<bool> UpdateGuardianAsync(Guid id, Guardian Guardian)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_MemberService}/api/Guardian/{id}", Guardian);
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error updating Guardian with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<bool> DeleteGuardianAsync(Guid id)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.DeleteAsync($"{_MemberService}/api/Guardian/{id}");
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error deleting Guardian with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<List<Guardian>?> GetGuardianByNameAsync(string name)
        {
            List<Guardian>? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Guardian/search?name={name}");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<Guardian>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch Guardianes with name {name}"], name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching Guardianes with name {name}"], name);
                throw;
            }

            return result;
        }

        public async Task<List<Guardian>?> GetAllGuardianAsync()
        {
            List<Guardian>? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Guardian");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<Guardian>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch all Guardianes"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching all Guardianes"]);
                throw;
            }

            return result;
        }
    }
}
