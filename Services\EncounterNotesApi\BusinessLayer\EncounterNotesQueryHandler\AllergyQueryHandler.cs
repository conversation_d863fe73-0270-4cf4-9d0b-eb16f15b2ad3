﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class AllergyQueryHandler : IAllergyQueryHandler<Allergy>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public AllergyQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Allergy>> GetAllergyByIdAndIsActive(Guid id)
        {
            var allergies = await _unitOfWork.AllergyRepository.GetAllAllergyAsync();

            return allergies.Where(m => m.PatientId == id && m.isActive == true);
        }

        public async Task<IEnumerable<Allergy>> GetAllAllergybyId(Guid id)
        {
            var allergies = await _unitOfWork.AllergyRepository.GetAllAllergyAsync();

            return allergies.Where(m => m.PatientId == id);
        }
    }
}