﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class TherapeuticInterventionsCommandHandler : ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData>
    {
        private readonly IUnitOfWork _unitOfWork;

        public TherapeuticInterventionsCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddTherapeuticInterventions(List<TherapeuticInterventionsData> _TherapeuticInterventions)
        {
            await _unitOfWork.TherapeuticInterventionsRepository.AddAsync(_TherapeuticInterventions);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateTherapeuticInterventions(TherapeuticInterventionsData _TherapeuticInterventions)
        {
            await _unitOfWork.TherapeuticInterventionsRepository.UpdateAsync(_TherapeuticInterventions);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTherapeuticInterventionsById(Guid id)
        {
            await _unitOfWork.TherapeuticInterventionsRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTherapeuticInterventionsByEntity(TherapeuticInterventionsData _TherapeuticInterventions)
        {
            await _unitOfWork.TherapeuticInterventionsRepository.DeleteByEntityAsync(_TherapeuticInterventions);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateTherapeuticInterventionsList(List<TherapeuticInterventionsData> _TherapeuticInterventions)
        {
            await _unitOfWork.TherapeuticInterventionsRepository.UpdateRangeAsync(_TherapeuticInterventions);
            await _unitOfWork.SaveAsync();
        }
    }
}
