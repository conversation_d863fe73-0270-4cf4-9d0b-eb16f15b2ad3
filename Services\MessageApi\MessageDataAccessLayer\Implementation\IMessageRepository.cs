using MessageContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public interface IMessageRepository : IGenericRepository<Message>
    {
        Task<IEnumerable<Message>> GetMessagesByEmailAsync(string email);
        Task<IEnumerable<Message>> GetConversationAsync(string senderEmail, string receiverEmail);
        Task<IEnumerable<Message>> GetUnreadMessagesAsync(string email);
        Task<IEnumerable<Message>> GetSentMessagesAsync(string email);
        Task<IEnumerable<Message>> GetReceivedMessagesAsync(string email);
        Task<Message?> GetMessageWithAttachmentsAsync(Guid messageId);
        Task<IEnumerable<Message>> GetMessagesWithAttachmentsByEmailAsync(string email);
    }
}
