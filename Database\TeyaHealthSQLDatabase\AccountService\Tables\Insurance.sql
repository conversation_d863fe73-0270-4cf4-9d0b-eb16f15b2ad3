﻿CREATE TABLE [AccountService].[Insurance] (
    [InsuranceId]              UNIQUEIDENTIFIER NOT NULL,
    [PrimaryInsuranceProvider] NVARCHAR (255)   NULL,
    [PlanName]                 NVARCHAR (255)   NULL,
    [Subscriber]               NVARCHAR (255)   NULL,
    [EffectiveDate]            DATETIME         NULL,
    [Relationship]             NVARCHAR (100)   NULL,
    [PolicyNumber]             NVARCHAR (100)   NULL,
    [GroupNumber]              NVARCHAR (100)   NULL,
    [SocialSecurityNumber]     NVARCHAR (20)    NULL,
    [SubscriberEmployer]       NVARCHAR (255)   NULL,
    [Sex]                      NVARCHAR (10)    NULL,
    [SubscriberAddressLine1]   NVARCHAR (255)   NULL,
    [SubscriberAddressLine2]   NVARCHAR (255)   NULL,
    [SubscriberCity]           NVARCHAR (100)   NULL,
    [SubscriberState]          NVARCHAR (100)   NULL,
    [SubscriberZipCode]        NVARCHAR (20)    NULL,
    [SubscriberCountry]        NVARCHAR (100)   NULL,
    [SubscriberPhone]          NVARCHAR (20)    NULL,
    [CoPay]                    DECIMAL (18, 2)  NULL,
    [AcceptAssignment]         BIT              NULL,
    PRIMARY KEY CLUSTERED ([InsuranceId] ASC)
);

