﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IAssessmentsRepository:IGenericRepository<AssessmentsData>
    {
        Task<IEnumerable<AssessmentsData>> GetAllAssessmentsAsync();
        Task<List<string>> GetAssessmentRelatedMedications(Guid PatientId);
        Task<List<AssessmentsData>> GetListOfAssesmentsThroughCheifComplaintId(Guid CheifComplaintId);
    }
}
