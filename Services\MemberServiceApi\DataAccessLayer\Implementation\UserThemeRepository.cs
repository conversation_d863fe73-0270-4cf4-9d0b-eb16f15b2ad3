using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class UserThemeRepository : IUserThemeRepository
    {
        private readonly AccountDatabaseContext _context;

        public UserThemeRepository(AccountDatabaseContext context)
        {
            _context = context;
        }

        public async Task AddAsync(UserTheme userTheme)
        {
            await _context.UserThemes.AddAsync(userTheme);
        }

        public async Task UpdateAsync(UserTheme userTheme)
        {
            _context.UserThemes.Update(userTheme);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var userTheme = await _context.UserThemes.FindAsync(id);
            if (userTheme != null)
            {
                _context.UserThemes.Remove(userTheme);
            }
        }

        public async Task<UserTheme> GetByIdAsync(Guid id)
        {
            var result = await _context.UserThemes.FindAsync(id);
            return result;
        }

        public async Task<List<UserTheme>> GetAllAsync()
        {
            var result = await _context.UserThemes.ToListAsync();
            return result;
        }
    }
}
