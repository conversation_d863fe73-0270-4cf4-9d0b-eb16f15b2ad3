﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class RolesQueryHandler : IRolesQueryHandler<Role>
    {
        private readonly IUnitOfWork _unitOfWork;

        public RolesQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Role> GetRoleByIdAsync(Guid id)
        {
            var role = await _unitOfWork.RolesRepository.GetByIdAsync(id);
            return role;
        }

        public async Task<List<Role>> GetRolesByNameAsync(string name)
        {
            var roles = await _unitOfWork.RolesRepository.GetAllAsync();
            return roles.Where(role => role.RoleName.Contains(name, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public async Task<List<Role>> GetAllRolesAsync()
        {
            var roles = await _unitOfWork.RolesRepository.GetAllAsync();
            return roles.ToList();
        }

        public async Task<List<Role>> GetRolesByOrgAsync(Guid id)
        {
            var roles = await _unitOfWork.RolesRepository.GetAllAsync();
            var filteredRoles = roles.Where(r => r.OrganizationId == id).ToList();
            return filteredRoles.ToList();
        }
    }
}
