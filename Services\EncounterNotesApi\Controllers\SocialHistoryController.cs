﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SocialHistoryController : ControllerBase
    {
        private readonly ISocialHistoryCommandHandler<PatientSocialHistory> _HistoryDataHandler;
        private readonly ISocialHistoryQueryHandler<PatientSocialHistory> _HistoryQueryHandler;
        private readonly ILogger<SocialHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public SocialHistoryController(
            ISocialHistoryCommandHandler<PatientSocialHistory> HistoryDataHandler,
            ISocialHistoryQueryHandler<PatientSocialHistory> HistoryQueryHandler,
            ILogger<SocialHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _HistoryDataHandler = HistoryDataHandler;
            _HistoryQueryHandler = HistoryQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get all History by Patient ID
        /// </summary>
        [HttpGet("{patientid:guid}")]
        public async Task<ActionResult<List<PatientSocialHistory>>> GetAllByPatientId(Guid patientid)
        {
            ActionResult<List<PatientSocialHistory>> result;
            try
            {
                var history = await _HistoryQueryHandler.GetAllByPatientId(patientid)
                              ?? new List<PatientSocialHistory>();
                result = Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get Active History by Patient ID
        /// </summary>
        [HttpGet("{patientid:guid}/isActive")]
        public async Task<ActionResult<List<PatientSocialHistory>>> GetbypatientidandisActive(Guid patientid)
        {
            ActionResult<List<PatientSocialHistory>> result;
            try
            {
                var history = await _HistoryQueryHandler.GetHistoryByIdAndIsActive(patientid)
                              ?? new List<PatientSocialHistory>();

                result = Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add a list of history entities
        /// </summary>
        [HttpPost("addlist")]
        public async Task<IActionResult> Addentity([FromBody] List<PatientSocialHistory> histories)
        {
            IActionResult result;
            if (histories == null || histories.Count == 0)
            {
                result = Ok(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _HistoryDataHandler.AddHistory(histories);
                    result = Ok(_localizer["SuccessfulRegistration"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(500, _localizer["PostLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update a list of history entities
        /// </summary>
        [HttpPut("updatelist")]
        public async Task<IActionResult> Updateentity([FromBody] List<PatientSocialHistory> histories)
        {
            IActionResult result;
            if (histories == null || histories.Count == 0)
            {
                result = Ok(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _HistoryDataHandler.UpdateHistoryList(histories);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(500, _localizer["UpdateLogError"]);
                }
            }
            return result;
        }
    }
}
