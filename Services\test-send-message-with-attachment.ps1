# Test sending message with attachment using multipart form data
$uri = "http://localhost:5000/api/Message/send"

Write-Host "Sending message with attachment..."
Write-Host "Attachment file: test-attachment.txt"

# Read file content
$fileContent = [System.IO.File]::ReadAllBytes("test-attachment.txt")
$fileName = "test-attachment.txt"

# Create boundary
$boundary = [System.Guid]::NewGuid().ToString()

# Create multipart form data
$LF = "`r`n"
$bodyLines = @(
    "--$boundary",
    "Content-Disposition: form-data; name=`"senderName`"$LF",
    "<PERSON> Johnson",
    "--$boundary",
    "Content-Disposition: form-data; name=`"senderEmailId`"$LF",
    "<EMAIL>",
    "--$boundary",
    "Content-Disposition: form-data; name=`"receiverName`"$LF",
    "Bob Smith",
    "--$boundary",
    "Content-Disposition: form-data; name=`"receiverEmailId`"$LF",
    "<EMAIL>",
    "--$boundary",
    "Content-Disposition: form-data; name=`"messageContent`"$LF",
    "Test message with attachment for user management debugging",
    "--$boundary",
    "Content-Disposition: form-data; name=`"attachments`"; filename=`"$fileName`"",
    "Content-Type: text/plain$LF"
)

$bodyString = $bodyLines -join $LF
$bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($bodyString)
$fileBytes = $fileContent
$endBytes = [System.Text.Encoding]::UTF8.GetBytes("$LF--$boundary--$LF")

# Combine all bytes
$totalBytes = $bodyBytes + $fileBytes + $endBytes

try {
    $response = Invoke-RestMethod -Uri $uri -Method POST -Body $totalBytes -ContentType "multipart/form-data; boundary=$boundary"
    Write-Host "Success! Response:"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error occurred:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
