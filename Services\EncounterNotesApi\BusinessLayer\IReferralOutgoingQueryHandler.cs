﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IReferralOutgoingQueryHandler<TText>
    {
        Task<IEnumerable<PatientReferralOutgoing>> GetPatientReferralOutgoingByIdAndIsActive(Guid id);
        Task<IEnumerable<PatientReferralOutgoing>> GetAllPatientReferralOutgoingbyId(Guid id);
    }
}
