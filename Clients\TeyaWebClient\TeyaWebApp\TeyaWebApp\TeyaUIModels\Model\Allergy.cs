﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Allergy
    {
        public string? Classification { get; set; }
        public string? Agent { get; set; }
        public string? Reaction { get; set; }
        public string? Type { get; set; }
        public Guid MedicineId { get; set; }
        public Guid PatientId { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public Guid? OrganizationId { get; set; }
        public Guid? PCPId { get; set; }
        public string? DrugName { get; set; }
        public string? DrugDetails { get; set; }
        public DateTime? CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public bool IsActive { get; set; }
        public string? AllergyInfo { get; set; }
        public string? Substance { get; set; }

        [JsonIgnore] 
        public bool IsNew { get; set; } = false;
    }
}
