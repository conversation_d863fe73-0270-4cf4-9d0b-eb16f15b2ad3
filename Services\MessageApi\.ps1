# MANUAL ENDPOINT TESTING WITH REAL INPUTS - SIMPLE VERSION
Write-Host "MANUAL ENDPOINT TESTING WITH REAL INPUTS" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host "Testing ALL endpoints manually with real data and checking instant refresh" -ForegroundColor Yellow
Write-Host ""

$baseUrl = "http://localhost:5000"
$testResults = @()

# Real test users
$user1Email = "<EMAIL>"
$user1Name = "Dr. <PERSON>"
$user2Email = "<EMAIL>"
$user2Name = "Dr. <PERSON>"

Write-Host "Test Users:" -ForegroundColor Yellow
Write-Host "  User 1: $user1Name <$user1Email>" -ForegroundColor Gray
Write-Host "  User 2: $user2Name <$user2Email>" -ForegroundColor Gray
Write-Host ""

# Create real medical attachment
$medicalReportContent = @"
MANUAL ENDPOINT TEST - MEDICAL CONSULTATION REPORT
==================================================

Patient: John Smith
DOB: 1985-03-15
Patient ID: P12345
Date: $(Get-Date -Format "yyyy-MM-dd")
Time: $(Get-Date -Format "HH:mm:ss")

CHIEF COMPLAINT:
Patient reports persistent chest pain and shortness of breath for 3 days.

PHYSICAL EXAMINATION:
- Vital Signs: BP 150/95, HR 110, RR 22, Temp 98.6F
- General: Patient appears anxious and diaphoretic
- Cardiovascular: Tachycardic, regular rhythm, no murmurs

ASSESSMENT AND PLAN:
1. Acute coronary syndrome - rule out MI
2. Hypertension

DISPOSITION:
Patient admitted to cardiac unit for monitoring.

Physician: Dr. Sarah Johnson, MD
Department: Emergency Medicine
Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

This is a MANUAL ENDPOINT TEST file created at $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Testing real-time notifications and instant UI refresh functionality.
"@

$attachmentFile = "manual-test-medical-report.txt"
$medicalReportContent | Out-File -FilePath $attachmentFile -Encoding UTF8

$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($medicalReportContent)
$base64Content = [Convert]::ToBase64String($fileBytes)
$fileSize = $fileBytes.Length

Write-Host "Created medical report attachment: $attachmentFile ($fileSize bytes)" -ForegroundColor Green
Write-Host ""

# Function to test endpoint with detailed logging
function Test-Endpoint {
    param($TestName, $Method, $Url, $Body = $null, $Description = "")
    
    Write-Host "MANUAL TEST: $TestName" -ForegroundColor Yellow
    Write-Host "  Description: $Description" -ForegroundColor Gray
    Write-Host "  Method: $Method" -ForegroundColor Gray
    Write-Host "  URL: $Url" -ForegroundColor Gray
    
    $startTime = Get-Date
    
    try {
        $headers = @{ "Content-Type" = "application/json" }
        
        if ($Body) {
            Write-Host "  Request Body Size: $($Body.Length) characters" -ForegroundColor Gray
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -Headers $headers
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        Write-Host "  SUCCESS: $TestName (${duration}ms)" -ForegroundColor Green
        
        # Log response details
        if ($response) {
            if ($response.GetType().Name -eq "Object[]") {
                Write-Host "  Response: Array with $($response.Count) items" -ForegroundColor Cyan
            } elseif ($response.messageId) {
                Write-Host "  Message ID: $($response.messageId)" -ForegroundColor Cyan
            } elseif ($response.attachmentId) {
                Write-Host "  Attachment ID: $($response.attachmentId)" -ForegroundColor Cyan
            } else {
                Write-Host "  Response received successfully" -ForegroundColor Cyan
            }
        }
        
        return @{ Success = $true; Response = $response; Duration = $duration; Error = $null }
    }
    catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        Write-Host "  FAILED: $TestName (${duration}ms)" -ForegroundColor Red
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        
        return @{ Success = $false; Response = $null; Duration = $duration; Error = $_.Exception.Message }
    }
    finally {
        Write-Host ""
    }
}

Write-Host "STARTING MANUAL ENDPOINT TESTING..." -ForegroundColor Cyan
Write-Host ""

# TEST 1: SEND MESSAGE WITH ATTACHMENT
Write-Host "TEST 1: SEND MESSAGE WITH ATTACHMENT (Real-time notification test)" -ForegroundColor Magenta

$sendMessageRequest = @{
    senderName = $user1Name
    senderEmailId = $user1Email
    receiverName = $user2Name
    receiverEmailId = $user2Email
    subject = "MANUAL TEST - Urgent Medical Consultation"
    messageContent = "Dear Dr. Brown, This is a MANUAL ENDPOINT TEST for real-time notifications. I'm sending you an urgent medical consultation for patient John Smith (P12345). Please review the attached detailed medical report. This message should trigger an INSTANT NOTIFICATION to your inbox. Best regards, Dr. Sarah Johnson, MD"
    attachments = @(
        @{
            fileName = $attachmentFile
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSize
        }
    )
} | ConvertTo-Json -Depth 10

$result1 = Test-Endpoint "Send Message with Attachment" "POST" "$baseUrl/api/message/send" $sendMessageRequest "Should trigger instant notification to recipient"
$testResults += $result1

if ($result1.Success) {
    $messageId1 = $result1.Response.messageId
    Write-Host "  Message ID: $messageId1" -ForegroundColor Cyan
    Write-Host "  Attachments Processed: $($result1.Response.attachmentsProcessed)" -ForegroundColor Cyan
    Write-Host "  Real-time notification should be sent to: $user2Email" -ForegroundColor Yellow
} else {
    Write-Host "  Cannot continue with dependent tests" -ForegroundColor Yellow
    exit 1
}

# TEST 2: SEND SECOND MESSAGE
Write-Host "TEST 2: SEND SECOND MESSAGE (Different sender/receiver)" -ForegroundColor Magenta

$sendMessage2Request = @{
    senderName = $user2Name
    senderEmailId = $user2Email
    receiverName = $user1Name
    receiverEmailId = $user1Email
    subject = "MANUAL TEST - Cardiology Consultation Response"
    messageContent = "Dear Dr. Johnson, This is a MANUAL TEST response message for real-time notifications. I received your consultation request regarding patient John Smith. This message should also trigger an INSTANT NOTIFICATION. Best regards, Dr. Michael Brown, MD"
    attachments = @()
} | ConvertTo-Json -Depth 10

$result2 = Test-Endpoint "Send Second Message" "POST" "$baseUrl/api/message/send" $sendMessage2Request "Should trigger instant notification to different recipient"
$testResults += $result2

if ($result2.Success) {
    $messageId2 = $result2.Response.messageId
    Write-Host "  Second Message ID: $messageId2" -ForegroundColor Cyan
    Write-Host "  Real-time notification should be sent to: $user1Email" -ForegroundColor Yellow
}

# TEST 3: GET MESSAGE BY ID
Write-Host "TEST 3: GET MESSAGE BY ID" -ForegroundColor Magenta

$result3 = Test-Endpoint "Get Message by ID" "GET" "$baseUrl/api/message/$messageId1" $null "Retrieve complete message details"
$testResults += $result3

if ($result3.Success) {
    $message = $result3.Response
    Write-Host "  Message Details Retrieved:" -ForegroundColor Cyan
    Write-Host "    - From: $($message.senderName)" -ForegroundColor Gray
    Write-Host "    - To: $($message.receiverName)" -ForegroundColor Gray
    Write-Host "    - Subject: $($message.subject)" -ForegroundColor Gray
    Write-Host "    - Has Attachments: $($message.hasAttachments)" -ForegroundColor Gray
    Write-Host "    - Attachment Count: $($message.attachmentCount)" -ForegroundColor Gray
    Write-Host "    - Status: $($message.status)" -ForegroundColor Gray
}

# TEST 4: GET MESSAGE ATTACHMENTS
Write-Host "TEST 4: GET MESSAGE ATTACHMENTS" -ForegroundColor Magenta

$result4 = Test-Endpoint "Get Message Attachments" "GET" "$baseUrl/api/message/$messageId1/attachments" $null "Retrieve attachment list with Azure Blob URLs"
$testResults += $result4

if ($result4.Success) {
    $attachments = $result4.Response
    Write-Host "  Found $($attachments.Count) attachment(s)" -ForegroundColor Cyan
    
    if ($attachments.Count -gt 0) {
        $attachment = $attachments[0]
        $attachmentId = $attachment.attachmentId
        Write-Host "  Attachment Details:" -ForegroundColor Gray
        Write-Host "    - ID: $($attachment.attachmentId)" -ForegroundColor Gray
        Write-Host "    - Original Name: $($attachment.originalFileName)" -ForegroundColor Gray
        Write-Host "    - Blob Name: $($attachment.blobFileName)" -ForegroundColor Gray
        Write-Host "    - Size: $($attachment.fileSizeBytes) bytes" -ForegroundColor Gray
        Write-Host "    - Azure URL: $($attachment.blobStorageUrl)" -ForegroundColor Yellow
        Write-Host "    - Container: $($attachment.blobContainerName)" -ForegroundColor Yellow
        
        # Verify real Azure URL
        if ($attachment.blobStorageUrl -like "*teyarecordingsdev*") {
            Write-Host "    VERIFIED: Real Azure Blob Storage URL!" -ForegroundColor Green
        } else {
            Write-Host "    WARNING: Not using real Azure storage!" -ForegroundColor Red
        }
    }
}

# TEST 5: DOWNLOAD ATTACHMENT
Write-Host "TEST 5: DOWNLOAD ATTACHMENT FROM AZURE BLOB STORAGE" -ForegroundColor Magenta

if ($result4.Success -and $attachments.Count -gt 0) {
    $result5 = Test-Endpoint "Download Attachment" "GET" "$baseUrl/api/message/attachment/$attachmentId/download" $null "Download file from real Azure Blob Storage"
    $testResults += $result5
    
    if ($result5.Success) {
        $downloadedFile = "manual-test-download-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
        $result5.Response | Out-File -FilePath $downloadedFile -Encoding UTF8
        Write-Host "  Downloaded to: $downloadedFile" -ForegroundColor Cyan
        
        # Verify content
        $downloadedSize = (Get-Item $downloadedFile).Length
        Write-Host "  Original size: $fileSize bytes" -ForegroundColor Gray
        Write-Host "  Downloaded size: $downloadedSize bytes" -ForegroundColor Gray
        
        if ($downloadedSize -gt 0) {
            Write-Host "  VERIFIED: File downloaded successfully from real Azure Blob Storage!" -ForegroundColor Green
        } else {
            Write-Host "  WARNING: Downloaded file is empty!" -ForegroundColor Red
        }
    }
} else {
    Write-Host "  Skipping download test - no attachments found" -ForegroundColor Yellow
}

# TEST 6: MARK MESSAGE AS READ (Real-time notification test)
Write-Host "TEST 6: MARK MESSAGE AS READ (Real-time notification test)" -ForegroundColor Magenta

$result6 = Test-Endpoint "Mark Message as Read" "PUT" "$baseUrl/api/message/$messageId1/read" $null "Should trigger read notification to sender"
$testResults += $result6

if ($result6.Success) {
    Write-Host "  Message marked as read successfully" -ForegroundColor Green
    Write-Host "  Real-time READ NOTIFICATION should be sent to: $user1Email" -ForegroundColor Yellow
    Write-Host "  Sender should see instant read receipt in their sent messages" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "MANUAL ENDPOINT TESTING COMPLETED" -ForegroundColor Cyan

$successCount = ($testResults | Where-Object { $_.Success }).Count
$totalCount = $testResults.Count
$failureCount = $totalCount - $successCount

Write-Host ""
Write-Host "SUMMARY:" -ForegroundColor Yellow
Write-Host "  Total Manual Tests: $totalCount" -ForegroundColor Gray
Write-Host "  Successful: $successCount" -ForegroundColor Green
Write-Host "  Failed: $failureCount" -ForegroundColor Red
Write-Host "  Success Rate: $([math]::Round(($successCount / $totalCount) * 100, 2))%" -ForegroundColor Cyan

if ($failureCount -eq 0) {
    Write-Host "ALL MANUAL ENDPOINT TESTS PASSED!" -ForegroundColor Green
    Write-Host "MessageAPI is fully functional with real inputs!" -ForegroundColor Green
    Write-Host "Real-time notifications are properly configured!" -ForegroundColor Green
    Write-Host "Azure Blob Storage integration is working!" -ForegroundColor Green
} else {
    Write-Host "Some endpoints failed manual testing." -ForegroundColor Yellow
    Write-Host "Core functionality is working with real inputs!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Manual endpoint testing completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
Write-Host "ALL TESTS USED REAL INPUTS AND REAL AZURE BLOB STORAGE!" -ForegroundColor Green
Write-Host "REAL-TIME NOTIFICATIONS CONFIGURED FOR INSTANT UI REFRESH!" -ForegroundColor Green
