﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IMedicalHistoryService
    {
        Task<List<MedicalHistoryDTO>> GetAllByIdAsync(Guid id);
        Task<List<MedicalHistoryDTO>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddMedicalHistoryAsync(List<MedicalHistoryDTO> medicalHistories);
        Task UpdateMedicalHistoryAsync(MedicalHistoryDTO medicalHistory);
        Task UpdateMedicalHistoryListAsync(List<MedicalHistoryDTO> medicalHistories);
        Task DeleteMedicalHistoryByEntityAsync(MedicalHistoryDTO medicalHistory);
    }
}