﻿using Moq;
using Microsoft.Extensions.Configuration;
using PracticeDataAccessLayer.Implementation;
using PracticeBusinessLayer.QueryHandler;
using PracticeContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace PracticeBusinessLayerTest
{
    public class PracticeBusinessLayerQueryHandlerPracticeTest
    {
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IPracticeRepository> _mockPracticeRepository;
        private readonly PracticeQueryHandler _queryHandler;

        public PracticeBusinessLayerQueryHandlerPracticeTest()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPracticeRepository = new Mock<IPracticeRepository>();

            // Setup repository to return mock repository
            _mockUnitOfWork.Setup(uow => uow.PracticeRepository).Returns(_mockPracticeRepository.Object);
            _queryHandler = new PracticeQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Fact]
        public async Task GetTasks_ReturnsAllTasks()
        {
            // Arrange
            var tasks = new List<Tasks>
            {
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    PatientName = "P01",
                    TaskType = "Checkup",
                    Status = "Pending",
                    AssignedTo = "D01",
                    Priority = "High",
                    Frequency = 1,
                    Subject = "Routine checkup",
                    DueDate = DateTime.Today.AddDays(5),
                    Notes = "Patient is stable",
                    LastVisitDate = DateTime.Today.AddMonths(-1),
                    LastDueDate = DateTime.Today.AddDays(7),
                    CreationDate = DateTime.Today.AddMonths(-1),
                    StartDate = DateTime.Now,
                    CreatedBy = "Admin",
                    RecurringAction = true
                },
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    PatientName = "P02",
                    TaskType = "Follow-up",
                    Status = "Completed",
                    AssignedTo = "D01",
                    Priority = "Medium",
                    Frequency = 2,
                    Subject = "Post-op Follow-up",
                    DueDate = DateTime.Today.AddDays(10),
                    Notes = "Follow-up for surgery",
                    LastVisitDate = DateTime.Today.AddMonths(-2),
                    LastDueDate = DateTime.Today.AddDays(12),
                    CreationDate = DateTime.Today.AddMonths(-2),
                    StartDate = DateTime.Now.AddDays(-1),
                    CreatedBy = "Admin",
                    RecurringAction = false
                }
            };

            _mockPracticeRepository.Setup(repo => repo.GetAllAsync()).ReturnsAsync(tasks);

            // Act
            var result = await _queryHandler.GetTasks();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            _mockPracticeRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetTaskById_ReturnsCorrectTask()
        {
            // Arrange
            var taskId = Guid.NewGuid();
            var task = new Tasks
            {
                Id = taskId,
                PatientName = "P01",
                TaskType = "Checkup",
                Status = "Pending",
                AssignedTo = "D01",
                Priority = "High",
                Frequency = 1,
                Subject = "Routine checkup",
                DueDate = DateTime.Today.AddDays(5),
                Notes = "Patient is stable",
                LastVisitDate = DateTime.Today.AddMonths(-1),
                LastDueDate = DateTime.Today.AddDays(7),
                CreationDate = DateTime.Today.AddMonths(-1),
                StartDate = DateTime.Now,
                CreatedBy = "Admin",
                RecurringAction = true
            };

            _mockPracticeRepository.Setup(repo => repo.GetByIdAsync(taskId)).ReturnsAsync(task);

            // Act
            var result = await _queryHandler.GetTaskById(taskId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(taskId, result.Id);
            _mockPracticeRepository.Verify(repo => repo.GetByIdAsync(taskId), Times.Once);
        }

        [Fact]
        public async Task GetTaskById_ReturnsNull_WhenTaskNotFound()
        {
            // Arrange
            var taskId = Guid.NewGuid();

            _mockPracticeRepository.Setup(repo => repo.GetByIdAsync(taskId)).ReturnsAsync((Tasks?)null);

            // Act
            var result = await _queryHandler.GetTaskById(taskId);

            // Assert
            Assert.Null(result);
            _mockPracticeRepository.Verify(repo => repo.GetByIdAsync(taskId), Times.Once);
        }

        [Fact]
        public async Task GetTasks_ReturnsEmptyList_WhenNoTasks()
        {
            // Arrange
            var tasks = new List<Tasks>();
            _mockPracticeRepository.Setup(repo => repo.GetAllAsync()).ReturnsAsync(tasks);

            // Act
            var result = await _queryHandler.GetTasks();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
            _mockPracticeRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetTasks_ReturnsCorrectTaskWithSpecificProperties()
        {
            // Arrange
            var tasks = new List<Tasks>
            {
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    PatientName = "P01",
                    TaskType = "Checkup",
                    Status = "Pending",
                    AssignedTo = "D01",
                    Priority = "High",
                    Frequency = 1,
                    Subject = "Routine checkup",
                    DueDate = DateTime.Today.AddDays(5),
                    Notes = "Patient is stable",
                    LastVisitDate = DateTime.Today.AddMonths(-1),
                    LastDueDate = DateTime.Today.AddDays(7),
                    CreationDate = DateTime.Today.AddMonths(-1),
                    StartDate = DateTime.Now,
                    CreatedBy = "Admin",
                    RecurringAction = true
                },
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    PatientName = "P02",
                    TaskType = "Follow-up",
                    Status = "Completed",
                    AssignedTo = "D02",
                    Priority = "Medium",
                    Frequency = 2,
                    Subject = "Post-op Follow-up",
                    DueDate = DateTime.Today.AddDays(10),
                    Notes = "Follow-up for surgery",
                    LastVisitDate = DateTime.Today.AddMonths(-2),
                    LastDueDate = DateTime.Today.AddDays(12),
                    CreationDate = DateTime.Today.AddMonths(-2),
                    StartDate = DateTime.Now.AddDays(-1),
                    CreatedBy = "Admin",
                    RecurringAction = false
                }
            };

            _mockPracticeRepository.Setup(repo => repo.GetAllAsync()).ReturnsAsync(tasks);

            // Act
            var result = await _queryHandler.GetTasks();

            // Assert
            Assert.NotNull(result);
            Assert.Contains(result, t => t.PatientName == "P02" && t.AssignedTo == "D02");
            Assert.Contains(result, t => t.Priority == "Medium");
            Assert.DoesNotContain(result, t => t.Priority == "Low");
            _mockPracticeRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }
    }
}
