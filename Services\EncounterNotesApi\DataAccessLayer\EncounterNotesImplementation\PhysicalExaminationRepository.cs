﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class PhysicalExaminationRepository : GenericRepository<PhysicalExamination>, IPhysicalExaminationRepository
    {
        private readonly RecordDatabaseContext _context;

        public PhysicalExaminationRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<PhysicalExamination>> GetAllExaminationsAsync()
        {
            return await _context.PhysicalExaminations
                .AsNoTracking()
                .ToListAsync();
        }
    }
}