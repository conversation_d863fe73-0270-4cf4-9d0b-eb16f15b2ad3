﻿@using Syncfusion.Blazor.Layouts
@using MudBlazor
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
<MudPaper Class="custom-paper">
    <SfSplitter ID="splitter" Height="600px" Width="100%" SeparatorSize="5">
        <SplitterPanes>
            <SplitterPane Size="70%" Min="30%" Max="85%">
                <MudPaper Class="left-panel">
                    <div class="complaint-list">
                        <div class="column-headers">
                            <span class="assessment-header">@Localizer["Assessments / Recorded Date"]</span>
                            <span class="complaint-header">@Localizer["Related ChiefComplaints"]</span>
                        </div>
                        @foreach (var assessment in assessments)
                        {
                            <MudCard Class="assessment-card">
                                <MudCardContent>
                                    <div class="complaint-header">
                                        <span class="complaint-text">@assessment.Diagnosis / @assessment.CreatedDate</span>
                                        <span class="complaint-date">@assessment.CheifComplaint</span>
                                    </div>

                                    <div class="toggle-section">
                                        <MudIconButton Icon="@Icons.Material.Filled.Medication"
                                        Color="Color.Primary"
                                        Title="Edit All Medications"
                                        OnClick="()=>ToggleMedicationVisibility(assessment.CheifComplaintId)" />
                                        <MudIconButton Icon="@Icons.Material.Filled.Biotech"
                                        Color="Color.Success"
                                        Title="Edit All Lab Tests"
                                        OnClick="()=>ToggleLabsVisibility(assessment.AssessmentsID)" />
                                        <MudIconButton Icon="@Icons.Material.Filled.MedicalServices"
                                        Color="Color.Secondary"
                                        Title="Edit All Procedure"
                                        OnClick="()=>ToggleProcedureVisibility(assessment.AssessmentsID)" />
                                    </div>

                                    <div class="medication-list">
                                        <h3>Medications(Rx)</h3>
                                        @if (visibleAssessmentDiagnoses.TryGetValue(assessment.AssessmentsID, out var diagnosis) &&
                                    medicationRelatedToAssessments.TryGetValue(diagnosis, out var medications))
                                        {
                                            @foreach (var medication in medications)
                                            {
                                                <MudChip T="string" Color="Color.Info">@medication</MudChip>
                                            }
                                        }
                                        else
                                        {
                                            <MudChip T="string" Color="Color.Info">No Medications Found</MudChip>
                                        }
                                    </div>
                                    <div class="labs-list">
                                        <h3>Labs</h3>
                                        @if (visibleAssessmentDiagnoses.TryGetValue(assessment.AssessmentsID, out var diagnosiss) &&
                                     labsRelatedToAssessments?.Where(l => l.AssessmentData == diagnosis).Any() == true)
                                        {
                                            @foreach (var lab in labsRelatedToAssessments?.Where(l => l.AssessmentData == diagnosiss))
                                            {
                                                <MudChip T="string" Color="Color.Info" Class="lab-chip">
                                                    @if (!string.IsNullOrEmpty(lab.LabTest1))
                                                    {
                                                        <span>@lab.LabTest1</span>
                                                    }
                                                </MudChip>
                                            }
                                        }
                                        else
                                        {
                                            <MudChip T="string" Color="Color.Info">No Labs Found</MudChip>
                                        }
                                    </div>
                                    <div class="Procedure-list">
                                        <h3>Procedures</h3>
                                        @if(visibleAssessmentDiagnoses.TryGetValue(assessment.AssessmentsID,out var diagnostic) &&
                                       proceduresRelatedToAssessments?.Where(p=>p.AssessmentData==diagnosiss).Any()==true)
                                        {
                                            @foreach (var procedure in proceduresRelatedToAssessments?.Where(p => p.AssessmentData == diagnostic))
                                            {
                                                <MudChip T="string" Color="Color.Info">
                                                    @if (!string.IsNullOrEmpty(procedure.CPTCode))
                                                    {
                                                    <span>@procedure.CPTCode</span>
                                                    }
                                                    @if(!string.IsNullOrEmpty(procedure.Notes))
                                                    {
                                                        <span> - @procedure.Notes</span>
                                                    }
                                                </MudChip>
                                            }
                                        }
                                    </div>
                                </MudCardContent>
                            </MudCard>
                        }
                    </div>
                </MudPaper>
            </SplitterPane>
            <SplitterPane Size="30%" Min="15%" Max="50%">
                @if (selectedComponent == ComponentType.Medication)
                {
                    <CurrentMedicationGrid ChiefComplaintId="@selectedChiefComplaintId" OnChange="HandleGridChange"></CurrentMedicationGrid>
                }
                else if (selectedComponent == ComponentType.Labs)
                {
                    <PlanLabsGrid SelectedAssessmentId="@selectedAssessmentId" OnChange="HandleGridChange"></PlanLabsGrid>
                }
                else if(selectedComponent == ComponentType.Procedure)
                {
                    <ProcedureGrid SelectedAssessmentID="@selectedAssessmentId" OnChange="HandleGridChange"></ProcedureGrid>
                }
                else
                {
                    <MudPaper Class="edit-placeholder">
                        <p>@Localizer["Click on edit to see edit properties"]</p>
                    </MudPaper>
                }
            </SplitterPane>
        </SplitterPanes>
    </SfSplitter>
</MudPaper>
<style>
    .column-headers {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;
    font-weight: bold;
    }

    .custom-paper, .left-panel {
    padding: 15px;
    background: #f9f9f9;
    border-radius: 10px;
    }

    .complaint-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    }

    .assessment-card {
    border-radius: 10px;
    transition: 0.3s;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    }

    .complaint-header {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    font-weight: bold;
    }

    .toggle-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    }

    .medication-list {
    margin-top: 10px;
    padding: 10px;
    background: #eaf4fc;
    border-radius: 8px;
    }

    .edit-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 16px;
    color: gray;
    background: #f4f4f4;
    border-radius: 10px;
    }

    .labs-list {
    margin-top: 10px;
    padding: 10px;
    background: #cbf5dd;
    border-radius: 8px;
    }

    .Procedure-list{
        margin-top: 10px;
        padding: 10px;
        background: #ebe8fc;
        border-radius: 8px;
    }

    .lab-chip {
    margin: 2px;
    }
</style>

