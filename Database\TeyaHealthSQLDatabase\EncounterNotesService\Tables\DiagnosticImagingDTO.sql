﻿CREATE TABLE [EncounterNotesService].[DiagnosticImagingDTO] (
    [RecordID]       UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [OrganizationID] UNIQUEIDENTIFIER NULL,
    [PatientId]      UNIQUEIDENTIFIER NULL,
    [Status]         NVARCHAR (100)   NULL,
    [Provider]       NVARCHAR (255)   NULL,
    [Facility]       NVARCHAR (255)   NULL,
    [AssignedTo]     NVARCHAR (255)   NULL,
    [FutureOrder]    BIT              DEFAULT ((0)) NOT NULL,
    [HigherPriority] BIT              DEFAULT ((0)) NOT NULL,
    [InHouse]        BIT              DEFAULT ((0)) NOT NULL,
    [Procedgure]     NVARCHAR (255)   NULL,
    [OrderDate]      DATETIME         NULL,
    [Reason]         NVARCHAR (MAX)   NULL,
    [Recieved]       BIT              DEFAULT ((0)) NOT NULL,
    [Date]           DATETIME         NULL,
    [Result]         NVARCHAR (MAX)   NULL,
    [Notes]          NVARCHAR (MAX)   NULL,
    [ClassicalInfo]  NVARCHAR (MAX)   NULL,
    [InternalNotes]  NVARCHAR (MAX)   NULL,
    [IsActive]       BIT              DEFAULT ((1)) NOT NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER NULL,
    [CreatedDate]    DATETIME         DEFAULT (getdate()) NULL,
    [UpdatedDate]    DATETIME         NULL,
    PRIMARY KEY CLUSTERED ([RecordID] ASC)
);

