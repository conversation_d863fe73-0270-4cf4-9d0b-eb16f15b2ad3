﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AssessmentsController : ControllerBase
    {
        private readonly IAssessmentsCommandHandler<AssessmentsData> _AssessmentsDataHandler;
        private readonly IAssessmentsQueryHandler<AssessmentsData> _AssessmentsQueryHandler;
        private readonly ILogger<AssessmentsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public AssessmentsController(
            IAssessmentsCommandHandler<AssessmentsData> medicalDataHandler,
            IAssessmentsQueryHandler<AssessmentsData> medicalQueryHandler,
            ILogger<AssessmentsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _AssessmentsDataHandler = medicalDataHandler;
            _AssessmentsQueryHandler = medicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<AssessmentsData>>> GetAllById(Guid id)
        {
            ActionResult<IEnumerable<AssessmentsData>> result;

            try
            {
                var data = await _AssessmentsQueryHandler.GetAllAssessmentsById(id);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }
        [HttpGet("{PatientId:guid}/AssesmentsRelatedMedications")]
        public async Task<ActionResult<List<string>>> GetAllMedicationsRelatedToAssessments(Guid PatientId)
        {
            ActionResult<List<string>> result;
            try
            {
                result = await _AssessmentsQueryHandler.GetAllMedications(PatientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("{id:guid}/IsActive")]
        public async Task<ActionResult<IEnumerable<AssessmentsData>>> GetAllByIdAndIsActive(Guid id)
        {
            ActionResult<IEnumerable<AssessmentsData>> result;

            try
            {
                var diagnosis = await _AssessmentsQueryHandler.GetAssessmentsByIdAndIsActive(id);
                result = diagnosis != null ? Ok(diagnosis) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpPost]
        [Route("AddAssessments")]
        public async Task<IActionResult> AddAssessments([FromBody] List<AssessmentsData> _Assessments)
        {
            IActionResult result;

            if (_Assessments == null || _Assessments.Count == 0)
            {
                result = BadRequest(_localizer["NoLicense"]);
            }
            else
            {
                try
                {
                    await _AssessmentsDataHandler.AddAssessments(_Assessments);
                    result = Ok(_localizer["SuccessfulRegistration"]);
                }
                catch (SqlException)
                {
                    result = StatusCode(500, _localizer["DatabaseError"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(500);
                }
            }

            return result;
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] AssessmentsData _Assessments)
        {
            IActionResult result;

            if (_Assessments == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _AssessmentsDataHandler.DeleteAssessmentsByEntity(_Assessments);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(500, _localizer["DeleteLogError"]);
                }
            }

            return result;
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateAssessmentsById(Guid id, [FromBody] AssessmentsData _Assessments)
        {
            IActionResult result;

            if (_Assessments == null || _Assessments.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _AssessmentsDataHandler.UpdateAssessments(_Assessments);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(500, _localizer["UpdateLogError"]);
                }
            }

            return result;
        }

        [HttpPut]
        [Route("UpdateAssessmentsList")]
        public async Task<IActionResult> UpdateAssessmentsList([FromBody] List<AssessmentsData> _Assessments)
        {
            IActionResult result;

            try
            {
                await _AssessmentsDataHandler.UpdateAssessmentsList(_Assessments);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(500, _localizer["UpdateLogError"]);
            }

            return result;
        }
    }
}