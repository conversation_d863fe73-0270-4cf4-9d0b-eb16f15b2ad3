using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;

namespace MessageBusinessLayer
{
    [Authorize]
    public class MessageHub : Hub
    {
        public async Task JoinUserGroup(string userEmail)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userEmail}");
        }

        public async Task LeaveUserGroup(string userEmail)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"user_{userEmail}");
        }

        public override async Task OnConnectedAsync()
        {
            var userEmail = Context.User?.FindFirst("email")?.Value ?? 
                           Context.User?.FindFirst("preferred_username")?.Value;
            
            if (!string.IsNullOrEmpty(userEmail))
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userEmail}");
            }

            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            await base.OnDisconnectedAsync(exception);
        }
    }
}
