﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class TherapeuticInterventionsQueryHandler : ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public TherapeuticInterventionsQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<TherapeuticInterventionsData>> GetAllTherapeuticInterventionsById(Guid id)
        {
            var medicalHistories = await _unitOfWork.TherapeuticInterventionsRepository.GetAllTherapeuticInterventionsAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id);
        }

        public async Task<IEnumerable<TherapeuticInterventionsData>> GetTherapeuticInterventionsByIdAndIsActive(Guid id)
        {
            var medicalHistories = await _unitOfWork.TherapeuticInterventionsRepository.GetAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id && medHistory.IsActive == true);
        }
    }
}
