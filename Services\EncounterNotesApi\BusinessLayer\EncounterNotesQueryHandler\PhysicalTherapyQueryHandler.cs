﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class PhysicalTherapyQueryHandler : IPhysicalTherapyQueryHandler<PhysicalTherapyData>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PhysicalTherapyQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PhysicalTherapyData>> GetAllPhysicalTherapyById(Guid id)
        {
            var medicalHistories = await _unitOfWork.PhysicalTherapyRepository.GetAllPhysicalTherapyAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id);
        }

        public async Task<IEnumerable<PhysicalTherapyData>> GetPhysicalTherapyByIdAndIsActive(Guid id)
        {
            var medicalHistories = await _unitOfWork.PhysicalTherapyRepository.GetAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id && medHistory.IsActive == true);
        }
    }
}
