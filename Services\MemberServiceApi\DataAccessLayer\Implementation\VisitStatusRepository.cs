﻿using Contracts;
using DataAccessLayer.Context;
using DataAccessLayer.Implementation;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class VisitStatusRepository : GenericRepository<VisitStatus>, IVisitStatusRepository
    {
        private readonly AccountDatabaseContext _context;

        public VisitStatusRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }

    }
}
