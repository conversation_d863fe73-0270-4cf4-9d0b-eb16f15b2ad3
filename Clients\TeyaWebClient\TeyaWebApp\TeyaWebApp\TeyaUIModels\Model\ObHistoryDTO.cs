﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace TeyaUIModels.Model
{
    public class ObHistoryDTO : IModel
    {
        public Guid obId { get; set; }
        public Guid PatientId { get; set; }
        public string Symptoms { get; set; }
        public string Notes { get; set; }
        public DateTime DateOfComplaint { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid? PcpId { get; set; }

        public bool IsDeleted { get; set; } = false;
    }
}
