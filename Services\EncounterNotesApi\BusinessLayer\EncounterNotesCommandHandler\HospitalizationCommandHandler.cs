﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class HospitalizationRecordCommandHandler : IHospitalizationRecordCommandHandler<HospitalizationRecord>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<HospitalizationRecordCommandHandler> _logger;

        public HospitalizationRecordCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<HospitalizationRecordCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Add new HospitalizationRecord
        /// </summary>
        public async Task AddHospitalizationRecord(List<HospitalizationRecord> hospitalizationRecordList)
        {
            await _unitOfWork.HospitalizationRecordRepository.AddAsync(hospitalizationRecordList);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Update an existing HospitalizationRecord
        /// </summary>
        public async Task UpdateHospitalizationRecord(HospitalizationRecord hospitalizationRecord)
        {
            await _unitOfWork.HospitalizationRecordRepository.UpdateAsync(hospitalizationRecord);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Delete an existing HospitalizationRecord By Id
        /// </summary>
        public async Task DeleteHospitalizationRecordById(Guid id)
        {
            await _unitOfWork.HospitalizationRecordRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Update an existing List of HospitalizationRecords
        /// </summary>
        public async Task<bool> UpdateHospitalizationRecordList(List<HospitalizationRecord> HospitalizationRecordList)
        {
            var existingRecords = await _unitOfWork.HospitalizationRecordRepository.GetAsync();

            foreach (var update in HospitalizationRecordList)
            {
                var recordToUpdate = existingRecords.FirstOrDefault(r => r.RecordID == update.RecordID);

                if (recordToUpdate != null)
                {
                    recordToUpdate.Reason = update.Reason;
                    recordToUpdate.Date = update.Date;
                    recordToUpdate.IsActive = update.IsActive;

                    await _unitOfWork.HospitalizationRecordRepository.UpdateAsync(recordToUpdate);
                }
            }

            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}