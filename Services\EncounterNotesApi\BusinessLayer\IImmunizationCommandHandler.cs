﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IImmunizationCommandHandler<TText>
    {
        Task DeleteImmunizationByEntity(Immunization immunization);
        Task DeleteImmunizationById(Guid id);
        Task AddImmunization(List<TText> texts);
        Task UpdateImmunization(Immunization IMmunization);
        Task UpdateImmunizationList(List<Immunization> immuNization);
    }
}