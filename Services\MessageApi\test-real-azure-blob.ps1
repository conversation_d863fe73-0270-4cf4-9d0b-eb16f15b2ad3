# Test Real Azure Blob Storage Connection
Write-Host "🔧 TESTING REAL AZURE BLOB STORAGE CONNECTION" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

$baseUrl = "http://localhost:5000"
$attachmentFile = "test-medical-report.txt"

# Check if attachment file exists
if (-not (Test-Path $attachmentFile)) {
    Write-Host "❌ ERROR: Attachment file not found: $attachmentFile" -ForegroundColor Red
    exit 1
}

# Read and encode attachment
Write-Host "📎 Reading attachment file: $attachmentFile" -ForegroundColor Yellow
$fileContent = Get-Content $attachmentFile -Raw -Encoding UTF8
$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
$base64Content = [Convert]::ToBase64String($fileBytes)
$fileSize = $fileBytes.Length

Write-Host "📊 File Details:" -ForegroundColor Gray
Write-Host "   - File Name: $attachmentFile" -ForegroundColor Gray
Write-Host "   - File Size: $fileSize bytes" -ForegroundColor Gray
Write-Host "   - Content Type: text/plain" -ForegroundColor Gray

# Create email request with real medical data
$emailRequest = @{
    senderName = "Dr. Sarah Johnson"
    senderEmailId = "<EMAIL>"
    receiverName = "Dr. Michael Brown"
    receiverEmailId = "<EMAIL>"
    subject = "REAL AZURE BLOB TEST - Patient Report"
    messageContent = "This is a REAL Azure Blob Storage test. The attachment should be uploaded to Azure Storage Account: teyarecordingsdev, Container: messageattachment"
    attachments = @(
        @{
            fileName = $attachmentFile
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSize
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host ""
Write-Host "📧 SENDING EMAIL WITH REAL AZURE BLOB STORAGE..." -ForegroundColor Green
Write-Host "   Storage Account: teyarecordingsdev" -ForegroundColor Cyan
Write-Host "   Container: messageattachment" -ForegroundColor Cyan
Write-Host "   From: <EMAIL>" -ForegroundColor Gray
Write-Host "   To: <EMAIL>" -ForegroundColor Gray

try {
    # Send email
    $response = Invoke-RestMethod -Uri "$baseUrl/api/message/send" -Method POST -Body $emailRequest -ContentType "application/json"
    
    Write-Host ""
    Write-Host "✅ EMAIL SENT SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "📨 Message ID: $($response.messageId)" -ForegroundColor Cyan
    Write-Host "📎 Attachments Processed: $($response.attachmentsProcessed)" -ForegroundColor Cyan
    
    $messageId = $response.messageId
    
    if ($messageId) {
        # Test: Get message attachments
        Write-Host ""
        Write-Host "🔍 TESTING: Getting message attachments..." -ForegroundColor Yellow
        $attachmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/message/$messageId/attachments" -Method GET
        
        Write-Host "✅ Found $($attachmentsResponse.Count) attachment(s)" -ForegroundColor Green
        
        foreach ($attachment in $attachmentsResponse) {
            Write-Host ""
            Write-Host "📎 REAL AZURE BLOB ATTACHMENT DETAILS:" -ForegroundColor Cyan
            Write-Host "   - ID: $($attachment.attachmentId)" -ForegroundColor Gray
            Write-Host "   - Original Name: $($attachment.originalFileName)" -ForegroundColor Gray
            Write-Host "   - Blob Name: $($attachment.blobFileName)" -ForegroundColor Gray
            Write-Host "   - Size: $($attachment.fileSizeBytes) bytes" -ForegroundColor Gray
            Write-Host "   - Content Type: $($attachment.contentType)" -ForegroundColor Gray
            Write-Host "   - Blob URL: $($attachment.blobStorageUrl)" -ForegroundColor Yellow
            Write-Host "   - Container: $($attachment.blobContainerName)" -ForegroundColor Yellow
            Write-Host "   - Upload Time: $($attachment.uploadedDateTime)" -ForegroundColor Gray
            
            # Check if this is a real Azure URL (not mock)
            if ($attachment.blobStorageUrl -like "*teyarecordingsdev*") {
                Write-Host "🎉 SUCCESS! File uploaded to REAL Azure Blob Storage!" -ForegroundColor Green
            } elseif ($attachment.blobStorageUrl -like "*mockstorageaccount*") {
                Write-Host "⚠️  WARNING: Still using mock storage - check environment variables!" -ForegroundColor Yellow
            }
            
            # Test: Download attachment
            Write-Host ""
            Write-Host "⬇️ TESTING: Downloading attachment from Azure Blob Storage..." -ForegroundColor Yellow
            $attachmentId = $attachment.attachmentId
            
            try {
                $downloadResponse = Invoke-RestMethod -Uri "$baseUrl/api/message/attachment/$attachmentId/download" -Method GET
                
                # Save downloaded content to verify
                $downloadedFile = "downloaded-real-azure-$($attachment.originalFileName)"
                $downloadResponse | Out-File -FilePath $downloadedFile -Encoding UTF8
                
                Write-Host "✅ Attachment downloaded successfully!" -ForegroundColor Green
                Write-Host "💾 Saved as: $downloadedFile" -ForegroundColor Cyan
                
                # Verify content matches
                $originalContent = Get-Content $attachmentFile -Raw
                $downloadedContent = Get-Content $downloadedFile -Raw
                
                if ($originalContent -eq $downloadedContent) {
                    Write-Host "✅ CONTENT VERIFICATION: Original and downloaded files match!" -ForegroundColor Green
                    Write-Host "🎉 REAL AZURE BLOB STORAGE TEST PASSED!" -ForegroundColor Green
                } else {
                    Write-Host "❌ CONTENT VERIFICATION: Files do not match!" -ForegroundColor Red
                }
                
            } catch {
                Write-Host "❌ ERROR downloading attachment: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "❌ ERROR: No message ID returned from server" -ForegroundColor Red
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🏁 Real Azure Blob Storage test completed." -ForegroundColor Cyan
