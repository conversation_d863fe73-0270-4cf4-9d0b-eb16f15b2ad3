﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IRolesQueryHandler<T>
    {
        Task<T> GetRoleByIdAsync(Guid id);
        Task<List<T>> GetRolesByNameAsync(string name);
        Task<List<T>> GetAllRolesAsync();
        Task<List<T>> GetRolesByOrgAsync(Guid id);
    }
}
