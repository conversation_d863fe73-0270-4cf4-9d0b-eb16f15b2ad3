﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using static System.Net.WebRequestMethods;

namespace TeyaUIViewModels.ViewModel
{
    public class MedicalHistoryService : IMedicalHistoryService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public MedicalHistoryService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<MedicalHistoryDTO>> GetAllByIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/MedicalHistory/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<MedicalHistoryDTO>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<MedicalHistoryDTO>> GetAllByIdAndIsActiveAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/MedicalHistory/{id}/IsActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<MedicalHistoryDTO>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task AddMedicalHistoryAsync(List<MedicalHistoryDTO> medicalHistories)
        {
            var apiUrl = $"{_EncounterNotes}/api/MedicalHistory/AddMedicalHistory";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task UpdateMedicalHistoryAsync(MedicalHistoryDTO medicalHistory)
        {
            var apiUrl = $"{_EncounterNotes}/api/MedicalHistory/{medicalHistory.PatientId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistory);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdateMedicalHistoryListAsync(List<MedicalHistoryDTO> medicalHistories)
        {
            var apiUrl = $"{_EncounterNotes}/api/MedicalHistory/UpdateMedicalHistoryList";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task DeleteMedicalHistoryByEntityAsync(MedicalHistoryDTO medicalHistory)
        {
            if (medicalHistory == null)
            {
                throw new ArgumentNullException(nameof(medicalHistory), _localizer["InvalidRecord"]);
            }
            var apiUrl = $"{_EncounterNotes}/api/MedicalHistory/DeleteMedicalHistory";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistory);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DeleteLogError"]);
            }
        }
    }
}
