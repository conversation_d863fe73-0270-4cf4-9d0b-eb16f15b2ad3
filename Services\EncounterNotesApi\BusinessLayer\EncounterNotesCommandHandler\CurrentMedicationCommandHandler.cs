﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class CurrentMedicationCommandHandler : ICurrentMedicationCommandHandler<CurrentMedication>
    {
        private readonly IUnitOfWork _unitOfWork;
        public CurrentMedicationCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddMedication(List<CurrentMedication> Medications)
        {
            await _unitOfWork.CurrentMedicationRepository.AddAsync(Medications);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateMedication(CurrentMedication medication)
        {
            await _unitOfWork.CurrentMedicationRepository.UpdateAsync(medication);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateMedicationList(List<CurrentMedication> Medications)
        {
            await _unitOfWork.CurrentMedicationRepository.UpdateRangeAsync(Medications);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteMedicationById(Guid id)
        {
            await _unitOfWork.CurrentMedicationRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteMedicationByEntity(CurrentMedication medication)
        {
            await _unitOfWork.CurrentMedicationRepository.DeleteByEntityAsync(medication);
            await _unitOfWork.SaveAsync();
        }
    }
}
