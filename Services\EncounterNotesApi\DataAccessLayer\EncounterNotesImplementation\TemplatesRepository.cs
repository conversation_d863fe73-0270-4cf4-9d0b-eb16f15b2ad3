﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class TemplatesRepository : GenericRepository<Templates>, ITemplatesRepository
    {
        private readonly RecordDatabaseContext _context;

        public TemplatesRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<List<Templates>> GetTemplatesByPCPId(Guid PCPId)
        {
            var templatedata= await _context.Templates
            .Where(t => t.PCPId == PCPId)
            .ToListAsync();
            return templatedata;
        }

        public async Task<List<Templates>> GetTemplatesByPCPIdAndVisitType(Guid PCPId,String VisitType)
        {
            var templatedata = await _context.Templates
            .Where(t => t.PCPId == PCPId && t.VisitType==VisitType)
            .ToListAsync();
            return templatedata;
        }

    }
}