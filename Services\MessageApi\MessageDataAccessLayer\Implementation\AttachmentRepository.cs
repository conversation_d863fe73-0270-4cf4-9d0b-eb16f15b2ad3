using DataAccessLayer.Context;
using MessageContracts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public class AttachmentRepository : GenericRepository<MessageAttachment>, IAttachmentRepository
    {
        public AttachmentRepository(MessageApiDatabaseContext context) : base(context)
        {
        }

        public async Task<List<MessageAttachment>> GetAttachmentsByMessageIdAsync(Guid messageId)
        {
            return await _context.Set<MessageAttachment>()
                .Where(a => a.MessageId == messageId)
                .OrderBy(a => a.UploadedDateTime)
                .ToListAsync();
        }

        public async Task<MessageAttachment?> GetAttachmentByFileNameAsync(string fileName)
        {
            return await _context.Set<MessageAttachment>()
                .FirstOrDefaultAsync(a => a.FileName == fileName || a.OriginalFileName == fileName);
        }

        public async Task<List<MessageAttachment>> GetAttachmentsByContentTypeAsync(string contentType)
        {
            return await _context.Set<MessageAttachment>()
                .Where(a => a.ContentType.Equals(contentType, StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(a => a.UploadedDateTime)
                .ToListAsync();
        }

        public async Task<long> GetTotalAttachmentSizeByMessageIdAsync(Guid messageId)
        {
            return await _context.Set<MessageAttachment>()
                .Where(a => a.MessageId == messageId)
                .SumAsync(a => a.FileSizeBytes);
        }
    }
}
