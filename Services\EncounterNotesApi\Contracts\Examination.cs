﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class Examination : IContract
    {
        public Guid ExaminationId { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdateDate { get; set; }
        public string? GeneralDescription { get; set; }
        public string? HEENT { get; set; }
        public string? Lungs { get; set; }
        public string? Abdomen { get; set; }
        public string? PeripheralPulses { get; set; }
        public string? Skin { get; set; }
        public string? Others { get; set; }
    }

}
