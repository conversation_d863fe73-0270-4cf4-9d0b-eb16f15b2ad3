﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class DiagnosticImagingQueryHandler : IDiagnosticImagingQueryHandler<DiagnosticImage>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        public DiagnosticImagingQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        ///  Get All Diagnostic Imaging by Id 
        /// </summary>
        public async Task<List<DiagnosticImage>> GetDiagnosticImagingById(Guid id)
        {
            var result = new List<DiagnosticImage>();
            try
            {
                result = await _unitOfWork.DiagnosticImagingRepository.GetDiagnosticImagingById(id);
                return result;
            }
            catch (Exception ex)
            {
                return result;
            }
        }

        /// <summary>
        ///  Get Active Diagnostic Imaging by Id 
        /// </summary>
        public async Task<IEnumerable<DiagnosticImage>> GetDiagnosticImagingByIdAndIsActive(Guid id)
        {
            var diagnosticImagings = await _unitOfWork.DiagnosticImagingRepository.GetDiagnosticImagingById(id);

            return diagnosticImagings.Where(diagnosticImaging => diagnosticImaging.PatientId == id && diagnosticImaging.IsActive == true);
        }
    }
}
