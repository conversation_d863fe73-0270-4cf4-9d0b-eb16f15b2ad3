﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface ITemplatesRepository : IGenericRepository<Templates>
    {
        Task<List<Templates>> GetTemplatesByPCPId(Guid PCPId);
        Task<List<Templates>> GetTemplatesByPCPIdAndVisitType(Guid PCPId, String VisitType);
    }
}