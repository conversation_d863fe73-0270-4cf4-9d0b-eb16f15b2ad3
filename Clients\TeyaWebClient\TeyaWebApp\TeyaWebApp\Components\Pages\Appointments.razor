﻿@page "/Appointments"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaUIViewModels.ViewModels
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "AppointmentsAccessPolicy")]
@using Microsoft.Extensions.Localization
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Inputs
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using System.Collections.Generic
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns;
@using Syncfusion.Blazor.Grids;
@using Microsoft.AspNetCore.Components;
@using MudBlazor
@using TeyaWebApp.TeyaAIScribeResources
@layout Admin
@inject ILogger<SecuritySettings> Logger
@inject IJSRuntime JSRuntime
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IAppointmentService AppointmentService
@inject IFacilityService FacilityService
@inject IVisitTypeService VisitTypeService
@inject IVisitStatusService VisitStatusService
@inject IMemberService MemberService

<head>
    <link href="css/Appointments.razor.css" rel="stylesheet" />
</head>

<h3 style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; margin-bottom: 2px; padding-top : 8px; padding-bottom: 8px;"><strong>@Localizer["Appointments"]</strong></h3>
<div class="d-flex gap-4">
    <div style="width: 300px;">
        <SfCalendar TValue="DateTime" Value="@selectedDate" ValueChanged="@OnDateChanged"></SfCalendar>
        <p style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; margin-bottom: 2px; padding-top : 8px">
            @Localizer["Facilities"]
        </p>
        <SfMultiSelect TValue="string[]" TItem="Facility"
                       CssClass="form-control-dropdown-compact"
                       Placeholder="@Localizer["Select Facility"]"
                       DataSource="@FacilityList"
                       Value="@selectedFacilities" ValueChanged="OnFacilitiesFilter"
                       Mode="VisualMode.Box">
            <MultiSelectFieldSettings Value="FacilityName" Text="FacilityName"></MultiSelectFieldSettings>
        </SfMultiSelect>
        <p style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; margin-bottom: 2px; padding-top : 8px">
            @Localizer["Providers"]
        </p>
        <SfMultiSelect TValue="string[]" TItem="Member"
                       CssClass="form-control-dropdown-compact"
                       Placeholder="@Localizer["Select Provider"]"
                       DataSource="@ProviderListToFilter"
                       Value="@selectedProviders" ValueChanged="OnProvidersFilter"
                       Mode="VisualMode.Box">
            <MultiSelectFieldSettings Value="UserName" Text="UserName"></MultiSelectFieldSettings>
        </SfMultiSelect>
    </div>

    <div style="flex-grow: 1">
        <SfButton CssClass="e-info" OnClick="OpenAddTaskDialog" style="margin-bottom: 10px;">@Localizer["New Appointment"]</SfButton>
        <SfSchedule TValue="Appointment" @bind-SelectedDate="@selectedDate" CurrentView="View.Day" AllowDragAndDrop="false" @ref="ScheduleRef">
            <ScheduleViews>
                <ScheduleView Option="View.Day"></ScheduleView>
            </ScheduleViews>
            <ScheduleTemplates>
                <ResourceHeaderTemplate>
                    @{
                        var provider = (context as TemplateContext).ResourceData as ProviderResource;
                    }
                    <div style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px;">
                        @provider?.UserName
                    </div>
                </ResourceHeaderTemplate>
            </ScheduleTemplates>
            <ScheduleGroup Resources="@resourceNames"></ScheduleGroup>
            <ScheduleResources>
                <ScheduleResource TValue="Guid" TItem="ProviderResource" Field="ProviderId" Title="Provider" Name="Provider"
                                  DataSource="@CachedProviderResources"
                                  TextField="UserName" IdField="Id" ColorField="Color">
                </ScheduleResource>
            </ScheduleResources>
            <ScheduleEvents TValue="Appointment"
                             OnEventClick="OnEventSelect"></ScheduleEvents>
            <ScheduleEventSettings DataSource="@appointments" AllowAdding="false">
                <ScheduleField Id="Id">
                    <FieldSubject Name="PatientName"></FieldSubject>
                    <FieldStartTime Name="StartTime"></FieldStartTime>
                    <FieldEndTime Name="EndTime"></FieldEndTime>
                    <FieldDescription Name="Notes"></FieldDescription>
                </ScheduleField>
            </ScheduleEventSettings>
        </SfSchedule>
    </div>
</div>
<MudDialog @ref="_AddAppointmentdialog" Style="width: 85vw; max-width: 800px;">
    <TitleContent>
        <MudText Style="font-size: 1.5rem; font-weight: bold; color: #333;">
            @DialogTitle
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CloseAddTaskDialog" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <!-- Search Criteria, Search Term, and Button in the same line -->
        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px; width: 70%;">
            <SfDropDownList TValue="string" TItem="string"
                            Placeholder="Select Criteria"
                            DataSource="@searchCriteriaOptions"
                            @bind-Value="selectedCriteria"
                            Style="width: 180px;">
            </SfDropDownList>
            <SfTextBox id="searchUser"
                       Value="@selectedUserName"
                       ValueChanged="@(args => { selectedUserName = args.ToString(); searchTerm = args.ToString(); })"
                       Placeholder="@Localizer["Enter Search Term"]"
                       Style="flex: 1; min-width: 200px; padding: 5px; border: 1px solid #ccc; border-radius: 5px;">
            </SfTextBox>


            <SfButton OnClick="@SearchUsers"
                      Style="background-color: #007bff; color: white; padding: 5px 15px; border-radius: 5px;">
                @Localizer["Search"]
            </SfButton>
        </div>

        @if (showNoResultsMessage)
        {
            <div style="margin-top: 5px;">@Localizer["No User Found"]</div>
        }
        else if (filteredUsers != null && filteredUsers.Count > 0)
        {
            <SfGrid TValue="Member" DataSource="@filteredUsers" AllowPaging="true" Style="margin-top: 10px;">
                <GridPageSettings PageSize="3"></GridPageSettings>
                <GridEvents RowSelected="RowSelectHandler" TValue="Member"></GridEvents>
                <GridColumns>
                    <GridColumn Field="@nameof(Member.SSN)" HeaderText="@Localizer["SSN"]"></GridColumn>
                    <GridColumn Field="@nameof(Member.UserName)" HeaderText="@Localizer["Name"]"></GridColumn>
                    <GridColumn Field="@nameof(Member.PhoneNumber)" HeaderText="@Localizer["Phone Number"]"></GridColumn>
                    <GridColumn Field="@nameof(Member.DateOfBirth)" HeaderText="@Localizer["Date Of Birth"]" Format="d"></GridColumn>
                </GridColumns>
            </SfGrid>
        }

        <!-- Select Facility and Select Provider in the same line -->
        <div style="display: flex; gap: 10px; margin-top: 10px;">
            <SfDropDownList TValue="string" TItem="Facility"
                            CssClass="form-control-dropdown-compact"
                            Placeholder="@Localizer["Select Facility"]"
                            DataSource="@FacilityList"
                            Value="@selectedFacility"
                            ValueChanged="OnFacilityChanged">
                <DropDownListFieldSettings Value="FacilityName"></DropDownListFieldSettings>
            </SfDropDownList>
            <SfDropDownList TValue="string" TItem="Member"
                            CssClass="form-control-dropdown-compact"
                            Placeholder="@Localizer["Select Provider"]"
                            DataSource="@Provider_List"
                            Value="@selectedProvider"
                            ValueChanged="OnProviderChanged">
                <DropDownListFieldSettings Value="UserName"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>

        <!-- Date and Time Pickers in the same line -->
        <div style="display: flex; gap: 10px; margin-top: 10px;">
            <SfDatePicker TValue="DateTime"
                          @bind-Value="@Date_Value"
                          CssClass="form-control-date-compact"
                          Placeholder="@Localizer["Select Appointment Date"]">
            </SfDatePicker>
            <SfTimePicker TValue="DateTime?"
                          @bind-Value="@Start_Time"
                          Format="HH:mm"
                          ShowClearButton="false"
                          CssClass="form-control-time-compact"
                          Step="30"
                          Placeholder="@Localizer["Select Start Time"]">
            </SfTimePicker>
            <SfTimePicker TValue="DateTime?"
                          @bind-Value="@End_Time"
                          Format="HH:mm"
                          ShowClearButton="false"
                          CssClass="form-control-time-compact"
                          Step="30"
                          Placeholder="@Localizer["Select End Time"]">
            </SfTimePicker>
        </div>

        <!-- Visit Type and Visit Status in the same line -->
        <div style="display: flex; gap: 10px; margin-top: 10px;">
            <SfDropDownList TValue="string" TItem="string"
                            CssClass="form-control-dropdown-compact"
                            Placeholder="@Localizer["Select Visit Type"]"
                            DataSource="@visitTypeOptions"
                            @bind-Value="selectedVisitType" >
            </SfDropDownList>
            <SfDropDownList TValue="string" TItem="string"
                            CssClass="form-control-dropdown-compact"
                            Placeholder="@Localizer["Select Visit Status"]"
                            DataSource="@visitStatusOptions"
                            @bind-Value="selectedVisitStatus">
            </SfDropDownList>
        </div>

        <!-- Reason below Visit Type & Status -->
        <textarea id="reason" @bind="@selectedReason"
                  placeholder="@Localizer["Enter Reason"]"
                  style="width: 100%; margin-top: 10px; padding: 8px; border: 1px solid #ccc; border-radius: 5px;"></textarea>

        <!-- Notes below Reason -->
        <textarea id="notes" @bind="@selectedNotes"
                  placeholder="@Localizer["Enter Notes"]"
                  style="width: 100%; margin-top: 10px; padding: 8px; border: 1px solid #ccc; border-radius: 5px;"></textarea>

        <!-- Submit Button at the bottom right -->
        <!-- Submit and Delete Buttons at the bottom -->
        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
            <div>
                @if (isEditMode)
                {
                    <SfButton OnClick="@DeleteAppointment"
                              Style="background-color: #dc3545; color: white; padding: 8px 15px; border-radius: 5px;">
                        @Localizer["Delete"]
                    </SfButton>
                }
            </div>
            <div>
                <SfButton OnClick="@AddAppointment"
                          Style="background-color: #28a745; color: white; padding: 8px 15px; border-radius: 5px;">
                    @Localizer["Submit"]
                </SfButton>
            </div>
        </div>

    </DialogContent>
</MudDialog>


<style>
    .e-schedule .e-header-cells {
        display: none;
    }

    ::deep .e-schedule .e-content-wrap {
        overflow-x: auto !important;
    }
</style>
