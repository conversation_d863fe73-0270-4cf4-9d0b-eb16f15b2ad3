﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Text;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class AllergyCommandHandler : IAllergyCommandHandler<Allergy>
    {
        private readonly IUnitOfWork _unitOfWork;
        public AllergyCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddAllergy(List<Allergy> allergies)
        {
            await _unitOfWork.AllergyRepository.AddAsync(allergies);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateAllergy(Allergy allergy)
        {
            await _unitOfWork.AllergyRepository.UpdateAsync(allergy);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAllergyList(List<Allergy> newAllergies, List<Allergy> updatedAllergies, List<Allergy> deletedAllergies)
        {
            try
            {
                foreach (var allergy in newAllergies)
                {
                    _unitOfWork.AllergyRepository.Add(allergy);
                }

                foreach (var allergy in updatedAllergies)
                {
                    _unitOfWork.AllergyRepository.Update(allergy);
                }

                foreach (var allergy in deletedAllergies)
                {
                    allergy.isActive = false;
                    _unitOfWork.AllergyRepository.Update(allergy);
                }

                await _unitOfWork.SaveAsync();
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while updating the allergy list.", ex);
            }
        }
        public async Task DeleteAllergyById(Guid id)
        {
            await _unitOfWork.AllergyRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteAllergyByEntity(Allergy allergy)
        {
            await _unitOfWork.AllergyRepository.DeleteByEntityAsync(allergy);
            await _unitOfWork.SaveAsync();
        }
    }
}