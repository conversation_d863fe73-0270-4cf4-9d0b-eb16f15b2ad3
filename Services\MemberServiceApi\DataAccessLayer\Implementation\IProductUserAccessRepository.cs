﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IProductUserAccessRepository
    {
        Task<IEnumerable<Member>> GetMembersForProduct(Guid productId);

        Task<ProductUserAccess> GetAccessRecordAsync(Guid productId, Guid memberId);
    }
}
