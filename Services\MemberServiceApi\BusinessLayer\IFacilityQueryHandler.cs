﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IFacilityQueryHandler<T> where T : Facility
    {
        Task<T> GetFacilityByIdAsync(Guid id);
        Task<List<T>> GetFacilitiesByNameAsync(string name);
        Task<List<T>> GetAllFacilitiesAsync();
        Task<List<T>> GetFacilitiesByOrgAsync(Guid id);
    }
}
