﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class GuardianCommandHandler : IGuardianCommandHandler<Guardian>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GuardianCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddGuardianAsync(List<Guardian> Guardian)
        {
            await _unitOfWork.GuardianRepository.AddAsync(Guardian);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateGuardianAsync(Guardian Guardian)
        {
            await _unitOfWork.GuardianRepository.UpdateAsync(Guardian);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteGuardianAsync(Guid id)
        {
            await _unitOfWork.GuardianRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
