﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IReviewOfSystemService
    {
        Task<List<ReviewOfSystem>> GetAllByIdAsync(Guid id);
        Task<List<ReviewOfSystem>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddReviewOfSystemAsync(List<ReviewOfSystem> reviewOfSystems);
        Task UpdateReviewOfSystemAsync(ReviewOfSystem reviewOfSystem);
        Task UpdateReviewOfSystemListAsync(List<ReviewOfSystem> reviewOfSystems);
        Task DeleteReviewOfSystemByEntityAsync(ReviewOfSystem reviewOfSystem);
    }
}
