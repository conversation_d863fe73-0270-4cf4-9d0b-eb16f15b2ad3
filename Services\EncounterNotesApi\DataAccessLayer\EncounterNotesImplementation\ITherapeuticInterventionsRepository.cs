﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface ITherapeuticInterventionsRepository : IGenericRepository<TherapeuticInterventionsData>
    {
        Task<IEnumerable<TherapeuticInterventionsData>> GetAllTherapeuticInterventionsAsync();
    }
}
