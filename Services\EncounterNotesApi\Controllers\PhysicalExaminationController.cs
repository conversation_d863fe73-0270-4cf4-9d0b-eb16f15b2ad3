﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class PhysicalExaminationController : ControllerBase
    {
        private readonly IPhysicalExaminationCommandHandler<PhysicalExamination> _physicalDataHandler;
        private readonly IPhysicalExaminationQueryHandler<PhysicalExamination> _physicalQueryHandler;
        private readonly ILogger<PhysicalExaminationController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public PhysicalExaminationController(
            IPhysicalExaminationCommandHandler<PhysicalExamination> physicalDataHandler,
            IPhysicalExaminationQueryHandler<PhysicalExamination> physicalQueryHandler,
            ILogger<PhysicalExaminationController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _physicalDataHandler = physicalDataHandler;
            _physicalQueryHandler = physicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get All Details By Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<PhysicalExamination>>> GetAllById(Guid id)
        {
            ActionResult result;
            try
            {
                var data = await _physicalQueryHandler.GetAllExaminationsById(id);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}/IsActive")]
        public async Task<ActionResult<IEnumerable<PhysicalExamination>>> GetAllByIdAndIsActive(Guid id)
        {
            ActionResult result;
            try
            {
                var surgery = await _physicalQueryHandler.GetExaminationByIdAndIsActive(id);
                result = surgery != null ? Ok(surgery) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physicals"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddExamination")]
        public async Task<IActionResult> Registration([FromBody] List<PhysicalExamination> physicals)
        {
            if (physicals == null || physicals.Count == 0)
            {
                return Ok(_localizer["NoLicense"]);
            }

            try
            {
                await _physicalDataHandler.AddExamination(physicals);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physical"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] PhysicalExamination physical)
        {
            IActionResult result;
            {
                if (physical == null)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _physicalDataHandler.DeleteExaminationByEntity(physical);
                        result = Ok(_localizer["DeleteSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["DeleteLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                    }
                }
                return result;
            }
        }

       /// <summary>
       /// 
       /// </summary>
       /// <param name="id"></param>
       /// <param name="Phyexamination"></param>
       /// <returns></returns>
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateExaminationById(Guid id, [FromBody] PhysicalExamination Phyexamination)
        {
            IActionResult result;
            {
                if (Phyexamination == null || Phyexamination.PatientId != id)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _physicalDataHandler.UpdateExamination(Phyexamination);
                        result = Ok(_localizer["UpdateSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["UpdateLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physicalExamination"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateExaminationList")]
        public async Task<IActionResult> UpdateExaminationList([FromBody] List<PhysicalExamination> physicalExamination)
        {
            IActionResult result;
            try
            {
                await _physicalDataHandler.UpdateExaminationList(physicalExamination);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }
    }
}