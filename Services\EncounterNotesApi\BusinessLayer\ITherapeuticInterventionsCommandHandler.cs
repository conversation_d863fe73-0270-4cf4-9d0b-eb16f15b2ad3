﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITherapeuticInterventionsCommandHandler<TText>
    {
        Task DeleteTherapeuticInterventionsByEntity(TherapeuticInterventionsData _TherapeuticInterventions);
        Task DeleteTherapeuticInterventionsById(Guid id);
        Task AddTherapeuticInterventions(List<TText> texts);
        Task UpdateTherapeuticInterventions(TherapeuticInterventionsData _TherapeuticInterventions);
        Task UpdateTherapeuticInterventionsList(List<TherapeuticInterventionsData> _TherapeuticInterventions);
    }
}
