﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class EncounterDataAccessLayer {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EncounterDataAccessLayer() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource.EncounterDataAcces" +
                            "sLayer", typeof(EncounterDataAccessLayer).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allergy.
        /// </summary>
        public static string Allergy {
            get {
                return ResourceManager.GetString("Allergy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessments.
        /// </summary>
        public static string Assessments {
            get {
                return ResourceManager.GetString("Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error in the database.
        /// </summary>
        public static string DatabaseError {
            get {
                return ResourceManager.GetString("DatabaseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DateTime.
        /// </summary>
        public static string DateTime {
            get {
                return ResourceManager.GetString("DateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EncounterNotesService.
        /// </summary>
        public static string EncounterNotesService {
            get {
                return ResourceManager.GetString("EncounterNotesService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to store data in cache for key: {0}.
        /// </summary>
        public static string FailedToStoreDataInCache {
            get {
                return ResourceManager.GetString("FailedToStoreDataInCache", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error while fetching Allergies data.
        /// </summary>
        public static string GetLogErrorAllergy {
            get {
                return ResourceManager.GetString("GetLogErrorAllergy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Record.
        /// </summary>
        public static string InvalidRecord {
            get {
                return ResourceManager.GetString("InvalidRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No License.
        /// </summary>
        public static string NoLicense {
            get {
                return ResourceManager.GetString("NoLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PatientCurrentMedication.
        /// </summary>
        public static string PatientCurrentMedication {
            get {
                return ResourceManager.GetString("PatientCurrentMedication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PatientSocialHistory.
        /// </summary>
        public static string PatientSocialHistory {
            get {
                return ResourceManager.GetString("PatientSocialHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error while registerinig allergies data.
        /// </summary>
        public static string PostLogErrorAllergy {
            get {
                return ResourceManager.GetString("PostLogErrorAllergy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Records.
        /// </summary>
        public static string Records {
            get {
                return ResourceManager.GetString("Records", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Redis Cache Error.
        /// </summary>
        public static string RedisCacheError_ {
            get {
                return ResourceManager.GetString("RedisCacheError ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Redis is not connected. Retrying....
        /// </summary>
        public static string RedisNotConnectedRetrying {
            get {
                return ResourceManager.GetString("RedisNotConnectedRetrying", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Redis Timeout Error.
        /// </summary>
        public static string RedisTimeoutError {
            get {
                return ResourceManager.GetString("RedisTimeoutError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registered successfully.
        /// </summary>
        public static string SuccessfulRegistration {
            get {
                return ResourceManager.GetString("SuccessfulRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to WordTimings.
        /// </summary>
        public static string WordTimings {
            get {
                return ResourceManager.GetString("WordTimings", resourceCulture);
            }
        }
    }
}
