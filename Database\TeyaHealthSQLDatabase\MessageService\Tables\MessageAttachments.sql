﻿CREATE TABLE [MessageService].[MessageAttachments] (
    [AttachmentId]      UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [MessageId]         UNIQUEIDENTIFIER NOT NULL,
    [FileName]          NVARCHAR (255)   NOT NULL,
    [OriginalFileName]  NVARCHAR (255)   NOT NULL,
    [ContentType]       NVARCHAR (100)   NOT NULL,
    [FileSizeBytes]     BIGINT           NOT NULL,
    [FilePath]          NVARCHAR (500)   NOT NULL,
    [FileContentBase64] NVARCHAR (MAX)   NULL,
    [UploadedDateTime]  DATETIME2 (7)    DEFAULT (getutcdate()) NOT NULL,
    [FileHash]          NVARCHAR (100)   NULL,
    [BlobStorageUrl]    NVARCHAR (500)   NULL,
    [BlobContainerName] NVARCHAR (100)   NULL,
    [BlobFileName]      NVARCHAR (255)   NULL,
    [IsScanned]         BIT              DEFAULT ((0)) NOT NULL,
    [IsSafe]            BIT              DEFAULT ((1)) NOT NULL,
    [ScanResult]        NVARCHAR (255)   NULL,
    PRIMARY KEY CLUSTERED ([AttachmentId] ASC),
    CONSTRAINT [FK_MessageAttachments_Messages] FOREIGN KEY ([MessageId]) REFERENCES [MessageService].[Messages] ([MessageId]) ON DELETE CASCADE
);


GO
CREATE NONCLUSTERED INDEX [IX_MessageAttachments_UploadedDateTime]
    ON [MessageService].[MessageAttachments]([UploadedDateTime] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_MessageAttachments_ContentType]
    ON [MessageService].[MessageAttachments]([ContentType] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MessageAttachments_FileName]
    ON [MessageService].[MessageAttachments]([FileName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MessageAttachments_MessageId]
    ON [MessageService].[MessageAttachments]([MessageId] ASC);

