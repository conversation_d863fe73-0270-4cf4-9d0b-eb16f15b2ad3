﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;



namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public interface IGynHistoryCommandHandler<T> where T : class
    {
        Task AddAsync(IEnumerable<T> gynhistory);
        Task UpdateAsync(T complaint);
        Task UpdateGynHistoryListAsync(IEnumerable<GynHistory> obhistory);
        Task DeleteByIdAsync(Guid ObId);
        Task DeleteByEntity(T gynhistory);
    }
}
