$body = @{
    senderName = "<PERSON>"
    senderEmailId = "<EMAIL>"
    receiverName = "<PERSON>"
    receiverEmailId = "<EMAIL>"
    messageContent = "Test message for user management debugging"
} | ConvertTo-Json

Write-Host "Sending message..."
Write-Host "Body: $body"

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/Message/send" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Success! Response:"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error occurred:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
