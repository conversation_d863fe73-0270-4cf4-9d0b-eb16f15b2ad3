﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class LicenseRepository : GenericRepository<ProductLicense>, ILicenseRepository
    {
        private readonly AccountDatabaseContext _context;

        public LicenseRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }
    }
}