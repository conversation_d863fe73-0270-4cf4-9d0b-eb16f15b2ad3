﻿using Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Data;

namespace DataAccessLayer.Context
{
    public class AccountDatabaseContext : DbContext
    {
        private readonly IStringLocalizer<AccountDatabaseContext> _localizer;
        private readonly ILogger<AccountDatabaseContext> _logger;
        public AccountDatabaseContext(DbContextOptions<AccountDatabaseContext> options,
                                      IStringLocalizer<AccountDatabaseContext> localizer,
                                      ILogger<AccountDatabaseContext> logger)
            : base(options)
        {
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger;
        }
        public DbSet<Member> Members { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Product> Licenses { get; set; }
        public DbSet<ProductUserAccess> ProductUserAccess { get; set; }
        public DbSet<Role> Role { get; set; }
        public DbSet<Address> Addresses { get; set; }
        public DbSet<Organization> Organization { get; set; }
        public DbSet<Insurance> Insurance { get; set; }
        public DbSet<UpToDate> UpToDate { get; set; }
        public DbSet<UserTheme> UserThemes { get; set; }
        public DbSet<Facility> Facility { get; set; }
        public DbSet<VisitType> VisitTypes { get; set; }
        public DbSet<PageRoleMapping> PageRoleMappings { get; set; }
        public DbSet<PreDefinedPageRoleMapping> PreDefinedPageRoleMappings { get; set; }
        public DbSet<PredefinedVisitType> PredefinedVisitType { get; set; }
        public DbSet<Rolesdata> Roleslist { get; set; }
        public DbSet<PagePath> PagePaths { get; set; }
        public DbSet<Country> Countries { get; set; }

        public DbSet<Guardian> Guardians { get; set; }
        public DbSet<Employer> Employers { get; set; }

        public DbSet<UserLicense> UserLicenses { get; set; }
        public DbSet<PlanType> PlanTypes { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            try
            {
                modelBuilder.Entity<Member>().ToTable(_localizer["Users"], _localizer["AccountService"]);
                modelBuilder.Entity<Member>().Property(m => m.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);
                modelBuilder.Entity<Member>().HasIndex(m => m.Email).IsUnique();

                modelBuilder.Entity<Product>().ToTable(_localizer["Products"], _localizer["AccountService"]);
                modelBuilder.Entity<Product>().Property(p => p.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<ProductLicense>().ToTable(_localizer["Licenses"], _localizer["AccountService"]);
                modelBuilder.Entity<ProductLicense>().Property(p => p.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<ProductUserAccess>().ToTable(_localizer["ProductUserAccess"], _localizer["AccountService"]);
                modelBuilder.Entity<ProductUserAccess>().HasKey(pu => new { pu.ProductId, pu.MemberId });

                modelBuilder.Entity<VisitType>().ToTable(_localizer["VisitType"], _localizer["AccountService"])
                .Property(visitType => visitType.ID).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<VisitStatus>().ToTable(_localizer["VisitStatus"], _localizer["AccountService"])
                .Property(visitStatus => visitStatus.ID).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<ProductUserAccess>()
                    .HasOne(pu => pu.Product)
                    .WithMany(p => p.ProductUserAccess)
                    .HasForeignKey(pu => pu.ProductId);

                modelBuilder.Entity<ProductUserAccess>()
                    .HasOne(pu => pu.Member)
                    .WithMany(m => m.ProductUserAccess)
                    .HasForeignKey(pu => pu.MemberId);

                modelBuilder.Entity<Role>()
                    .ToTable(_localizer["Role"], _localizer["AccountService"]);
                modelBuilder.Entity<Role>().Property(r => r.RoleId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<Organization>()
                    .ToTable(_localizer["Organization"], _localizer["AccountService"]);
                modelBuilder.Entity<Organization>().Property(o => o.OrganizationId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<Facility>()
                    .ToTable(_localizer["Facility"], _localizer["AccountService"]);
                modelBuilder.Entity<Facility>().Property(f => f.FacilityId).HasDefaultValueSql("NEWID()");
                modelBuilder.Entity<Facility>()
                .HasIndex(f => new { f.FacilityName, f.OrganizationId })
                    .IsUnique();

                modelBuilder.Entity<Address>()
                   .ToTable(_localizer["Addresses"], _localizer["AccountService"]);
                modelBuilder.Entity<Address>().Property(a => a.AddressId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<Insurance>()
                    .ToTable(_localizer["Insurance"], _localizer["AccountService"]);
                modelBuilder.Entity<Insurance>().Property(i => i.InsuranceId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<Guardian>()
                    .ToTable(_localizer["GuardianDetails"], _localizer["AccountService"]);
                modelBuilder.Entity<Guardian>().Property(a => a.GuardianId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<Employer>()
                    .ToTable(_localizer["EmployerDetails"], _localizer["AccountService"]);
                modelBuilder.Entity<Employer>().Property(a => a.EmployerId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<UpToDate>()
                    .ToTable(_localizer["UpToDate"], _localizer["AccountService"]);
                modelBuilder.Entity<UpToDate>().Property(u => u.Id).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<UserTheme>()
                    .ToTable(_localizer["UserThemes"], _localizer["AccountService"]);
                modelBuilder.Entity<UserTheme>().HasKey(ut => ut.UserId);
                modelBuilder.Entity<UserTheme>().Property(ut => ut.UserId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<PageRoleMapping>()
                    .ToTable(_localizer["PageRoleMappings"], _localizer["AccountService"]);
                modelBuilder.Entity<PageRoleMapping>().Property(prm => prm.Id).HasDefaultValueSql("NEWID()");
                
                modelBuilder.Entity<PreDefinedPageRoleMapping>()
                    .ToTable(_localizer["PreDefinedPageRoleMappings"], _localizer["AccountService"]);
                modelBuilder.Entity<PreDefinedPageRoleMapping>().Property(prm => prm.Id).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<PredefinedVisitType>()
                   .ToTable(_localizer["PredefinedVisitType"], _localizer["AccountService"]);
                modelBuilder.Entity<PredefinedVisitType>().Property(prm => prm.ID).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<Rolesdata>()
                   .ToTable(_localizer["Roleslist"], _localizer["AccountService"]);
                modelBuilder.Entity<Rolesdata>().Property(prm => prm.ID).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<PagePath>()
            .ToTable(_localizer["PagePath"], _localizer["AccountService"]);
                modelBuilder.Entity<PagePath>().HasKey(pp => pp.PageId);
                modelBuilder.Entity<PagePath>().Property(pp => pp.PageId).HasDefaultValueSql("NEWID()");
                modelBuilder.Entity<PagePath>().Property(pp => pp.CreatedDate).HasDefaultValueSql("GETDATE()");
                modelBuilder.Entity<PagePath>().Property(pp => pp.IsActive).HasDefaultValue(true);

                modelBuilder.Entity<UpToDate>()
                .HasOne(u => u.Organization)
                .WithMany()
                .HasForeignKey(u => u.OrganizationId)
                .OnDelete(DeleteBehavior.Cascade);

                modelBuilder.Entity<Country>()
                    .ToTable(_localizer["Country"], _localizer["AccountService"]);
                modelBuilder.Entity<Country>().HasKey(c => c.CountryId);
                modelBuilder.Entity<Country>().Property(c => c.CountryId).HasDefaultValueSql("NEWID()");

                modelBuilder.Entity<UserLicense>()
            .ToTable(_localizer["License"], _localizer["AccountService"]);
                modelBuilder.Entity<UserLicense>().HasKey(ul => ul.Id);
                modelBuilder.Entity<UserLicense>().Property(ul => ul.Id).HasDefaultValueSql("NEWID()");
                modelBuilder.Entity<UserLicense>().Property(ul => ul.CreatedDate).HasDefaultValueSql("GETDATE()");
                modelBuilder.Entity<UserLicense>().Property(ul => ul.Status).HasDefaultValue(true);

                modelBuilder.Entity<PlanType>()
            .ToTable(_localizer["PlanTypes"], _localizer["AccountService"]);
                modelBuilder.Entity<PlanType>().HasKey(ul => ul.Id);
                modelBuilder.Entity<PlanType>().Property(ul => ul.CreatedDate).HasDefaultValueSql("GETDATE()");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DatabaseError"]);
            }
        }
    }
}
