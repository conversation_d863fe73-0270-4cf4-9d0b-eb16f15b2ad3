﻿using AppointmentContracts;
using AppointmentDataAccessLayer.AppointmentImplementation;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace BusinessLayer.CommandHandler
{
    public class AppointmentCommandHandler : IAppointmentCommandHandler<Appointment>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AppointmentCommandHandler> _logger;
        private readonly IMigration _migration;
        const int zero=0,one=1,two=2;
        public AppointmentCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<AppointmentCommandHandler> logger, IMigration migration)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _migration = migration;
        }

        public async Task AddAppointment(List<Appointment> appointments,Guid shardId)
        {
            await _unitOfWork.AppointmentRepository.AddAsync(appointments,shardId);

        }

        public async Task UpdateAppointment(Appointment appointment,Guid shardId)
        {
            await _unitOfWork.AppointmentRepository.UpdateAsync(appointment,shardId);
        }

        public async Task DeleteAppointmentById(bool Subscription,Guid shardId, Guid OrgID)
        {
            await _unitOfWork.AppointmentRepository.DeleteByIdAsync(Subscription, shardId, OrgID);
        }

        public void AddShard(ShardRequest request)
        {
            List<byte> guidBytes = request.Key.ToByteArray().ToList();

            if (request.Subscription == zero)
            {
                guidBytes.Add(two);
                byte[] updatedGuidBytes = guidBytes.ToArray();
                _migration.CreateAndMigrateDataToNewShard(request.ServerName, request.DatabaseName, updatedGuidBytes);
            }
            else
            {
                guidBytes.Add(one);
                byte[] updatedGuidBytes = guidBytes.ToArray();
                _migration.CreateShardForSingleTenant(request.ServerName, request.DatabaseName, updatedGuidBytes);
            }
        }
    }
}
