﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.Implementation;
using Contracts;
using MemberServiceDataAccessLayer;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IUnitOfWork : IDisposable
    {
        IMemberRepository MemberRepository { get; }
        IProductRepository ProductRepository { get; }
        ILicenseRepository ProductLicenseRepository { get; }
        IProductUserAccessRepository ProductUserAccessRepository { get; }
        IOrganizationRepository OrganizationRepository { get; }
        IRolesRepository RolesRepository { get; }
        IFacilityRepository FacilityRepository { get; }
        IAddressesRepository AddressesRepository { get; }
        IInsuranceRepository InsuranceRepository { get; }
        IUpToDateRepository UpToDateRepository { get; }
        IUserThemeRepository UserThemeRepository { get; }
        IPageRoleMappingRepository PageRoleMappingRepository { get; }
        IPreDefinedPageRoleMappingRepository PreDefinedPageRoleMappingRepository { get; }
        IPredefinedVisitTypeRepository PredefinedVisitTypeRepository { get; }
        IRoleslistRepository RoleslistRepository { get; }
        IVisitTypeRepository VisitTypeRepository { get; }
        IVisitStatusRepository VisitStatusRepository { get; }
        IPagePathRepository PagePathRepository { get; }
        ICountryRepository CountryRepository { get; }

        IGuardianRepository GuardianRepository { get; }
        IEmployerRepository EmployerRepository { get; }

        IUserLicenseRepository UserLicenseRepository { get; }
        IPlanTypeRepository PlanTypeRepository { get; }

        Task<int> SaveAsync();
    }
}
