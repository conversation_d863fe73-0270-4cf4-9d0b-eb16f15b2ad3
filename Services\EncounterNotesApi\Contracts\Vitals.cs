﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class Vitals : IContract
    {
        public Guid VitalId { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? Temperature { get; set; }
        public string? BP { get; set; }
        public string? Weight { get; set; }
        public string? Height { get; set; }
        public string? Pulse { get; set; }
        public bool isActive { get; set; }

    }
}
