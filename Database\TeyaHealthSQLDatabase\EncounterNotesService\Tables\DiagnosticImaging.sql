﻿CREATE TABLE [EncounterNotesService].[DiagnosticImaging] (
    [RecordID]       UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [OrganizationID] UNIQUEIDENTIFIER NULL,
    [PatientId]      UNIQUEIDENTIFIER NOT NULL,
    [DiCompany]      NVARCHAR (255)   NOT NULL,
    [Type]           NVARCHAR (255)   NOT NULL,
    [Lookup]         NVARCHAR (255)   NOT NULL,
    [OrderName]      NVARCHAR (255)   NOT NULL,
    [StartsWith]     NVARCHAR (255)   NOT NULL,
    [IsActive]       BIT              DEFAULT ((1)) NOT NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER NULL,
    [CreatedDate]    DATETIME         DEFAULT (getdate()) NULL,
    [UpdatedDate]    DATETIME         NULL,
    [ccResults]      NVARCHAR (MAX)   NULL,
    PRIMARY KEY CLUSTERED ([RecordID] ASC)
);

