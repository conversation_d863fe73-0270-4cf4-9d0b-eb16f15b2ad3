﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class ProcedureCommandHandler : IProcedureCommandHandler<Procedure>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProcedureCommandHandler> _logger;

        public ProcedureCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<ProcedureCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }


        public async Task AddProcedure(IEnumerable<Procedure> procedure)
        {
            await _unitOfWork.ProcedureRepository.AddAsync(procedure);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateProcedure(Procedure procedure)
        {
            await _unitOfWork.ProcedureRepository.UpdateAsync(procedure);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateProcedureListAsync(List<Procedure> procedures)
        {            
            
            await _unitOfWork.ProcedureRepository.UpdateRangeAsync(procedures);            
            await _unitOfWork.SaveAsync();
        }


    }
}

