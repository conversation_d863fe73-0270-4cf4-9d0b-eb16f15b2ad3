﻿CREATE TABLE [AccountService].[PageRoleMappings] (
    [Id]             UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [PagePath]       NVARCHAR (100)   NOT NULL,
    [RoleId]         UNIQUEIDENTIFIER NOT NULL,
    [RoleName]       NVARCHAR (100)   NOT NULL,
    [OrganizationId] UNIQUEIDENTIFIER NOT NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER NULL,
    [CreatedDate]    DATETIME         DEFAULT (getdate()) NOT NULL,
    [UpdatedDate]    DATETIME         NULL,
    [IsActive]       BIT              DEFAULT ((1)) NOT NULL,
    [HasAccess]      BIT              CONSTRAINT [DF_HasAccess] DEFAULT ((0)) NOT NULL,
    [IsModified]     BIT              DEFAULT ((0)) NOT NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_RolePageAccess_Organization] FOREIGN KEY ([OrganizationId]) REFERENCES [AccountService].[Organization] ([OrganizationId]),
    CONSTRAINT [FK_RolePageAccess_Role] FOREIGN KEY ([RoleId]) REFERENCES [AccountService].[Role] ([RoleId]) ON DELETE CASCADE
);

