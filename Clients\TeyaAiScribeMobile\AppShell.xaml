<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="TeyaAiScribeMobile.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:TeyaAiScribeMobile"
    Shell.FlyoutBehavior="Disabled"
     FlyoutWidth="250"
    Title="TeyaAiScribeMobile">

     <!--Sign In Page--> 
    <ShellContent
        Title="Sign In"
        ContentTemplate="{DataTemplate local:SignIn}"
        Route="SignIn" />

     <!--Main Page--> 
    <ShellContent
        Title="Main Page"
        ContentTemplate="{DataTemplate local:MainPage}"
        Route="MainPage" />
    <ShellContent
     Title="Appointments"
     ContentTemplate="{DataTemplate local:Appointments}"
     Route="Appointments" />
    <ShellContent
     Title="UserSettings"
     ContentTemplate="{DataTemplate local:UserSettings}"
     Route="UserSettings" />
    <ShellContent
     Title="Message"
     ContentTemplate="{DataTemplate local:Message}"
     Route="Message" />

</Shell>
