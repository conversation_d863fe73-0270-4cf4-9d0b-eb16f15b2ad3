﻿
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IHistoryOfPresentIllnessService
    {
        Task<List<HistoryOfPresentIllness>> GetHpiByPatientIdAsync(Guid patientId);
        Task<List<HistoryOfPresentIllness>> GetActiveHpiByPatientIdAsync(Guid patientId);
        Task AddHpiAsync(List<HistoryOfPresentIllness> hpiRecords);
        Task UpdateHpiListAsync(List<HistoryOfPresentIllness> hpiRecords);

    }
}
