﻿CREATE TABLE [MessageService].[Messages] (
    [MessageId]         UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [SenderName]        NVARCHAR (100)   NOT NULL,
    [SenderEmailId]     NVARCHAR (255)   NOT NULL,
    [ReceiverName]      NVARCHAR (100)   NOT NULL,
    [ReceiverEmailId]   NVARCHAR (255)   NOT NULL,
    [MessageContent]    NVARCHAR (2000)  NOT NULL,
    [SentDateTime]      DATETIME2 (7)    DEFAULT (getutcdate()) NOT NULL,
    [DeliveredDateTime] DATETIME2 (7)    NULL,
    [ReadDateTime]      DATETIME2 (7)    NULL,
    [Status]            INT              DEFAULT ((1)) NOT NULL,
    [AzureMessageId]    NVARCHAR (255)   NULL,
    [ConversationId]    NVARCHAR (255)   NULL,
    [HasAttachments]    BIT              DEFAULT ((0)) NOT NULL,
    [AttachmentCount]   INT              DEFAULT ((0)) NOT NULL,
    [Subject]           NVARCHAR (200)   DEFAULT ('') NOT NULL,
    [IsDeleted]         BIT              DEFAULT ((0)) NOT NULL,
    [CreatedAt]         DATETIME         NULL,
    [DeletedBy]         NVARCHAR (255)   NULL,
    [DeletedDateTime]   DATETIME         NULL,
    [UpdatedAt]         DATETIME         NULL,
    [IsArchived]        BIT              DEFAULT ((0)) NOT NULL,
    [ArchivedDateTime]  DATETIME2 (7)    NULL,
    [ArchivedBy]        NVARCHAR (255)   NULL,
    [IsImportant]       BIT              DEFAULT ((0)) NOT NULL,
    PRIMARY KEY CLUSTERED ([MessageId] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_Messages_IsImportant]
    ON [MessageService].[Messages]([IsImportant] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Messages_IsArchived]
    ON [MessageService].[Messages]([IsArchived] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Messages_Status]
    ON [MessageService].[Messages]([Status] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Messages_ConversationId]
    ON [MessageService].[Messages]([ConversationId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Messages_SentDateTime]
    ON [MessageService].[Messages]([SentDateTime] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_Messages_ReceiverEmailId]
    ON [MessageService].[Messages]([ReceiverEmailId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Messages_SenderEmailId]
    ON [MessageService].[Messages]([SenderEmailId] ASC);

