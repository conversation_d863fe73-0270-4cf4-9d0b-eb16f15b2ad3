﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IAllergyRepository : IGenericRepository<Allergy>
    {
        Task<IEnumerable<Allergy>> GetAllAllergyAsync();
        Task UpdateRangeAsync(List<Allergy> allergies);
        void Update(Allergy allergy);
        void Add(Allergy allergy);
        Task<Allergy> GetByIdAsync(Guid patientId, Guid medicineId);
    }
}
