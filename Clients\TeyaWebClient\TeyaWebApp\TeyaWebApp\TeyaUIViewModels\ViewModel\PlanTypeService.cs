﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DotNetEnv;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Services
{
    public class PlanTypeService : IPlanTypeService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<PlanTypeService> _localizer;
        private readonly ILogger<PlanTypeService> _logger;

        public PlanTypeService(HttpClient httpClient, IStringLocalizer<PlanTypeService> localizer, ILogger<PlanTypeService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        public async Task<IEnumerable<PlanType>> GetAllPlanTypesAsync()
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PlanType";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<PlanType>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllPlanTypes"]);
                throw;
            }
        }

        public async Task<PlanType> GetPlanTypeByIdAsync(Guid id)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/PlanType/{id}";
                var accessToken = _tokenService.AccessToken;
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<PlanType>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetPlanTypeByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPlanTypeById"], id);
                throw;
            }
        }

        public async Task<IEnumerable<PlanType>> GetPlanTypesByNameAsync(string name)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/PlanType/search?name={Uri.EscapeDataString(name)}";
                var accessToken = _tokenService.AccessToken;
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<IEnumerable<PlanType>>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetPlanTypesByNameFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPlanTypesByName"], name);
                throw;
            }
        }

        public async Task AddPlanTypeAsync(PlanType PlanType)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var bodyContent = JsonSerializer.Serialize(PlanType);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/PlanType";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["PlanTypeAddedSuccessfully"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddPlanTypeFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingPlanType"]);
                throw;
            }
        }

        public async Task UpdatePlanTypeAsync(PlanType PlanType)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PlanType/{PlanType.Id}";
                var bodyContent = JsonSerializer.Serialize(PlanType);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PlanTypeUpdatedSuccessfully"], PlanType.Id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PlanTypeUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingPlanType"]);
                throw;
            }
        }

        public async Task DeletePlanTypeByIdAsync(Guid id)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_MemberService}/api/PlanType/{id}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PlanTypeDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PlanTypeDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingPlanType"], id);
                throw;
            }
        }
    }
}
