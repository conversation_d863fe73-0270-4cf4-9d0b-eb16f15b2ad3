﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class ImmunizationCommandHandler : IImmunizationCommandHandler<Immunization>
    {
        private readonly IUnitOfWork _unitOfWork;
        public ImmunizationCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddImmunization(List<Immunization> addimmunization)
        {
            await _unitOfWork.ImmunizationRepository.AddAsync(addimmunization);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateImmunization(Immunization IMmunization)
        {
            await _unitOfWork.ImmunizationRepository.UpdateAsync(IMmunization);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteImmunizationById(Guid id)
        {
            await _unitOfWork.ImmunizationRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteImmunizationByEntity(Immunization immunization)
        {
            await _unitOfWork.ImmunizationRepository.DeleteByEntityAsync(immunization);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateImmunizationList(List<Immunization> immuNization)
        {
            await _unitOfWork.ImmunizationRepository.UpdateRangeAsync(immuNization);
            await _unitOfWork.SaveAsync();
        }
    }
}