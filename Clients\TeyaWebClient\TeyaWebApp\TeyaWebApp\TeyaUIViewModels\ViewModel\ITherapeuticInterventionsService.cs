﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ITherapeuticInterventionsService
    {
        Task<List<TherapeuticInterventionsData>> GetAllByIdAsync(Guid id);
        Task<List<TherapeuticInterventionsData>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddTherapeuticInterventionsAsync(List<TherapeuticInterventionsData> medicalHistories);
        Task UpdateTherapeuticInterventionsAsync(TherapeuticInterventionsData _TherapeuticInterventions);
        Task UpdateTherapeuticInterventionsListAsync(List<TherapeuticInterventionsData> medicalHistories);
        Task DeleteTherapeuticInterventionsByEntityAsync(TherapeuticInterventionsData _TherapeuticInterventions);
    }
}