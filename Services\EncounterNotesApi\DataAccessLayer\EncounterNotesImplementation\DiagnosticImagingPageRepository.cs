﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class DiagnosticImagingPageRepository : GenericRepository<DiagnosticImagingDTO>, IDiagnosticImagingPageRepository
    {
        private readonly RecordDatabaseContext _context;

        public DiagnosticImagingPageRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingById(Guid patientId)
        {
            return await _context.DiagnosticImagingDTO
                .Where(DiagnosticImagingRecord => DiagnosticImagingRecord.PatientId == patientId && DiagnosticImagingRecord.IsActive==true)
                .Select(DiagnosticImagingRecord => new DiagnosticImagingDTO
                {
                    RecordID = DiagnosticImagingRecord.RecordID,
                    OrganizationID = DiagnosticImagingRecord.OrganizationID,
                    PatientId = DiagnosticImagingRecord.PatientId,
                    Status = DiagnosticImagingRecord.Status,
                    Provider = DiagnosticImagingRecord.Provider,
                    Facility = DiagnosticImagingRecord.Facility,
                    AssignedTo = DiagnosticImagingRecord.AssignedTo,
                    FutureOrder = DiagnosticImagingRecord.FutureOrder,
                    HigherPriority = DiagnosticImagingRecord.HigherPriority,
                    InHouse = DiagnosticImagingRecord.InHouse,
                    Procedgure = DiagnosticImagingRecord.Procedgure,
                    OrderDate = DiagnosticImagingRecord.OrderDate,
                    Reason = DiagnosticImagingRecord.Reason,
                    Recieved = DiagnosticImagingRecord.Recieved,
                    Date = DiagnosticImagingRecord.Date,
                    Result = DiagnosticImagingRecord.Result,
                    Notes = DiagnosticImagingRecord.Notes,
                    ClassicalInfo = DiagnosticImagingRecord.ClassicalInfo,
                    InternalNotes = DiagnosticImagingRecord.InternalNotes,
                    IsActive = DiagnosticImagingRecord.IsActive,
                    CreatedBy = DiagnosticImagingRecord.CreatedBy,
                    UpdatedBy = DiagnosticImagingRecord.UpdatedBy,
                    CreatedDate = DiagnosticImagingRecord.CreatedDate,
                    UpdatedDate = DiagnosticImagingRecord.UpdatedDate,

                    Assessments = _context.DiagnosticImagingAssessment
                        .Where(DiagnosticAssessment => DiagnosticAssessment.DiagnosticImagingID == DiagnosticImagingRecord.RecordID)
                        .Select(DiagnosticAssessment => new DiagnosticImagingAssessment
                        {
                            ID = DiagnosticAssessment.ID,
                            DiagnosticImagingID = DiagnosticAssessment.DiagnosticImagingID,
                            AssessmentID = DiagnosticAssessment.AssessmentID
                        })
                        .ToList()
                })
                .ToListAsync();
        }

        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAndIsActive(Guid patientId)
        {
            return await _context.DiagnosticImagingDTO
                .Where(DiagnosticImagingRecord => DiagnosticImagingRecord.PatientId == patientId)
                .Select(DiagnosticImagingRecord => new DiagnosticImagingDTO
                {
                    RecordID = DiagnosticImagingRecord.RecordID,
                    OrganizationID = DiagnosticImagingRecord.OrganizationID,
                    PatientId = DiagnosticImagingRecord.PatientId,
                    Status = DiagnosticImagingRecord.Status,
                    Provider = DiagnosticImagingRecord.Provider,
                    Facility = DiagnosticImagingRecord.Facility,
                    AssignedTo = DiagnosticImagingRecord.AssignedTo,
                    FutureOrder = DiagnosticImagingRecord.FutureOrder,
                    HigherPriority = DiagnosticImagingRecord.HigherPriority,
                    InHouse = DiagnosticImagingRecord.InHouse,
                    Procedgure = DiagnosticImagingRecord.Procedgure,
                    OrderDate = DiagnosticImagingRecord.OrderDate,
                    Reason = DiagnosticImagingRecord.Reason,
                    Recieved = DiagnosticImagingRecord.Recieved,
                    Date = DiagnosticImagingRecord.Date,
                    Result = DiagnosticImagingRecord.Result,
                    Notes = DiagnosticImagingRecord.Notes,
                    ClassicalInfo = DiagnosticImagingRecord.ClassicalInfo,
                    InternalNotes = DiagnosticImagingRecord.InternalNotes,
                    IsActive = DiagnosticImagingRecord.IsActive,
                    CreatedBy = DiagnosticImagingRecord.CreatedBy,
                    UpdatedBy = DiagnosticImagingRecord.UpdatedBy,
                    CreatedDate = DiagnosticImagingRecord.CreatedDate,
                    UpdatedDate = DiagnosticImagingRecord.UpdatedDate,

                    Assessments = _context.DiagnosticImagingAssessment
                        .Where(DiagnosticAssessment => DiagnosticAssessment.DiagnosticImagingID == DiagnosticImagingRecord.RecordID)
                        .Select(DiagnosticAssessment => new DiagnosticImagingAssessment
                        {
                            ID = DiagnosticAssessment.ID,
                            DiagnosticImagingID = DiagnosticAssessment.DiagnosticImagingID,
                            AssessmentID = DiagnosticAssessment.AssessmentID
                        })
                        .ToList()
                })
                .ToListAsync();
        }

    }
}
