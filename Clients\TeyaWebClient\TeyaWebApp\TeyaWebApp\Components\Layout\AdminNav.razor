﻿﻿@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IDialogService DialogService
@inject NavigationManager NavigationManager
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@implements IDisposable
<link href="../app.css" rel="stylesheet" />

<MudPaper Width="250px" Class="py-3" Elevation="0">

	<MudNavMenu>
		@if (IsPageAccessible(Menu))
		{
			<MudNavLink Class="mud-navlink-style" Href="/Menu" Icon="@Icons.Material.Filled.MenuBook">@Localizer["Menu"]</MudNavLink>
		}

		@if (IsPageAccessible(Chart))
		{
			<MudNavLink Class="mud-navlink-style" Href="/Chart" Icon="@Icons.Material.Filled.MenuBook">@Localizer["Chart"]</MudNavLink>
		}

		@if (IsPageAccessible(Appointments))
		{
			<MudNavLink Class="mud-navlink-style" Href="/Appointments" Icon="@Icons.Material.Filled.AddCard">Appointments</MudNavLink>
		}



		@if (IsPageAccessible(Practice))
		{
			<MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Practice"]" Expanded="true" Icon="@Icons.Material.Filled.Sports">
				<MudNavLink Class="mud-navlink-style" Href="/Practice" Icon="@Icons.Material.Filled.Sports">@Localizer["Tasks"]</MudNavLink>
				<MudNavLink Class="mud-navlink-style" Href="/OfficeVisit" Icon="@Icons.Material.Filled.People">@Localizer["OfficeVisit"]</MudNavLink>
			</MudNavGroup>
		}
		@if (IsPageAccessible(Chart))
		{
			<MudNavLink Class="mud-navlink-style" Href="/email" Icon="@Icons.Material.Filled.MenuBook">@Localizer["Message"]</MudNavLink>
		}
		@if (IsPageAccessible(Providers) || IsPageAccessible(Patients) || IsPageAccessible(Staff) ||
				IsPageAccessible(LicenseActivation) || IsPageAccessible(Security) || IsPageAccessible(UserManagement))
		{
			<MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Settings"]" Expanded="true" Icon="@Icons.Material.Filled.Settings">
				@if (IsPageAccessible(Providers))
				{
					<MudNavLink Class="mud-navlink-style" Href="/Providers" Icon="@Icons.Material.Filled.People">@Localizer["Providers"]</MudNavLink>
				}

				@if (IsPageAccessible(Patients))
				{
					<MudNavLink Class="mud-navlink-style" Href="/Patients" Icon="@Icons.Material.Filled.People">@Localizer["Patient"]</MudNavLink>
				}


				@if (ShouldRenderLicenseLink)
				{
					@if (IsPageAccessible(LicenseActivation))
					{
						<MudNavGroup Class="mud-navgroup-style" Title="@Localizer["LicenseActivation"]" Expanded="true" Icon="@Icons.Material.Filled.Verified">
							@if (IsPageAccessible(License) && IsPageAccessible(LicenseActivation))
							{

								<MudNavLink Class="mud-navlink-style" Href="/License" Icon="@Icons.Material.Filled.Approval">@Localizer["License"]</MudNavLink>
							}
						</MudNavGroup>
					}
				}

				@if (IsPageAccessible(Security))
				{
					<MudNavLink Class="mud-navlink-style" Href="/Security" Icon="@Icons.Material.Filled.VerifiedUser">@Localizer["Security"]</MudNavLink>
				}

				@if (IsPageAccessible(UserManagement))
				{
					<MudNavLink Class="mud-navlink-style" Href="/UserManagement" Icon="@Icons.Material.Filled.VerifiedUser">@Localizer["UserManagement"]</MudNavLink>
				}
				@if (IsPageAccessible(Templates))
				{
					<MudNavLink Class="mud-navlink-style" Href="/Templates" Icon="@Icons.Material.Filled.DocumentScanner">@Localizer["Templates"]</MudNavLink>
				}

				@if (IsPageAccessible(Config))
				{
					<MudNavLink Class="mud-navlink-style" Href="/Config" Icon="@Icons.Material.Filled.AdminPanelSettings">@Localizer["Config"]</MudNavLink>
				}

				@if (IsPageAccessible(PlanBilling))
				{
					<MudNavLink Class="mud-navlink-style" Href="/PlanBilling" Icon="@Icons.Material.Filled.Money">@Localizer["PlanBilling"]</MudNavLink>
				}
			</MudNavGroup>
		}

	
	</MudNavMenu>
</MudPaper>