using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TeyaUIModels.Model
{
    public class SendMessageRequest : IModel
    {
        [Required]
        [StringLength(100)]
        public string SenderName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string SenderEmailId { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ReceiverName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string ReceiverEmailId { get; set; } = string.Empty;

        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string MessageContent { get; set; } = string.Empty;

        // Multiple recipients support
        public List<string> ToEmails { get; set; } = new List<string>();
        public List<string> CcEmails { get; set; } = new List<string>();
        public List<string> BccEmails { get; set; } = new List<string>();

        // Optional attachments
        public List<AttachmentRequest>? Attachments { get; set; }
    }

    // Soft Delete Request
    public class SoftDeleteMessageRequest : IModel
    {
        [Required]
        public Guid MessageId { get; set; }

        [Required]
        [StringLength(255)]
        public string DeletedBy { get; set; } = string.Empty;

        public string? Reason { get; set; }
    }

    // Restore Message Request
    public class RestoreMessageRequest : IModel
    {
        [Required]
        public Guid MessageId { get; set; }

        [Required]
        [StringLength(255)]
        public string RestoredBy { get; set; } = string.Empty;

        public string? Reason { get; set; }
    }

    // Archive Message Request
    public class ArchiveMessageRequest : IModel
    {
        [Required]
        public Guid MessageId { get; set; }

        [Required]
        [StringLength(255)]
        public string ArchivedBy { get; set; } = string.Empty;
    }

    // Unarchive Message Request
    public class UnarchiveMessageRequest : IModel
    {
        [Required]
        public Guid MessageId { get; set; }

        [Required]
        [StringLength(255)]
        public string UnarchivedBy { get; set; } = string.Empty;
    }

    // Toggle Important Request
    public class ToggleImportantRequest : IModel
    {
        [Required]
        public Guid MessageId { get; set; }

        [Required]
        public bool IsImportant { get; set; }

        [Required]
        [StringLength(255)]
        public string UpdatedBy { get; set; } = string.Empty;
    }
}
