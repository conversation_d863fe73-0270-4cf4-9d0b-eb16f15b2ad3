using System;
using System.Collections.Generic;

namespace TeyaUIModels.Model
{
    public class SendMessageRequest : IModel
    {
        public string SenderName { get; set; } = string.Empty;
        public string SenderEmailId { get; set; } = string.Empty;
        public string ReceiverName { get; set; } = string.Empty;
        public string ReceiverEmailId { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string MessageContent { get; set; } = string.Empty;

        // Multiple recipients support
        public List<string> ToEmails { get; set; } = new List<string>();
        public List<string> CcEmails { get; set; } = new List<string>();
        public List<string> BccEmails { get; set; } = new List<string>();

        // Optional attachments
        public List<AttachmentRequest>? Attachments { get; set; }
    }

}
