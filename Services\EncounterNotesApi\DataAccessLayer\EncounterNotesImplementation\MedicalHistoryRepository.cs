﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class MedicalHistoryRepository : GenericRepository<MedicalHistory>, IMedicalHistoryRepository
    {
        private readonly RecordDatabaseContext _context;

        public MedicalHistoryRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer)
            : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<MedicalHistory>> GetAllMedicalHistoriesAsync()
        {
            return await _context.MedicalHistories
                .AsNoTracking()
                .ToListAsync();
        }
    }
}