﻿CREATE TABLE [EncounterNotesService].[PatientPastResults] (
    [ResultId]       UNIQUEIDENTIFIER NOT NULL,
    [PatientId]      UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId] UNIQUEIDENTIFIER NULL,
    [PCPId]          UN<PERSON>QUE<PERSON>ENTIFIER NULL,
    [CreatedDate]    D<PERSON><PERSON><PERSON><PERSON>         NULL,
    [UpdatedDate]    DATETIME         NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER NOT NULL,
    [OrderName]      NVARCHAR (75)    NULL,
    [OrderDate]      DATETIME         NULL,
    [ResultDate]     DATETIM<PERSON>         NULL,
    [OrderType]      <PERSON>VA<PERSON>HAR (75)    NULL,
    [OrderBy]        NVARCHAR (75)    NULL,
    [ViewResults]    NVARCHAR (75)    NULL,
    [IsActive]       BIT              NULL,
    CONSTRAINT [PK_PatientPastResults] PRIMARY KEY CLUSTERED ([ResultId] ASC, [PatientId] ASC)
);

