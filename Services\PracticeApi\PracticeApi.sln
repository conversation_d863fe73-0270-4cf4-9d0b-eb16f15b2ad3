﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35327.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeApi", "PracticeApi.csproj", "{C0B35DCC-DF13-4ADC-8322-60A9E5B6527A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeContracts", "PracticeContracts\PracticeContracts.csproj", "{E959C328-F92E-4D4A-AA9D-D06BF0920659}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeBusinessLayer", "PracticeBusinessLayer\PracticeBusinessLayer.csproj", "{E5BE8D27-ADC4-4AF1-9303-FE30CB438266}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeDataAccessLayer", "PracticeDataAccessLayer\PracticeDataAccessLayer.csproj", "{08769C6A-E886-4E18-AEBD-1D724F6544C8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{F8B4763E-A2AB-4BF4-96AF-A72614C61CC6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeBusinessLayerTest", "..\PracticeBusinessLayerTest\PracticeBusinessLayerTest.csproj", "{5A775437-0D72-4867-A4C8-36BEDD10BCAD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeApi_Tests", "PracticeApi_Tests\PracticeApi_Tests.csproj", "{8221A3AA-E9F0-43CD-B629-C1F0AC9BA7E1}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C0B35DCC-DF13-4ADC-8322-60A9E5B6527A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0B35DCC-DF13-4ADC-8322-60A9E5B6527A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0B35DCC-DF13-4ADC-8322-60A9E5B6527A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0B35DCC-DF13-4ADC-8322-60A9E5B6527A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E959C328-F92E-4D4A-AA9D-D06BF0920659}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E959C328-F92E-4D4A-AA9D-D06BF0920659}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E959C328-F92E-4D4A-AA9D-D06BF0920659}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E959C328-F92E-4D4A-AA9D-D06BF0920659}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5BE8D27-ADC4-4AF1-9303-FE30CB438266}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5BE8D27-ADC4-4AF1-9303-FE30CB438266}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5BE8D27-ADC4-4AF1-9303-FE30CB438266}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5BE8D27-ADC4-4AF1-9303-FE30CB438266}.Release|Any CPU.Build.0 = Release|Any CPU
		{08769C6A-E886-4E18-AEBD-1D724F6544C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08769C6A-E886-4E18-AEBD-1D724F6544C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08769C6A-E886-4E18-AEBD-1D724F6544C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08769C6A-E886-4E18-AEBD-1D724F6544C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A775437-0D72-4867-A4C8-36BEDD10BCAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A775437-0D72-4867-A4C8-36BEDD10BCAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A775437-0D72-4867-A4C8-36BEDD10BCAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A775437-0D72-4867-A4C8-36BEDD10BCAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{8221A3AA-E9F0-43CD-B629-C1F0AC9BA7E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8221A3AA-E9F0-43CD-B629-C1F0AC9BA7E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8221A3AA-E9F0-43CD-B629-C1F0AC9BA7E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8221A3AA-E9F0-43CD-B629-C1F0AC9BA7E1}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5A775437-0D72-4867-A4C8-36BEDD10BCAD} = {F8B4763E-A2AB-4BF4-96AF-A72614C61CC6}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A2E05C80-5F0A-4455-9B27-9C175D46AA16}
	EndGlobalSection
EndGlobal
