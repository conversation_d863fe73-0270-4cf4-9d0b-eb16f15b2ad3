using System;
using System.Collections.Generic;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class AddressService : IAddressService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<AddressService> _logger;
        private readonly IStringLocalizer<AddressService> _localizer;
        private readonly string _MemberService;

        public AddressService(HttpClient httpClient, ILogger<AddressService> logger, IStringLocalizer<AddressService> localizer)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL") ?? throw new InvalidOperationException(_localizer["Base URL not configured"]);
        }

        public async Task<Address?> GetAddressByIdAsync(Guid id)
        {
            Address? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Address/{id}");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<Address>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch address with ID {id}"], id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching address with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<bool> AddAddressAsync(Address address)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_MemberService}/api/Address", address);
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error adding address"]);
                throw;
            }

            return result;
        }

        public async Task<bool> UpdateAddressAsync(Guid id, Address address)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_MemberService}/api/Address/{id}", address);
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error updating address with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<bool> DeleteAddressAsync(Guid id)
        {
            bool result = false;
            try
            {
                var response = await _httpClient.DeleteAsync($"{_MemberService}/api/Address/{id}");
                result = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error deleting address with ID {id}"], id);
                throw;
            }

            return result;
        }

        public async Task<List<Address>?> GetAddressesByNameAsync(string name)
        {
            List<Address>? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Address/search?name={name}");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<Address>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch addresses with name {name}"], name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching addresses with name {name}"], name);
                throw;
            }

            return result;
        }

        public async Task<List<Address>?> GetAllAddressesAsync()
        {
            List<Address>? result = null;
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Address");
                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<Address>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch all addresses"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching all addresses"]);
                throw;
            }

            return result;
        }
    }
}
