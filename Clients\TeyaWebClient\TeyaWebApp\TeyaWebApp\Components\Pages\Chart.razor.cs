﻿using Microsoft.AspNetCore.Components;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Represents a Blazor component for displaying patient chart data.
    /// </summary>
    public partial class Chart : ComponentBase
    {
        /// <summary>
        /// Injected service for managing patient data.
        /// </summary>
        [Inject]
        private PatientService PatientService { get; set; }

        /// <summary>
        /// Gets the patient data from the service or initializes a new instance if null.
        /// </summary>
        public Patient PatientData => PatientService.PatientData ?? new Patient();
    }
}
