﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Contracts
{
    public class Insurance : IContract
    {
        public Guid InsuranceId { get; set; }
        public string? PrimaryInsuranceProvider { get; set; }
        public string? PlanName { get; set; }
        public string? Subscriber { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public string? Relationship { get; set; }
        public string? PolicyNumber { get; set; }
        public string? GroupNumber { get; set; }
        public string? SocialSecurityNumber { get; set; }
        public string? SubscriberEmployer { get; set; }
        public string? Sex { get; set; }
        public string? SubscriberAddressLine1 { get; set; }
        public string? SubscriberAddressLine2 { get; set; }
        public string? SubscriberCity { get; set; }
        public string? SubscriberState { get; set; }
        public string? SubscriberZipCode { get; set; }
        public string? SubscriberCountry { get; set; }
        public string? SubscriberPhone { get; set; }
        public decimal? CoPay { get; set; }
        public bool? AcceptAssignment { get; set; }
        [JsonIgnore]
        public virtual Member? member { get; set; }
    }
}
