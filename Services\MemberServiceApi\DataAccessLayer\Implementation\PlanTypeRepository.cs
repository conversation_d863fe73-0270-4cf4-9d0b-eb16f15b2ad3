﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class PlanTypeRepository : GenericRepository<PlanType>, IPlanTypeRepository
    {
        private readonly AccountDatabaseContext _context;

        public PlanTypeRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }

        public async Task AddAsync(PlanType license)
        {
            await _context.PlanTypes.AddAsync(license);
        }

        public async Task UpdateAsync(PlanType license)
        {
            _context.PlanTypes.Update(license);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var license = await _context.PlanTypes.FindAsync(id);
            if (license != null)
            {
                _context.PlanTypes.Remove(license);
            }
        }

        public async Task<PlanType> GetByIdAsync(Guid id)
        {
            var result = await _context.PlanTypes.FindAsync(id);
            return result;
        }

        public async Task<List<PlanType>> GetAllAsync()
        {
            var result = await _context.PlanTypes.ToListAsync();
            return result;
        }

    }
}