﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class SpeechRequest:IContract
    {
        public Guid Id { get; set; } 

        public Guid PCPId { get; set; }

        public Guid PatientId { get; set; }
        public List<Speech> Speeches { get; set; }
        
        public String? VisitType {  get; set; }
        public string accessToken {  get; set; }
    }
}
