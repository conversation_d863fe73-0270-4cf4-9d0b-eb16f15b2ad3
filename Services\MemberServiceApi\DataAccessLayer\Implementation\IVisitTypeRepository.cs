﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceDataAccessLayer
{
    public interface IVisitTypeRepository : IGenericRepository<VisitType>
    {
        Task<IEnumerable<VisitType>> GetByOrganizationIdAsync(Guid orgId);
        Task<bool> UpdateCptCodeAsync(Guid orgId, string visitName, string newCptCode);
        Task<bool> DeleteVisitTypeAsync(Guid orgId, string visitName);
        Task<VisitType> GetFirstOrDefaultAsync(Expression<Func<VisitType, bool>> predicate);
    }
}
