﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Contracts\MessageContracts.csproj" />
    <ProjectReference Include="..\MessageDataAccessLayer\MessageDataAccessLayer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageReference Include="Azure.Communication.Chat" Version="1.3.0" />
    <PackageReference Include="Azure.Communication.Identity" Version="1.3.1" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="CommandHandler\" />
    <Folder Include="QueryHandler\" />
  </ItemGroup>

</Project>
