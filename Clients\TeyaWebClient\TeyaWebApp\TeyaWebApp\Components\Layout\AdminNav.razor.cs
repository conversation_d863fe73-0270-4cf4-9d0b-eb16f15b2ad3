using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.Graph.Models;
using MudBlazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Pages;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Layout
{
    public partial class AdminNav : ComponentBase
    {
        private readonly string Menu = "/Menu";
        private readonly string Chart = "/Chart";
        private readonly string Appointments = "/Appointments";
        private readonly string Message = "/Message";
        private readonly string Practice = "/Practice";
        private readonly string Document = "/Document";
        private readonly string Providers = "/Providers";
        private readonly string Patients = "/Patients";
        private readonly string PlanBilling = "/PlanBilling";
        private readonly string Staff = "/Staff";
        private readonly string LicenseActivation = "/LicenseActivation";
        private readonly string License = "/License";
        private readonly string Security = "/Security";
        private readonly string UserManagement = "/UserManagement";
        private readonly string Templates = "/Templates";
        private readonly string Config = "/Config";
        private readonly string About = "/about";
        private const string adminRole = "Admin";

        private bool ShouldRenderLicenseLink = false;
        [Inject] private IMemberService MemberService { get; set; } = default!;
        [Inject] private IPageRoleMappingService PageRoleMappingService { get; set; } = default!;
        [Inject] private IPreDefinedPageRoleMappingService PreDefinedPageRoleMappingService { get; set; } = default!;
        [Inject] private ILogger<AdminNav> Logger { get; set; } = default!;
        [Inject] private RoleMappingState RoleMappingState { get; set; }
        [Inject] private ActiveUser activeUser { get; set; }

        private List<Member> members = new();
        private List<PageRoleMappingData> PageRoleMappings = new();
        private List<PreDefinedPageRoleMappingData> PreDefinedPageRoleMappings = new();

        protected override async Task OnInitializedAsync()
        {
            await LoadPageRoleMappingsAsync();

            RoleMappingState.OnChange += async () =>
            {
                await LoadPageRoleMappingsAsync();
                await InvokeAsync(StateHasChanged);
            };
            await CheckUserAccess();
        }

        private async Task CheckUserAccess()
        {

            if (activeUser?.mail?.EndsWith("@teyahealth.com") == true)
            {
                ShouldRenderLicenseLink = true;
            }
        }

        private async Task LoadPageRoleMappingsAsync()
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                try
                {
                    members = await MemberService.GetAllMembersAsync();
                    var preferredName = user.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value;
                    var matchingMember = members.FirstOrDefault(m => m.Email == preferredName);
                    activeUser.role = matchingMember.RoleName;

                    if (matchingMember.RoleName != adminRole)
                    {
                        var preDefinedMappings = await PreDefinedPageRoleMappingService.GetPagesByRoleNameAsync(matchingMember.RoleName);
                        PreDefinedPageRoleMappings = preDefinedMappings?.ToList();
                    }
                    else if (matchingMember != null && matchingMember.RoleID.HasValue)
                    {
                        var pageRoleMappings = await PageRoleMappingService.GetPagesByRoleIdAsync(matchingMember.RoleID.Value);
                        PageRoleMappings = pageRoleMappings?.ToList();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError($"Error during data loading: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            RoleMappingState.OnChange -= async () => await InvokeAsync(StateHasChanged);
        }

        private bool IsPageAccessible(string pageUrl)
        {
            if(activeUser.role == "Admin")
            {
                return PageRoleMappings != null && PageRoleMappings.Any(p => string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));
            }
            else
            {
                return PreDefinedPageRoleMappings != null && PreDefinedPageRoleMappings.Any(p => string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));
            }
        }

        
    }
}