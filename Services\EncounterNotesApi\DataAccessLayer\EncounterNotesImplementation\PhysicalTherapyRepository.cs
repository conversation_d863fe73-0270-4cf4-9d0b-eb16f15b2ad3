﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class PhysicalTherapyRepository : GenericRepository<PhysicalTherapyData>, IPhysicalTherapyRepository
    {
        private readonly RecordDatabaseContext _context;

        public PhysicalTherapyRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer)
            : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<PhysicalTherapyData>> GetAllPhysicalTherapyAsync()
        {
            return await _context._PhysicalTherapy
                .AsNoTracking()
                .ToListAsync();
        }
    }
}