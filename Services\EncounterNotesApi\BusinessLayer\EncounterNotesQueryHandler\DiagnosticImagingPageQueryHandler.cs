﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class DiagnosticImagingPageQueryHandler : IDiagnosticImagingPageQueryHandler<DiagnosticImagingDTO>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        public DiagnosticImagingPageQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        ///  Get All Diagnostic Imaging by Id 
        /// </summary>
        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingById(Guid id)
        {
            var result = new List<DiagnosticImagingDTO>();
            try
            {
                result = await _unitOfWork.DiagnosticImagingPageRepository.GetDiagnosticImagingById(id);
                return result;
            }
            catch (Exception ex)
            {
                return result;
            }
        }

        /// <summary>
        ///  Get Active Diagnostic Imaging by Id 
        /// </summary>
        /// 
        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAndIsActive(Guid id)
        {
            var result = new List<DiagnosticImagingDTO>();
            try
            {
                result = await _unitOfWork.DiagnosticImagingPageRepository.GetDiagnosticImagingByIdAndIsActive(id);
                return result;
            }
            catch (Exception ex)
            {
                return result;
            }
        }
    }
}
