﻿using System;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace Contracts
{
    public class Address : IContract
    {
        
        public Guid? AddressId { get; set; }

        public string? AddressLine1 { get; set; }

        public string? AddressLine2 { get; set; }

        public string? City { get; set; }

        public string? State { get; set; }
        public string? PostalCode { get; set; }
        public string? Country { get; set; }

        [JsonIgnore]
        public ICollection<Member>? Members { get; set; }
    }
}
