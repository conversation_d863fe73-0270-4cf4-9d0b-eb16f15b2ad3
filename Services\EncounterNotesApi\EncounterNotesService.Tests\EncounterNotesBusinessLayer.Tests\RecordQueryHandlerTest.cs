﻿using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesContracts;
using Microsoft.Extensions.Configuration;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using OpenAI.Assistants;
using static System.Collections.Specialized.BitVector32;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Runtime.ConstrainedExecution;
using NUnit.Framework.Internal;
using static NUnit.Framework.Constraints.Tolerance;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Routing;

namespace EncounterNotesBusinessLayer.Tests
{
    [TestFixture]
    public class RecordQueryHandlerTest
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IRecordRepository> _recordRepositoryMock;
        private RecordQueryHandler _recordQueryHandler;

        [SetUp]
        public void SetUp()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _recordRepositoryMock = new Mock<IRecordRepository>();

            _unitOfWorkMock.Setup(u => u.RecordRepository).Returns(_recordRepositoryMock.Object);
            _recordQueryHandler = new RecordQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

    //    [Test]
    //    public async Task GetRecord_ShouldReturnRecords_WithWordTimings()
    //    {
    //        // Arrange
    //        var WordTimings = new List<WordTiming>
    //        {
    //            new WordTiming { Id = 24, Word = "In progress", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() },
    //            new WordTiming { Id = 21, Word = "done", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() }
    //        };

    //        var records = new List<Record>
    //        {
    //            new Record { Id = Guid.NewGuid(), PatientName = "Akon", DateTime = new DateTime(2022, 12, 25, 15, 30, 0), Summary = "All is well", CurrentMedication = "DDSA5", MedicalHistory = "Nil", SurgicalHistory = "Nil", HospitalizationHistory = "Nill", FamilyHistory = "Norm", SocialHistory = "Well" ,Vitals = "Norm", Assessment = "As02", TreatmentPlan = "Plan1" , Transcription = "do Physical exercise", WordTimings = WordTimings },
    //            new Record { Id = Guid.NewGuid(), PatientName = "Bkon", DateTime = DateTime.Now, Summary = "Critical", CurrentMedication = "DDSA4", MedicalHistory = "18 yrs", SurgicalHistory = "Nil", HospitalizationHistory = "frequent", FamilyHistory = "Norm", SocialHistory = "Not Well" ,Vitals = "Norm", Assessment = "As01", TreatmentPlan = "Plan14" , Transcription = "do exercise", WordTimings = WordTimings },
    //            new Record { Id = Guid.NewGuid(), PatientName = "Ckon", DateTime = new DateTime(2024, 01, 01, 04, 30, 0), Summary = "Normal", CurrentMedication = "DDSA1", MedicalHistory = "Nil", SurgicalHistory = "4", HospitalizationHistory = "Nill", FamilyHistory = "Norm", SocialHistory = "Well" ,Vitals = "Norm", Assessment = "As05", TreatmentPlan = "Plan7" , Transcription = "do Physical exercise", WordTimings = WordTimings }
    //        };

    //        _recordRepositoryMock.Setup(r => r.GetAllAsync()).ReturnsAsync(records);
    //        _recordRepositoryMock.Setup(r => r.GetWordTimings(It.IsAny<Guid>()))
    //            .ReturnsAsync(new List<WordTiming> { new WordTiming(), new WordTiming() });

    //        // Act
    //        var result = await _recordQueryHandler.GetRecord();

    //        // Assert
    //        Assert.That(result, Is.Not.Null);
    //        Assert.That(result.Count(), Is.EqualTo(3));
    //        Assert.That(result.First().WordTimings, Is.Not.Null);
    //        Assert.That(result.First().WordTimings.Count, Is.EqualTo(2));
    //    }

    //    [Test]
    //    public async Task GetRecordById_ShouldReturnRecord_WhenRecordExists()
    //    {
    //        // Arrange
    //        var WordTimings = new List<WordTiming>
    //        {
    //            new WordTiming { Id = 24, Word = "In progress", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() },
    //    new WordTiming { Id = 21, Word = "done", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() }
    //};

    //        var recordId = Guid.NewGuid();

    //        var record = new Record
    //        {
    //            Id = recordId,
    //            PatientName = "Xkon",
    //            DateTime = new DateTime(2022, 12, 25, 15, 30, 0),
    //            Summary = "All is well",
    //            CurrentMedication = "DDSA5x",
    //            MedicalHistory = "Nil",
    //            SurgicalHistory = "Nil",
    //            HospitalizationHistory = "Nill",
    //            FamilyHistory = "Norm",
    //            SocialHistory = "Well",
    //            Vitals = "Norm",
    //            Assessment = "As02",
    //            TreatmentPlan = "Plan1",
    //            Transcription = "do Physical exercise",
    //            WordTimings = WordTimings
    //        };

    //        _recordRepositoryMock.Setup(r => r.GetByIdAsync(recordId)).ReturnsAsync(record);

    //        // Act
    //        var result = await _recordQueryHandler.GetRecordById(recordId);

    //        // Assert
    //        Assert.That(result, Is.Not.Null);
    //        Assert.That(result.Id, Is.EqualTo(recordId));
    //    }
        [Test]
        public async Task GetRecordById_ShouldReturnNull_WhenRecordDoesNotExist()
        {
            // Arrange
            var recordId = Guid.NewGuid();

            _recordRepositoryMock.Setup(r => r.GetByIdAsync(recordId)).ReturnsAsync((Record)null);

            // Act
            var result = await _recordQueryHandler.GetRecordById(recordId);

            // Assert
            Assert.That(result, Is.Null);
        }
    }
}

