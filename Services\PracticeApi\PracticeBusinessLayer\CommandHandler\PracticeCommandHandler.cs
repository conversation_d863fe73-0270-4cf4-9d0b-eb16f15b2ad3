﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PracticeBusinessLayer.CommandHandler
{
    public class PracticeCommandHandler :IPracticeCommandHandler<Tasks>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PracticeCommandHandler> _logger;

        public PracticeCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<PracticeCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddTasks(List<Tasks> tasks)
        {
            await _unitOfWork.PracticeRepository.AddAsync(tasks);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateTasks(Tasks task)
        {
            await _unitOfWork.PracticeRepository.UpdateAsync(task);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTasksById(Guid id)
        {
            await _unitOfWork.PracticeRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTasksByEntity(Tasks task)
        {
            await _unitOfWork.PracticeRepository.DeleteByEntityAsync(task);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateTasksByPatientId(Tasks task)
        {
            await _unitOfWork.PracticeRepository.UpdateTasksByPatientIdAsync(task);
            await _unitOfWork.SaveAsync();
        }
    }
}
