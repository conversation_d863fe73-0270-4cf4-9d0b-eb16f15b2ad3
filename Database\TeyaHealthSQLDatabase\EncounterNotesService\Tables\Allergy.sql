﻿CREATE TABLE [EncounterNotesService].[Allergy] (
    [MedicineId]     UNIQUEIDENTIFIER NOT NULL,
    [PatientId]      UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId] UNIQUEIDENTIFIER NOT NULL,
    [AllergyInfo]    NVARCHAR (200)   NULL,
    [Classification] NVARCHAR (50)    NULL,
    [Agent]          NVARCHAR (200)   NULL,
    [Type]           NVARCHAR (50)    NULL,
    [Substance]      NVARCHAR (200)   NULL,
    [Reaction]       NVARCHAR (200)   NULL,
    [PCPId]          UNIQUEIDENTIFIER NOT NULL,
    [DrugName]       NVARCHAR (200)   NULL,
    [DrugDetails]    NVARCHAR (200)   NULL,
    [isActive]       BIT              NOT NULL,
    [CreatedBy]     UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]     UNIQUEIDENTIFIER NOT NULL,
    [CreatedOn]      DATETIME         NULL,
    [UpdatedOn]      DATETIME         NULL,
    CONSTRAINT [PK_Allergy] PRIMARY KEY CLUSTERED ([MedicineId] ASC, [PatientId] ASC)
);
GO

