﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class SocialHistoryQueryHandler : ISocialHistoryQueryHandler<PatientSocialHistory>
    {
        private readonly IUnitOfWork _unitOfWork;
        public SocialHistoryQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Retrieves the active social history records for a given patient ID.
        /// </summary>
        /// <param name="patientId">The unique identifier of the patient.</param>
        /// <returns>A list of active social history records for the patient.</returns>
        public async Task<List<PatientSocialHistory>> GetHistoryByIdAndIsActive(Guid patientId)
        {
            var allRecords = await _unitOfWork.SocialHistoryRepository.GetAsync();
            return allRecords
                     .Where(history => history.PatientId == patientId && history.isActive == true)
                     .ToList();
        }

        /// <summary>
        /// Retrieves all social history records for a given patient ID, regardless of active status.
        /// </summary>
        /// <param name="patientId">The unique identifier of the patient.</param>
        /// <returns>A list of all social history records for the patient.</returns>
        public async Task<List<PatientSocialHistory>> GetAllByPatientId(Guid patientId)
        {
            var allRecords = await _unitOfWork.SocialHistoryRepository.GetAsync();
            return allRecords
                     .Where(history => history.PatientId == patientId)
                     .ToList();
        }
    }
}
