﻿using AppointmentDataAccessLayer.DataAccessLayerResources;
using Microsoft.Azure.SqlDatabase.ElasticScale.ShardManagement;
using Microsoft.Extensions.Localization;
using System.Linq;

namespace AppointmentDataAccessLayer.AppointmentImplementation
{
    public class ShardMapManagerService : IShardMapManagerService
    {
        private readonly string _shardMapManagerConnectionString;
        private ShardMapManager _shardMapManager;
        private readonly IStringLocalizer<DataAccessLayerStrings> _localizer;

        public ShardMapManagerService(string shardMapManagerConnectionString)
        {
            _shardMapManagerConnectionString = shardMapManagerConnectionString;
            InitializeShardMapManager();
        }

        private void InitializeShardMapManager()
        {
            bool exists = ShardMapManagerFactory.TryGetSqlShardMapManager(
                _shardMapManagerConnectionString,
                ShardMapManagerLoadPolicy.Lazy,
                out _shardMapManager);

            if (!exists)
            {
                _shardMapManager = ShardMapManagerFactory.CreateSqlShardMapManager(_shardMapManagerConnectionString);
            }
        }

        public ListShardMap<byte[]> CreateOrGetListShardMap(string shardMapName)
        {
            ListShardMap<byte[]> shardMap;
            if (!_shardMapManager.TryGetListShardMap(shardMapName, out shardMap))
            {
                shardMap = _shardMapManager.CreateListShardMap<byte[]>(shardMapName);
            }
            return shardMap;
        }

        public void AddShard(string serverName, string databaseName, byte[] key)
        {
            var shardLocation = new ShardLocation(serverName, databaseName);
            var shardMap = CreateOrGetListShardMap("MyExampleShardMap");

            if (shardMap.GetShards().Any(shard => shard.Location.Equals(shardLocation)))
            {
                return; 
            }

            var shard = shardMap.CreateShard(shardLocation);
            shardMap.CreatePointMapping(key, shard);
        }


        public Shard GetShardForKey(byte[] key)
        {
            var shardMap = CreateOrGetListShardMap("MyExampleShardMap");
            if (shardMap.TryGetMappingForKey(key, out var mapping))
            {
                return mapping.Shard; 
            }
            return null;
        }
        public int GetTotalShards()
        {
            var shardMap = CreateOrGetListShardMap(_localizer["MyExampleShardMap"]);
            return shardMap.GetShards().Count();
        }
        public List<byte[]> GetAllKeys()
        {
            var shardMap = CreateOrGetListShardMap("MyExampleShardMap");
            return shardMap.GetMappings().Select(mapping => mapping.Value).ToList();
        }
    }
}
