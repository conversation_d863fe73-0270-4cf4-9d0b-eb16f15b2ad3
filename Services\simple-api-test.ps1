# Simple MessageAPI Test Suite
$baseUrl = "http://localhost:5000/api/Message"

Write-Host "🚀 Starting MessageAPI Test Suite" -ForegroundColor Yellow
Write-Host "=" * 50

# Test 1: Send Message without Attachment
Write-Host "`n🧪 Test 1: Send Message (No Attachment)" -ForegroundColor Cyan
$body1 = @{
    senderName = "Alice Johnson"
    senderEmailId = "<EMAIL>"
    receiverName = "Bob Smith"
    receiverEmailId = "<EMAIL>"
    messageContent = "Hello Bob! This is a test message."
} | ConvertTo-Json

try {
    $response1 = Invoke-RestMethod -Uri "$baseUrl/send" -Method POST -Body $body1 -ContentType "application/json"
    Write-Host "   ✅ SUCCESS: Message sent with ID $($response1.messageId)" -ForegroundColor Green
    $messageId1 = $response1.messageId
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Send Message with Attachment
Write-Host "`n🧪 Test 2: Send Message (With Attachment)" -ForegroundColor Cyan
$fileBytes = [System.IO.File]::ReadAllBytes("test-attachment.txt")
$base64Content = [System.Convert]::ToBase64String($fileBytes)

$body2 = @{
    senderName = "Bob Smith"
    senderEmailId = "<EMAIL>"
    receiverName = "Alice Johnson"
    receiverEmailId = "<EMAIL>"
    messageContent = "Hi Alice! Please find the attached file."
    attachments = @(
        @{
            fileName = "test-attachment.txt"
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileBytes.Length
        }
    )
} | ConvertTo-Json -Depth 3

try {
    $response2 = Invoke-RestMethod -Uri "$baseUrl/send" -Method POST -Body $body2 -ContentType "application/json"
    Write-Host "   ✅ SUCCESS: Message with attachment sent, ID $($response2.messageId)" -ForegroundColor Green
    $messageId2 = $response2.messageId
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Get Message by ID
Write-Host "`n🧪 Test 3: Get Message by ID" -ForegroundColor Cyan
try {
    $message = Invoke-RestMethod -Uri "$baseUrl/$messageId1" -Method GET
    Write-Host "   ✅ SUCCESS: Retrieved message from $($message.senderName)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Get Messages by Email
Write-Host "`n🧪 Test 4: Get Messages by Email" -ForegroundColor Cyan
try {
    $messages = Invoke-RestMethod -Uri "$baseUrl/email/<EMAIL>" -Method GET
    Write-Host "   ✅ SUCCESS: Retrieved $($messages.Count) messages for Alice" -ForegroundColor Green
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Get Conversation
Write-Host "`n🧪 Test 5: Get Conversation" -ForegroundColor Cyan
try {
    $conversation = Invoke-RestMethod -Uri "$baseUrl/conversation/<EMAIL>/<EMAIL>" -Method GET
    Write-Host "   ✅ SUCCESS: Retrieved conversation with $($conversation.Count) messages" -ForegroundColor Green
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Get Unread Messages
Write-Host "`n🧪 Test 6: Get Unread Messages" -ForegroundColor Cyan
try {
    $unread = Invoke-RestMethod -Uri "$baseUrl/unread/<EMAIL>" -Method GET
    Write-Host "   ✅ SUCCESS: Retrieved $($unread.Count) unread messages" -ForegroundColor Green
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Get Sent Messages
Write-Host "`n🧪 Test 7: Get Sent Messages" -ForegroundColor Cyan
try {
    $sent = Invoke-RestMethod -Uri "$baseUrl/sent/<EMAIL>" -Method GET
    Write-Host "   ✅ SUCCESS: Retrieved $($sent.Count) sent messages" -ForegroundColor Green
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Get Received Messages
Write-Host "`n🧪 Test 8: Get Received Messages" -ForegroundColor Cyan
try {
    $received = Invoke-RestMethod -Uri "$baseUrl/received/<EMAIL>" -Method GET
    Write-Host "   ✅ SUCCESS: Retrieved $($received.Count) received messages" -ForegroundColor Green
} catch {
    Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 9: Get Message Attachments
if ($messageId2) {
    Write-Host "`n🧪 Test 9: Get Message Attachments" -ForegroundColor Cyan
    try {
        $attachments = Invoke-RestMethod -Uri "$baseUrl/$messageId2/attachments" -Method GET
        Write-Host "   ✅ SUCCESS: Retrieved $($attachments.Count) attachments" -ForegroundColor Green

        if ($attachments.Count -gt 0) {
            $attachmentId = $attachments[0].attachmentId

            # Test 10: Download Attachment
            Write-Host "`n🧪 Test 10: Download Attachment" -ForegroundColor Cyan
            try {
                Invoke-WebRequest -Uri "$baseUrl/attachment/$attachmentId/download" -OutFile "test-downloaded.txt"
                Write-Host "   ✅ SUCCESS: Attachment downloaded" -ForegroundColor Green
            } catch {
                Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 11: Mark Message as Read
if ($messageId1) {
    Write-Host "`n🧪 Test 11: Mark Message as Read" -ForegroundColor Cyan
    try {
        $result = Invoke-RestMethod -Uri "$baseUrl/$messageId1/read" -Method PUT
        Write-Host "   ✅ SUCCESS: Message marked as read" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🏁 Test Suite Complete!" -ForegroundColor Yellow
