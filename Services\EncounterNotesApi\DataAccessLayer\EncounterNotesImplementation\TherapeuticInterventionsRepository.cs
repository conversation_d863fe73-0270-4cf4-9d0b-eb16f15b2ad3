﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class TherapeuticInterventionsRepository : GenericRepository<TherapeuticInterventionsData>, ITherapeuticInterventionsRepository
    {
        private readonly RecordDatabaseContext _context;

        public TherapeuticInterventionsRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer)
            : base(context, localizer)
        {
            _context = context;
        }

        public async Task<IEnumerable<TherapeuticInterventionsData>> GetAllTherapeuticInterventionsAsync()
        {
            return await _context._TherapeuticInterventions
                .AsNoTracking()
                .ToListAsync();
        }
    }
}