﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35222.181
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Appointments", "Appointments.csproj", "{E8785E17-A516-42F1-A839-63046EB968AF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentContracts", "Contracts\AppointmentContracts.csproj", "{1901CAC5-E827-4C61-9790-2285045AC08B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentDataAccessLayer", "DataAccessLayer\AppointmentDataAccessLayer.csproj", "{BEDFD2C8-E29E-4DD8-9AF4-70A51DA75FF5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Appointmentsapi.Test", "Appointmentsapi.Test\Appointmentsapi.Test.csproj", "{5C90370F-E862-40AF-8986-D3561CC7674C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentsBusinessLayer", "BusinessLayer\AppointmentsBusinessLayer.csproj", "{D98B084D-0B69-4F0A-BF12-3B2EED179240}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{B3C6E57D-0895-4971-8F3E-E2C6CBD84808}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppointmentsBusinessLayerTest", "..\AppointmentsBusinessLayerTest\AppointmentsBusinessLayerTest.csproj", "{228D9DBC-57C6-4EC3-863A-55F96CA9B310}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E8785E17-A516-42F1-A839-63046EB968AF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E8785E17-A516-42F1-A839-63046EB968AF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E8785E17-A516-42F1-A839-63046EB968AF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E8785E17-A516-42F1-A839-63046EB968AF}.Release|Any CPU.Build.0 = Release|Any CPU
		{1901CAC5-E827-4C61-9790-2285045AC08B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1901CAC5-E827-4C61-9790-2285045AC08B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1901CAC5-E827-4C61-9790-2285045AC08B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1901CAC5-E827-4C61-9790-2285045AC08B}.Release|Any CPU.Build.0 = Release|Any CPU
		{BEDFD2C8-E29E-4DD8-9AF4-70A51DA75FF5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BEDFD2C8-E29E-4DD8-9AF4-70A51DA75FF5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BEDFD2C8-E29E-4DD8-9AF4-70A51DA75FF5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BEDFD2C8-E29E-4DD8-9AF4-70A51DA75FF5}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C90370F-E862-40AF-8986-D3561CC7674C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C90370F-E862-40AF-8986-D3561CC7674C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C90370F-E862-40AF-8986-D3561CC7674C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C90370F-E862-40AF-8986-D3561CC7674C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D98B084D-0B69-4F0A-BF12-3B2EED179240}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D98B084D-0B69-4F0A-BF12-3B2EED179240}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D98B084D-0B69-4F0A-BF12-3B2EED179240}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D98B084D-0B69-4F0A-BF12-3B2EED179240}.Release|Any CPU.Build.0 = Release|Any CPU
		{228D9DBC-57C6-4EC3-863A-55F96CA9B310}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{228D9DBC-57C6-4EC3-863A-55F96CA9B310}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{228D9DBC-57C6-4EC3-863A-55F96CA9B310}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{228D9DBC-57C6-4EC3-863A-55F96CA9B310}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5C90370F-E862-40AF-8986-D3561CC7674C} = {B3C6E57D-0895-4971-8F3E-E2C6CBD84808}
		{228D9DBC-57C6-4EC3-863A-55F96CA9B310} = {B3C6E57D-0895-4971-8F3E-E2C6CBD84808}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {7ED5A283-1727-43E4-BC62-F61D18071C17}
	EndGlobalSection
EndGlobal
