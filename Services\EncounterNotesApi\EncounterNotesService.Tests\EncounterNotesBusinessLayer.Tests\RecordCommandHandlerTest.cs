﻿using System;
using System.Collections.Generic;
using System.Linq;
using Moq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Logging;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using NUnit.Framework;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Routing;

namespace EncounterNotesBusinessLayer.Tests
{
    public class RecordCommandHandlerTest
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<RecordCommandHandler>> _loggerMock;
        private Mock<IRecordRepository> _recordRepositoryMock;
        private RecordCommandHandler _recordCommandHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<RecordCommandHandler>>();
            _recordRepositoryMock = new Mock<IRecordRepository>();

            _unitOfWorkMock.Setup(u => u.RecordRepository).Returns(_recordRepositoryMock.Object);

            _recordCommandHandler = new RecordCommandHandler(_configurationMock.Object, _unitOfWorkMock.Object, _loggerMock.Object);
        }

        [Test]
        public async Task AddRecord_ShouldAddRecordAndSave_WhenAddRecordHandlerCalled()
        {
            //Arrange
            var WordTimings = new List<WordTiming>
            {
                new WordTiming { Id = 24, Word = "In progress", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() },
                new WordTiming { Id = 21, Word = "done", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() }
            };

            //var records = new List<Record>
            //{
            //    new Record { Id = Guid.NewGuid(), PatientName = "Akon", DateTime = new DateTime(2022, 12, 25, 15, 30, 0), Summary = "All is well", CurrentMedication = "DDSA5", MedicalHistory = "Nil", SurgicalHistory = "Nil", HospitalizationHistory = "Nill", FamilyHistory = "Norm", SocialHistory = "Well" ,Vitals = "Norm", Assessment = "As02", TreatmentPlan = "Plan1" , Transcription = "do Physical exercise", WordTimings = WordTimings },
            //    new Record { Id = Guid.NewGuid(), PatientName = "Bkon", DateTime = DateTime.Now, Summary = "Critical", CurrentMedication = "DDSA4", MedicalHistory = "18 yrs", SurgicalHistory = "Nil", HospitalizationHistory = "frequent", FamilyHistory = "Norm", SocialHistory = "Not Well" ,Vitals = "Norm", Assessment = "As01", TreatmentPlan = "Plan14" , Transcription = "do exercise", WordTimings = WordTimings },
            //    new Record { Id = Guid.NewGuid(), PatientName = "Ckon", DateTime = new DateTime(2024, 01, 01, 04, 30, 0), Summary = "Normal", CurrentMedication = "DDSA1", MedicalHistory = "Nil", SurgicalHistory = "4", HospitalizationHistory = "Nill", FamilyHistory = "Norm", SocialHistory = "Well" ,Vitals = "Norm", Assessment = "As05", TreatmentPlan = "Plan7" , Transcription = "do Physical exercise", WordTimings = WordTimings }
            //};

            //Act
            //await _recordCommandHandler.AddRecord(records);

            //Assert
            _recordRepositoryMock.Verify(r => r.AddRecordsAsync(It.Is<List<Record>>(l => l.Count == 3)), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateRecord_ShouldUpdateRecordAndSave_WhenUpdateRecordHandlerCalled()
        {
            //Arrange
            var WordTimings = new List<WordTiming>
            {
                new WordTiming { Id = 24, Word = "In progress", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() },
                new WordTiming { Id = 21, Word = "done", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() }
            };

            var record = new Record
            {
                Id = Guid.NewGuid(),
                PatientName = "Xkon",
                DateTime = new DateTime(2022, 12, 25, 15, 30, 0),
                //Summary = "All is well",
                //CurrentMedication = "DDSA5x",
                //MedicalHistory = "Nil",
                //SurgicalHistory = "Nil",
                //HospitalizationHistory = "Nill",
                //FamilyHistory = "Norm",
                //SocialHistory = "Well",
                //Vitals = "Norm",
                //Assessment = "As02",
                //TreatmentPlan = "Plan1",
                Transcription = "do Physical exercise",
                WordTimings = WordTimings
            };

            //Act
            await _recordCommandHandler.UpdateRecord(record);

            //Assert
            //_recordRepositoryMock.Verify(r => r.UpdateAsync(It.Is<Record>(r => r.Id != Guid.Empty && r.Id.GetType() == typeof(Guid) && r.PatientName == "Xkon" && r.CurrentMedication == "DDSA5x")), Times.Once);
            //_unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);

        }

        [Test]
        public async Task DeleteRecordById_ShouldDeleteRecordById_WhenDeleteRecordByIdHandlerCalled()
        {
            //Arrange
            var id = Guid.NewGuid();

            //Act
            await _recordCommandHandler.DeleteRecordById(id);

            //Assert
            _recordRepositoryMock.Verify(r => r.DeleteByIdAsync(It.Is<Guid>(i => i == id)), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteRecordByEntity_ShouldDeleteRecordByEntity_WhenDeleteRecordByEntityHandlerCalled()
        {
            //Arrange
            var WordTimings = new List<WordTiming>
            {
                new WordTiming { Id = 24, Word = "In progress", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() },
                new WordTiming { Id = 21, Word = "done", StartTime = 15.25, EndTime = 16.30, RecordId = Guid.NewGuid() }
            };

            var record = new Record
            {
                Id = Guid.NewGuid(),
                PatientName = "Bkon",
                DateTime = new DateTime(2022, 12, 25, 15, 30, 0),
                //Summary = "All is well",
                //CurrentMedication = "DDSA5x",
                //MedicalHistory = "Nil",
                //SurgicalHistory = "Nil",
                //HospitalizationHistory = "Nill",
                //FamilyHistory = "Norm",
                //SocialHistory = "Well",
                //Vitals = "Norm",
                //Assessment = "As02",
                //TreatmentPlan = "Plan1",
                Transcription = "do Physical exercise",
                WordTimings = WordTimings
            };

            //Act
            await _recordCommandHandler.DeleteRecordByEntity(record);

            //Assert
            //_recordRepositoryMock.Verify(r => r.DeleteByEntityAsync(It.Is<Record>(r => r.Id == record.Id && r.PatientName == record.PatientName && r.CurrentMedication == record.CurrentMedication)), Times.Once);
            //_unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

    }
}
