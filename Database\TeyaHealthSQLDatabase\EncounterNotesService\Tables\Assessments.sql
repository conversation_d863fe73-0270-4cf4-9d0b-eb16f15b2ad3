﻿CREATE TABLE [EncounterNotesService].[Assessments] (
    [AssessmentsID]    UNIQUEIDENTIFIER NOT NULL,
    [PatientId]        UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId]   UNIQUEIDENTIFIER NULL,
    [CreatedDate]      D<PERSON>ET<PERSON>E         NULL,
    [UpdatedDate]      D<PERSON>ETIME         NULL,
    [Diagnosis]        NVARCHAR (MAX)   NULL,
    [PCPId]            UNIQUEIDENTIFIER NULL,
    [IsActive]         BIT              NOT NULL,
    [ICDCode]          NVARCHAR (50)    NULL,
    [Specify]          NVARCHAR (MAX)   NULL,
    [Notes]            NVARCHAR (MAX)   NULL,
    [CheifComplaint]   NVARCHAR (MAX)   NULL,
    [CheifComplaintId] UNIQUEIDENTIFIER NULL,
    PRIMARY KEY CLUSTERED ([AssessmentsID] ASC, [PatientId] ASC),
    CONSTRAINT [FK_Assessments_ChiefComplaint] FOREIGN KEY ([CheifComplaintId]) REFERENCES [EncounterNotesService].[ChiefComplaint] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [UQ_Assessments_AssessmentsID] UNIQUE NONCLUSTERED ([AssessmentsID] ASC)
);

