﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ChiefComplaintQueryHandler : IChiefComplaintQueryHandler<ChiefComplaint>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;        
        public ChiefComplaintQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ChiefComplaint>> GetAllChiefComplaints()  
        {
            var getAll = await _unitOfWork.ChiefComplaintRepository.GetAllAsync();
            return getAll;
        }        
        public async Task<ChiefComplaint> GetChiefComplaintById(Guid id) 
        {
            var getById = await _unitOfWork.ChiefComplaintRepository.GetByIdAsync(id); 
            return getById;
        }
        public async Task<IEnumerable<ChiefComplaint>> GetChiefComplaintsByPatientId(Guid patientId)  
        {
           var getByPatientId = await _unitOfWork.ChiefComplaintRepository.GetByPatientIdAsync(patientId);
            return getByPatientId;
        }
    }
}
