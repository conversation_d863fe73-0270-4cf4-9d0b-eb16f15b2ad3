﻿using AppointmentContracts;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AppointmentDataAccessLayer.AppointmentImplementation
{
    public interface IGenericRepository<T> where T : class
    {
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> GetByUserIdAsync(Guid userId,Guid OrgID,bool Subscription);
        Task<IEnumerable<T>> GetByDateAsync(DateTime date, Guid OrgID,bool Subscription);
        Task<T> GetByIdAsync(bool Subscription, Guid userId,Guid OrgID);
        Task AddAsync(IEnumerable<Appointment> entities, Guid OrgId);
        Task UpdateAsync(Appointment entity, Guid userId);
        Task DeleteByIdAsync(bool Subscription, Guid userId, Guid OrgID);
    }
}
