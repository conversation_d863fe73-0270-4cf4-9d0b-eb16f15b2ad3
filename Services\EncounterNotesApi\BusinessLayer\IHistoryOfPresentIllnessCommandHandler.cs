﻿
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
 
namespace EncounterNotesBusinessLayer
{
    public interface IHistoryOfPresentIllnessCommandHandler<T>
    {
        Task AddHistoryOfPresentIllness(List<T> records);
        Task UpdateHistoryOfPresentIllness(T record);
        Task DeleteHistoryOfPresentIllnessById(Guid id);
        Task DeleteHistoryOfPresentIllnessByEntity(T record);
        Task UpdateHistoryOfPresentIllnessBulk(List<T> records);
    }
}