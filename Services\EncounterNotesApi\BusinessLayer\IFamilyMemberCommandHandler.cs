﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IFamilyMemberCommandHandler<TText>
    {
        Task DeleteFamilyMemberById(Guid id);

        Task<bool> UpdateFamilyMemberList(List<FamilyMember> FamilyMemberList);
        Task AddFamilyMember(List<TText> familyMemberList);
        Task UpdateFamilyMember(FamilyMember familyMember);
    }
}
