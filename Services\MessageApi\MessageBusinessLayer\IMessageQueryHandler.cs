using MessageContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IMessageQueryHandler<T>
    {
        Task<IEnumerable<T>> GetMessagesByEmail(string email);
        Task<IEnumerable<T>> GetConversation(string senderEmail, string receiverEmail);
        Task<T> GetMessageById(Guid messageId);
        Task<IEnumerable<T>> GetUnreadMessages(string email);
        Task<IEnumerable<T>> GetSentMessages(string email);
        Task<IEnumerable<T>> GetReceivedMessages(string email);
        Task<IEnumerable<T>> GetDeletedMessagesByEmail(string email);
    }
}
