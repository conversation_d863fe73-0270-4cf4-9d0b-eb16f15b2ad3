﻿using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Threading;
using System.Linq;
using static MudBlazor.Icons.Custom;
using Unity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;

namespace TeyaWebApp.Components.Pages
{
    public partial class SurgicalHistory
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public ISurgicalService _SurgicalService { get; set; }
        [Inject] private ILogger<SurgicalHistory> _logger { get; set; }
        [Inject] private IStringLocalizer<SurgicalHistory> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }

        [Inject] private ISnackbar Snackbar { get; set; }
        private MudDialog _surgicalhistory;
        private string _Surgery;

        private List<Surgical> surgicalhistory { get; set; }
        public string ICDName { get; set; }

        private SfRichTextEditor RichTextEditor;
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
        };

        public SfGrid<Surgical> SurgeryGrid { get; set; }
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientId { get; set; }
        private string editorContent;

        private List<Surgical> deletesurgerylist { get; set; } = new List<Surgical>();
        private List<Surgical> AddList = new();

        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = _PatientService.PatientData.Id;
            try
            {
                _icdCodes = await _ICDService.GetAllICDCodesAsync();
                surgicalhistory = await _SurgicalService.GetSurgeryByIdAsyncAndIsActive(PatientId);
                UpdateEditorContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving ICD codes");
            }
        }

        /// <summary>
        /// To show the data in Rich Text Editor
        /// </summary>
        private void UpdateEditorContent()
        {
            editorContent = string.Join("<p>", surgicalhistory
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $"<strong>{s.CreatedDate.ToString("dd-MM-yyyy")}</strong> - {s.Surgery}"));
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>
        private async Task OpenNewDialogBox()
        {
            await _surgicalhistory.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _surgicalhistory.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected async Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            var searchResults = _icdCodes
                .Where(icd => (icd.Code != null && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                              (icd.Description != null && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase)))
                .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                .ToList();

            cancellationToken.ThrowIfCancellationRequested();

            return searchResults;
        }

        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewSurgery()
        {
            if (DateTime.Now.Date < DateTime.Now.Date)
            {
                
                Snackbar.Add(_localizer["Date cannot be in the future"], Severity.Warning);
                return;
            }

            var newSurgery = new Surgical
            {
                SurgeryId = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = PatientId,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = PatientId,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Surgery = ICDName,
                IsActive = true,
            };

            AddList.Add(newSurgery);

            surgicalhistory.Add(newSurgery);
            await SurgeryGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private void ResetInputFields()
        {
            ICDName = string.Empty;
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<Surgical> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedSurgery = args.Data as Surgical;
                var existingItem = AddList.FirstOrDefault(v => v.SurgeryId == deletedSurgery.SurgeryId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deletesurgerylist.Add(args.Data);
                }
            }
        }

        /// <summary>
        /// To 
        /// </summary>
        /// <param name="args"></param>
        public void ActionBeginHandler(ActionEventArgs<Surgical> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.CreatedDate.Date > DateTime.Now.Date)
                {
                    Snackbar.Add(_localizer["Date cannot be in the future"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _SurgicalService.AddSurgeryAsync(AddList);
            }
            await _SurgicalService.UpdateSurgeryListAsync(deletesurgerylist);
            await _SurgicalService.UpdateSurgeryListAsync(surgicalhistory);
            deletesurgerylist.Clear();
            AddList.Clear();
            UpdateEditorContent();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deletesurgerylist.Clear();
            AddList.Clear();
            surgicalhistory = await _SurgicalService.GetSurgeryByIdAsyncAndIsActive(PatientId);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }
    }
}