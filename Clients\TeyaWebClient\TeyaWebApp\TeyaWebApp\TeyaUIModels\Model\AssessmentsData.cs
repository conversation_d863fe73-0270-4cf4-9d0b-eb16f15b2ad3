﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class AssessmentsData
    {
        public Guid AssessmentsID { get; set; }
        public Guid PatientId { get; set; }
        public Guid? OrganizationId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? Diagnosis { get; set; }
        public Guid? PCPId { get; set; }
        public string? ICDCode { get; set; }
        public string? Specify { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public string? CheifComplaint { get; set; }
        public Guid CheifComplaintId { get; set; }
    }
}
