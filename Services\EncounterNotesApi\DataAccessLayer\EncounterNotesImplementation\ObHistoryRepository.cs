﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ObHistoryRepository : GenericRepository<ObHistory>, IObHistoryRepository<ObHistory>
    {
        private readonly RecordDatabaseContext _context;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;

        public ObHistoryRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context,
                                                                                                                               localizer)
        {
            _context = context;
            _localizer = localizer;
        }

        public async Task AddAsync(IEnumerable<ObHistory> entities)
        {
            await _context.ObHistory.AddRangeAsync(entities);
            await _context.SaveChangesAsync();
        }



        public async Task<ObHistory> GetByIdAsync(Guid id)
        {
            return await _context.ObHistory.FirstOrDefaultAsync((System.Linq.Expressions.Expression<Func<ObHistory, bool>>)(ob => (bool)(ob.obId == id && !ob.IsDeleted)));
        }

        public async Task<IEnumerable<ObHistory>> GetByPatientIdAsync(Guid patientId)
        {
            return await _context.ObHistory.Where(ob => ob.PatientId == patientId && !ob.IsDeleted).ToListAsync();
        }



        public async Task UpdateAsync(ObHistory entity)
        {
            _context.ObHistory.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteByEntityAsync(ObHistory entity)
        {
            entity.IsDeleted = true;
            _context.ObHistory.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteByIdAsync(Guid obId)
        {
            var entity = await GetByIdAsync(obId);
            if (entity != null)
            {
                entity.IsDeleted = true;
                await UpdateAsync(entity);
            }
        }

        public async Task UpdateObHistoryListAsync(List<ObHistory> obHistoryList)
        {
            if (obHistoryList == null || obHistoryList.Count == 0)
                throw new ArgumentException("Invalid records provided.", nameof(obHistoryList));

            foreach (var obHistory in obHistoryList)
            {
                var existingObHistory = await _context.ObHistory
                    .FirstOrDefaultAsync((System.Linq.Expressions.Expression<Func<ObHistory, bool>>)(ob => ob.obId == obHistory.obId));

                if (existingObHistory != null)
                {
                    existingObHistory.Notes = obHistory.Notes;
                    existingObHistory.Symptoms = obHistory.Symptoms;
                    existingObHistory.OrganizationId = obHistory.OrganizationId;
                    existingObHistory.PcpId = obHistory.PcpId;
                    existingObHistory.DateOfComplaint = obHistory.DateOfComplaint;
                    existingObHistory.IsDeleted = obHistory.IsDeleted;
                    _context.ObHistory.Update((ObHistory)existingObHistory);
                }
            }
            await _context.SaveChangesAsync();
        }


    }
}
