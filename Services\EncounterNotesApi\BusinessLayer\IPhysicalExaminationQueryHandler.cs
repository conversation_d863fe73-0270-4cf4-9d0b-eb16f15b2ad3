﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IPhysicalExaminationQueryHandler<TText>
    {
        Task<IEnumerable<PhysicalExamination>> GetExaminationByIdAndIsActive(Guid id);
        Task<IEnumerable<PhysicalExamination>> GetAllExaminationsById(Guid id);
    }
}