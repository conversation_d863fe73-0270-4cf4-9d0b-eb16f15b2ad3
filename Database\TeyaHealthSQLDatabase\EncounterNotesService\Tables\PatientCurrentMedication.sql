﻿CREATE TABLE [EncounterNotesService].[PatientCurrentMedication] (
    [MedicineId]       UNIQUEIDENTIFIER NOT NULL,
    [PatientId]        UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId]   UNIQUEIDENTIFIER NOT NULL,
    [PCPId]            UNIQUEIDENTIFIER NOT NULL,
    [CreatedBy]        UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]        UNIQUEIDENTIFIER NULL,
    [CreatedDate]      DATETIME         NOT NULL,
    [UpdatedDate]      DATETIME         NULL,
    [BrandName]        NVARCHAR (100)   NULL,
    [DrugDetails]      NVARCHAR (255)   NULL,
    [Quantity]         NVARCHAR (50)    NULL,
    [Frequency]        NVARCHAR (50)    NULL,
    [StartDate]        DATETIME         NULL,
    [EndDate]          DATETIME         NULL,
    [isActive]         BIT              NULL,
    [CheifComplaint]   NVARCHAR (MAX)   NULL,
    [CheifComplaintId] UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_PatientCurrentMedication] PRIMARY KEY CLUSTERED ([MedicineId] ASC, [PatientId] ASC),
    CONSTRAINT [FK_PatientCurrentMedication_ChiefComplaint] FOREIGN KEY ([CheifComplaintId]) REFERENCES [EncounterNotesService].[ChiefComplaint] ([Id]) ON DELETE CASCADE
);

