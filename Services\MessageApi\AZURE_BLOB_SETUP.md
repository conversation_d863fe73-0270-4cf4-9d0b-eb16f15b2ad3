# Azure Blob Storage Setup for MessageAPI Attachments

## Overview
The MessageAPI now uses **Azure Blob Storage exclusively** for storing email attachments instead of the database or local file system.

## Configuration Required

### Environment Variables (Recommended)
Set these environment variables:

```bash
AZURE_BLOB_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=your_storage_account;AccountKey=your_account_key;EndpointSuffix=core.windows.net
AZURE_BLOB_CONTAINER_NAME=message-attachments
```

### Alternative: appsettings.json
```json
{
  "ConnectionStrings": {
    "AzureBlobStorage": "DefaultEndpointsProtocol=https;AccountName=your_storage_account;AccountKey=your_account_key;EndpointSuffix=core.windows.net"
  },
  "AzureBlobStorage": {
    "ContainerName": "message-attachments"
  }
}
```

## Azure Storage Account Setup

1. **Create Storage Account:**
   - Go to Azure Portal
   - Create a new Storage Account
   - Choose "Standard" performance tier
   - Select "Locally-redundant storage (LRS)" for cost efficiency

2. **Get Connection String:**
   - Go to Storage Account → Access Keys
   - Copy the connection string from Key1 or Key2

3. **Container Creation:**
   - The container will be created automatically when the first attachment is uploaded
   - Container name: `message-attachments` (configurable)
   - Access level: Private (no public access)

## Features

### ✅ What's Implemented:
- **Upload attachments** to Azure Blob Storage
- **Download attachments** from Azure Blob Storage  
- **Delete attachments** from Azure Blob Storage
- **Automatic container creation**
- **Unique blob naming** with GUID prefixes
- **Content type preservation**
- **File metadata storage** in blob properties
- **Database metadata** with blob references

### ✅ Benefits:
- **Scalable storage** - No database size limits
- **Cost effective** - Pay only for storage used
- **High availability** - Azure's 99.9% uptime SLA
- **Security** - Private container access only
- **Performance** - Direct blob access for downloads

## Database Changes

The `MessageAttachment` table now stores:
- `BlobFileName` - Unique blob identifier
- `BlobStorageUrl` - Direct URL to blob
- `BlobContainerName` - Container name
- Legacy fields marked as `[Obsolete]`

## Migration Notes

- **New attachments** go directly to blob storage
- **Legacy attachments** (if any) still work via fallback
- **No data loss** during transition
- **Gradual migration** possible

## Testing

Use the existing attachment test endpoints:
- Upload via `/api/Message/send` with attachments
- Download via `/api/Message/attachment/{id}/download`
- All existing functionality preserved

## Troubleshooting

### Common Issues:
1. **Connection string invalid** - Check Azure portal for correct string
2. **Container access denied** - Verify storage account permissions
3. **Blob not found** - Check if upload completed successfully

### Logs to Check:
- `Successfully uploaded attachment to blob: {blobFileName}`
- `Successfully downloaded attachment from blob: {blobFileName}`
- `Failed to upload attachment to blob storage: {fileName}`
