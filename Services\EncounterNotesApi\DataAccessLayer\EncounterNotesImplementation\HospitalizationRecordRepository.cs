﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class HospitalizationRecordRepository : GenericRepository<HospitalizationRecord>, IHospitalizationRecordRepository
    {
        private readonly RecordDatabaseContext _context;

        public HospitalizationRecordRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<List<HospitalizationRecord>> GetHospitalizationRecordById(Guid recordId)
        {
            return await _context.HospitalizationRecords
                .Where(wt => wt.PatientId == recordId)
                .ToListAsync();
        }

    }
}