using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using MessageContracts;
using MessageBusinessLayer;
using MessageDataAccessLayer;
using System.Linq;

namespace MessageApi.Tests.BusinessLayer
{
    [TestFixture]
    public class MessageQueryHandlerTests
    {
        private Mock<IMessageRepository> _repositoryMock;
        private Mock<ILogger<MessageQueryHandler>> _loggerMock;
        private Mock<IStringLocalizer<MessageQueryHandler>> _localizerMock;
        private MessageQueryHandler _queryHandler;
        private List<Message> _testMessages;

        [SetUp]
        public void Setup()
        {
            _repositoryMock = new Mock<IMessageRepository>();
            _loggerMock = new Mock<ILogger<MessageQueryHandler>>();
            _localizerMock = new Mock<IStringLocalizer<MessageQueryHandler>>();

            _testMessages = new List<Message>
            {
                new Message 
                { 
                    MessageId = Guid.NewGuid(), 
                    SenderName = "<PERSON>",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 1",
                    MessageContent = "Test Content 1",
                    SentDateTime = DateTime.UtcNow,
                    IsRead = false,
                    IsDeleted = false,
                    IsArchived = false
                },
                new Message 
                { 
                    MessageId = Guid.NewGuid(), 
                    SenderName = "Jane Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "John Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 2",
                    MessageContent = "Test Content 2",
                    SentDateTime = DateTime.UtcNow.AddMinutes(-30),
                    IsRead = true,
                    IsDeleted = false,
                    IsArchived = false
                },
                new Message 
                { 
                    MessageId = Guid.NewGuid(), 
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Bob Smith",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 3",
                    MessageContent = "Test Content 3",
                    SentDateTime = DateTime.UtcNow.AddHours(-1),
                    IsRead = false,
                    IsDeleted = true,
                    IsArchived = false
                }
            };

            _queryHandler = new MessageQueryHandler(
                _repositoryMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetMessagesByEmail_ValidEmail_ReturnsMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedMessages = _testMessages.Where(m => 
                m.SenderEmailId == email || m.ReceiverEmailId == email).ToList();
            
            _repositoryMock.Setup(x => x.GetMessagesByEmailAsync(email))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetMessagesByEmail(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedMessages);
            _repositoryMock.Verify(x => x.GetMessagesByEmailAsync(email), Times.Once);
        }

        [Test]
        public async Task GetMessageById_ExistingId_ReturnsMessage()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            var expectedMessage = _testMessages[0];
            
            _repositoryMock.Setup(x => x.GetMessageByIdAsync(messageId))
                          .ReturnsAsync(expectedMessage);

            // Act
            var result = await _queryHandler.GetMessageById(messageId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedMessage);
            _repositoryMock.Verify(x => x.GetMessageByIdAsync(messageId), Times.Once);
        }

        [Test]
        public async Task GetMessageById_NonExistingId_ReturnsNull()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            
            _repositoryMock.Setup(x => x.GetMessageByIdAsync(messageId))
                          .ReturnsAsync((Message)null);

            // Act
            var result = await _queryHandler.GetMessageById(messageId);

            // Assert
            result.Should().BeNull();
            _repositoryMock.Verify(x => x.GetMessageByIdAsync(messageId), Times.Once);
        }

        [Test]
        public async Task GetUnreadMessages_ValidEmail_ReturnsUnreadMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedMessages = _testMessages.Where(m => 
                m.ReceiverEmailId == email && !m.IsRead && !m.IsDeleted).ToList();
            
            _repositoryMock.Setup(x => x.GetUnreadMessagesAsync(email))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetUnreadMessages(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedMessages);
            _repositoryMock.Verify(x => x.GetUnreadMessagesAsync(email), Times.Once);
        }

        [Test]
        public async Task GetSentMessages_ValidEmail_ReturnsSentMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedMessages = _testMessages.Where(m => 
                m.SenderEmailId == email && !m.IsDeleted).ToList();
            
            _repositoryMock.Setup(x => x.GetSentMessagesAsync(email))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetSentMessages(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedMessages);
            _repositoryMock.Verify(x => x.GetSentMessagesAsync(email), Times.Once);
        }

        [Test]
        public async Task GetReceivedMessages_ValidEmail_ReturnsReceivedMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedMessages = _testMessages.Where(m => 
                m.ReceiverEmailId == email && !m.IsDeleted).ToList();
            
            _repositoryMock.Setup(x => x.GetReceivedMessagesAsync(email))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetReceivedMessages(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedMessages);
            _repositoryMock.Verify(x => x.GetReceivedMessagesAsync(email), Times.Once);
        }

        [Test]
        public async Task GetConversation_ValidEmails_ReturnsConversationMessages()
        {
            // Arrange
            var senderEmail = "<EMAIL>";
            var receiverEmail = "<EMAIL>";
            var expectedMessages = _testMessages.Where(m => 
                (m.SenderEmailId == senderEmail && m.ReceiverEmailId == receiverEmail) ||
                (m.SenderEmailId == receiverEmail && m.ReceiverEmailId == senderEmail)).ToList();
            
            _repositoryMock.Setup(x => x.GetConversationAsync(senderEmail, receiverEmail))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetConversation(senderEmail, receiverEmail);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedMessages);
            _repositoryMock.Verify(x => x.GetConversationAsync(senderEmail, receiverEmail), Times.Once);
        }

        [Test]
        public async Task GetDeletedMessagesByEmail_ValidEmail_ReturnsDeletedMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedMessages = _testMessages.Where(m => 
                (m.SenderEmailId == email || m.ReceiverEmailId == email) && m.IsDeleted).ToList();
            
            _repositoryMock.Setup(x => x.GetDeletedMessagesByEmailAsync(email))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetDeletedMessagesByEmail(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedMessages);
            _repositoryMock.Verify(x => x.GetDeletedMessagesByEmailAsync(email), Times.Once);
        }

        [Test]
        public async Task GetMessagesByEmail_EmptyEmail_ReturnsEmptyList()
        {
            // Arrange
            var email = "";
            var expectedMessages = new List<Message>();
            
            _repositoryMock.Setup(x => x.GetMessagesByEmailAsync(email))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetMessagesByEmail(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
            _repositoryMock.Verify(x => x.GetMessagesByEmailAsync(email), Times.Once);
        }

        [Test]
        public async Task GetUnreadMessages_NoUnreadMessages_ReturnsEmptyList()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedMessages = new List<Message>();
            
            _repositoryMock.Setup(x => x.GetUnreadMessagesAsync(email))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetUnreadMessages(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
            _repositoryMock.Verify(x => x.GetUnreadMessagesAsync(email), Times.Once);
        }

        [Test]
        public async Task GetConversation_NoConversation_ReturnsEmptyList()
        {
            // Arrange
            var senderEmail = "<EMAIL>";
            var receiverEmail = "<EMAIL>";
            var expectedMessages = new List<Message>();
            
            _repositoryMock.Setup(x => x.GetConversationAsync(senderEmail, receiverEmail))
                          .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetConversation(senderEmail, receiverEmail);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
            _repositoryMock.Verify(x => x.GetConversationAsync(senderEmail, receiverEmail), Times.Once);
        }
    }
}
