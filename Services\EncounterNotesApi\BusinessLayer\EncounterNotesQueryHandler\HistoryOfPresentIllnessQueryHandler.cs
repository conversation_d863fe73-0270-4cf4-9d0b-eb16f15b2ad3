﻿
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
 
namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class HistoryOfPresentIllnessQueryHandler : IHistoryOfPresentIllnessQueryHandler<HistoryOfPresentIllness>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public HistoryOfPresentIllnessQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

     
        public async Task<IEnumerable<HistoryOfPresentIllness>> GetHistoryOfPresentIllnessByPatientId(Guid patientId)
        {
            var hpiRecords = await _unitOfWork.HistoryOfPresentIllnessRepository.GetAsync();
            return hpiRecords.Where(hpi => hpi.PatientId == patientId);
        }

        public async Task<IEnumerable<HistoryOfPresentIllness>> GetHistoryOfPresentIllnessByPatientIdAndActive(Guid patientId)
        {
            var hpiRecords = await _unitOfWork.HistoryOfPresentIllnessRepository.GetAsync();
            return hpiRecords.Where(hpi => hpi.PatientId == patientId && hpi.IsActive == true);


        }
     

    }
}
