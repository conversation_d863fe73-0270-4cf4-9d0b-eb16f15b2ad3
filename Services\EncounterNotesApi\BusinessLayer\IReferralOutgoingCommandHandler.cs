﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IReferralOutgoingCommandHandler<TText>
    {
        Task DeleteReferralOutgoingByEntity(PatientReferralOutgoing ReferralOutgoing);
        Task DeleteReferralOutgoingById(Guid id);
        Task AddReferralOutgoing(List<TText> texts);
        Task UpdateReferralOutgoing(PatientReferralOutgoing ReferralOutgoing);
        Task UpdateReferralOutgoingList(List<PatientReferralOutgoing> PatientReferralOutgoing);
    }
}
