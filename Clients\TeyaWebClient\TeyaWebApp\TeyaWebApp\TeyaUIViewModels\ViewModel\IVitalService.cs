﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IVitalService
    {
        Task<List<PatientVitals>> GetVitalsByIdAsync(Guid id);
        Task<List<PatientVitals>> GetVitalsByIdAsyncAndIsActive(Guid id);
        Task AddVitalAsync(List<PatientVitals> vitals);
        Task DeleteVitalAsync(PatientVitals vital);
        Task UpdateVitalAsync(PatientVitals vital);
        Task UpdateVitalsListAsync(List<PatientVitals> vitals);
    }
}
