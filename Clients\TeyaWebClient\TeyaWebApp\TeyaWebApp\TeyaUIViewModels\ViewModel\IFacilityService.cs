﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IFacilityService
    {
        Task<Facility> RegisterFacilityAsync(Facility facility);
        Task<Facility> GetFacilityByIdAsync(Guid facilityId);
        Task<List<Facility>> GetAllFacilitiesAsync();
        Task<List<string>> GetFacilityNamesAsync();
        Task DeleteFacilityByIdAsync(Guid facilityId);
        Task UpdateFacilityByIdAsync(Guid facilityId, Facility facility);
        Task<List<Facility>> GetAllFacilitiesByOrgIdAsync(Guid? ID);
    }
}
