{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=MessageApiDB;Trusted_Connection=true;TrustServerCertificate=true;", "AzureCommunicationServices": "endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "your-domain.onmicrosoft.com", "TenantId": "your-tenant-id", "ClientId": "your-client-id", "Authority": "https://login.microsoftonline.com/your-tenant-id", "Scopes": "access_as_user"}, "SwaggerAzureAd": {"ClientId": "your-client-id", "AuthorizationUrl": "https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/authorize", "TokenUrl": "https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/token", "Scope": "api://your-client-id/access_as_user"}}