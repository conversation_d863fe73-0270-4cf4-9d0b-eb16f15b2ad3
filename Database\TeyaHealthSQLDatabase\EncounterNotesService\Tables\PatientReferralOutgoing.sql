﻿CREATE TABLE [EncounterNotesService].[PatientReferralOutgoing] (
    [PlanReferralId] UNIQUEIDENTIFIER NOT NULL,
    [PatientId]      UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId] UNIQUEIDENTIFIER NOT NULL,
    [PCPId]          UNIQUEIDENTIFIER NOT NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER NULL,
    [CreatedDate]    DATETIME         NOT NULL,
    [UpdatedDate]    DATETIME         NULL,
    [TreatmentPlan]  NVARCHAR (MAX)   NULL,
    [ReferralReason] NVARCHAR (MAX)   NULL,
    [isActive]       BIT              NULL,
    [Referral<PERSON>rom]   NVARCHAR (255)   NULL,
    [ReferralTo]     NVARCHAR (255)   NULL,
    [Tests]          NVARCHAR (255)   NULL,
    CONSTRAINT [PK_PatientReferralOutgoing] PRIMARY KEY CLUSTERED ([PlanReferralId] ASC, [PatientId] ASC)
);

