﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IPrescriptionMedicationCommandHandler<TText>
    {
        Task DeleteMedicationByEntity(PrescriptionMedication Pmedication);
        Task DeleteMedicationById(Guid id);
        Task AddMedication(List<TText> texts);
        Task UpdateMedication(PrescriptionMedication Pmedication);
        Task UpdateMedicationList(List<PrescriptionMedication> PMedications);
    }
}
