﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IReviewOfSystemCommandHandler<TText>
    {
        Task DeleteReviewOfSystemByEntity(ReviewOfSystem reviewOfSystem);
        Task DeleteReviewOfSystemById(Guid id);
        Task AddReviewOfSystem(List<TText> texts);
        Task UpdateReviewOfSystem(ReviewOfSystem reviewOfSystem);
        Task UpdateReviewOfSystemList(List<ReviewOfSystem> reviewOfSystems);
    }
}
