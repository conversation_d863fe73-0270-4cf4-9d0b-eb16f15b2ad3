﻿
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaWebApp;

namespace TeyaUIViewModels.ViewModel
{
    public interface IObHistoryService
    {
        Task<List<ObHistoryDTO>> GetAllObHistoriesAsync();
        Task AddAsync(ObHistoryDTO obHistoryDto);
        Task UpdateObHistoryAsync(Guid id, ObHistoryDTO obHistoryDto);
        Task DeleteObHistoryAsync(Guid id);

        Task UpdateObHistoryListAsync(List<ObHistoryDTO> obHistories);
        Task<List<ObHistoryDTO>> LoadObHistoriesAsync(Guid patientId);
        Task<IEnumerable<ObHistoryDTO>> GetByPatientIdAsync(Guid patientId);





    }
}
