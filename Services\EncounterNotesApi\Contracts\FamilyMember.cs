﻿using System;
using System.Text.Json.Serialization;

namespace EncounterNotesContracts
{
    public class FamilyMember : IContract
    {
        public Guid RecordID { get; set; } 
        public Guid? OrganizationID { get; set; }
        public string Relation { get; set; }
        public string Status { get; set; }
        public DateTime? DOB { get; set; } 
        public int? Age { get; set; } 
        public string Notes { get; set; }
        public Guid PatientId { get; set; }
        public bool IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
