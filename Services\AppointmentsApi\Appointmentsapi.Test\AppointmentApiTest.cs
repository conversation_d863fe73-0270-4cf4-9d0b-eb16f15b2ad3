using Moq;
using NUnit.Framework;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Appointments.Controllers;
using AppointmentContracts;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Appointments.AppointmentResources;
using BusinessLayer;

namespace Appointments.Test
{
    [TestFixture]
    public class AppointmentsControllerTests
    {
        private Mock<IAppointmentQueryHandler<Appointment>> _mockAppointmentQueryHandler;
        private Mock<IAppointmentCommandHandler<Appointment>> _mockAppointmentCommandHandler;
        private Mock<ILogger<AppointmentsController>> _mockLogger;
        private Mock<IStringLocalizer<AppointmentStrings>> _mockLocalizer;
        private AppointmentsController _controller;

        [SetUp]
        public void SetUp()
        {
            // Mock dependencies
            _mockAppointmentQueryHandler = new Mock<IAppointmentQueryHandler<Appointment>>();
            _mockAppointmentCommandHandler = new Mock<IAppointmentCommandHandler<Appointment>>();
            _mockLogger = new Mock<ILogger<AppointmentsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<AppointmentStrings>>();

            // Setup localizer to return mock values
            _mockLocalizer.Setup(l => l["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error while fetching appointments"));
            _mockLocalizer.Setup(l => l["500"]).Returns(new LocalizedString("500", "500"));
            _mockLocalizer.Setup(l => l["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Appointment updated successfully"));
            _mockLocalizer.Setup(l => l["InvalidAppointment"]).Returns(new LocalizedString("InvalidAppointment", "Invalid appointment"));

            // Initialize controller with mocked dependencies
            _controller = new AppointmentsController(
                _mockAppointmentCommandHandler.Object,
                _mockAppointmentQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task Get_ShouldReturnOkResult_WhenAppointmentsExist()
        {
            // Arrange: Create mock data for appointments
            var appointments = new List<Appointment>
            {
                new Appointment
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),
                    FacilityId = Guid.NewGuid(),
                    OrganisationId = Guid.NewGuid(),
                    StartTime = DateTime.Now.AddHours(1),
                    EndTime = DateTime.Now.AddHours(2),
                    AppointmentDate = DateTime.Now.AddDays(1),
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    Subscription = true
                }
            };

            // Setup the mock handler to return the list of appointments
            _mockAppointmentQueryHandler.Setup(handler => handler.GetAppointment()).ReturnsAsync(appointments);

            // Act: Call the Get method
            var result = await _controller.Get();

            // Assert: Use Assert.That to verify the result is OkObjectResult
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "The result should be an OkObjectResult.");

            var okResult = result as OkObjectResult;

            // Ensure okResult is not null and check its Value
            Assert.That(okResult, Is.Not.Null, "OkObjectResult should not be null.");
            Assert.That(okResult.Value, Is.EqualTo(appointments), "The OkObjectResult should contain the correct appointments.");
        }

        [Test]
        public async Task Get_ShouldReturnServerError_WhenExceptionOccurs()
        {
            // Arrange: Simulate an exception during the appointment query
            _mockAppointmentQueryHandler.Setup(handler => handler.GetAppointment()).ThrowsAsync(new Exception("Test exception"));

            // Act: Call the Get method
            var result = await _controller.Get();

            // Assert: Check that the result is a StatusCodeResult with a 500 status code
            Assert.That(result, Is.InstanceOf<StatusCodeResult>(), "The result should be a StatusCodeResult.");

            var statusCodeResult = result as StatusCodeResult;

            // Ensure statusCodeResult is not null and check the status code
            Assert.That(statusCodeResult, Is.Not.Null, "StatusCodeResult should not be null.");
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500), "The status code should be 500.");
        }
        [Test]
        public async Task GetByDate_ShouldReturnOkResult_WhenAppointmentsExistForDate()
        {
            // Arrange: Create mock data for appointments on a specific date
            DateTime date = DateTime.Now.AddDays(1);
            var appointments = new List<Appointment>
            {
                new Appointment
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    ProviderId = Guid.NewGuid(),

                    FacilityId =  Guid.NewGuid(),

                    OrganisationId = Guid.NewGuid(),
                    StartTime = date.AddHours(1),
                    EndTime = date.AddHours(2),
                    AppointmentDate = date,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    Subscription = true
                }
            };

            // Setup the mock handler to return the list of appointments for the given date
            _mockAppointmentQueryHandler.Setup(handler => handler.GetAppointmentByDate(date, appointments[0].OrganisationId, appointments[0].Subscription)).ReturnsAsync(appointments);

            // Act: Call the GetByDate method
            var result = await _controller.GetByDate(date, appointments[0].OrganisationId, appointments[0].Subscription);

            // Assert: Check if the result is OkObjectResult
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "The result should be an OkObjectResult.");

            var okResult = result as OkObjectResult;

            // Ensure okResult is not null and check its Value
            Assert.That(okResult, Is.Not.Null, "OkObjectResult should not be null.");
            Assert.That(okResult.Value, Is.EqualTo(appointments), "The OkObjectResult should contain the correct appointments.");
        }
        [Test]
        public async Task GetById_ShouldReturnOkResult_WhenAppointmentExists()
        {
            // Arrange: Create mock data for a specific appointment
            Guid appointmentId = Guid.NewGuid();
            var appointment = new Appointment
            {
                Id = appointmentId,
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now.AddHours(1),
                EndTime = DateTime.Now.AddHours(2),
                AppointmentDate = DateTime.Now.AddDays(1),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Subscription = true
            };

            // Setup the mock handler to return the appointment by Id
            _mockAppointmentQueryHandler.Setup(handler => handler.GetAppointmentById(It.IsAny<bool>(), appointmentId,appointment.OrganisationId))
                .ReturnsAsync(appointment);

            // Act: Call the GetById method
            var result = await _controller.GetById(false, appointmentId,appointment.OrganisationId);

            // Assert: Check if the result is OkObjectResult
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "The result should be an OkObjectResult.");

            var okResult = result as OkObjectResult;

            // Ensure okResult is not null and check its Value
            Assert.That(okResult, Is.Not.Null, "OkObjectResult should not be null.");
            Assert.That(okResult.Value, Is.EqualTo(appointment), "The OkObjectResult should contain the correct appointment.");
        }
        [Test]
        public async Task UpdateById_ShouldReturnOkResult_WhenUpdateIsSuccessful()
        {
            // Arrange: Create a valid appointment to update
            var appointmentId = Guid.NewGuid();
            var appointment = new Appointment
            {
                Id = appointmentId,
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now.AddHours(1),
                EndTime = DateTime.Now.AddHours(2),
                AppointmentDate = DateTime.Now.AddDays(1),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Subscription = true
            };

            // Setup the mock handler to return successful update
            _mockAppointmentCommandHandler.Setup(handler => handler.UpdateAppointment(appointment, appointmentId))
                .Returns(Task.CompletedTask);  // Simulate successful update

            // Act: Call the UpdateById method
            var result = await _controller.UpdateById(appointmentId, appointment);

            // Assert: Check if the result is OkObjectResult
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "The result should be OkObjectResult.");

            var okResult = result as OkObjectResult;

            // Ensure that the OkObjectResult contains the success message
            Assert.That(okResult.Value, Is.EqualTo(_mockLocalizer.Object["UpdateSuccessful"]), "The OkObjectResult should contain the correct success message.");
        }
        [Test]
        public async Task DeleteById_ShouldReturnOkResult_WhenDeleteIsSuccessful()
        {
            // Arrange: Create an appointment ID to delete and a Subscription flag
            var appointmentId = Guid.NewGuid();
            bool subscription = true;  // Example value for Subscription flag

            // Setup the mock handler to simulate successful deletion
            _mockAppointmentCommandHandler.Setup(handler => handler.DeleteAppointmentById(subscription, appointmentId,appointmentId))
                .Returns(Task.CompletedTask);  // Simulate successful delete

            // Act: Call the DeleteById method
            var result = await _controller.DeleteById(subscription, appointmentId,appointmentId);

            // Assert: Check that the result is OkObjectResult
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "The result should be OkObjectResult.");

            var okResult = result as OkObjectResult;

            // Ensure that okResult is not null
            Assert.That(okResult, Is.Not.Null, "The OkObjectResult should not be null.");

            // Ensure that the OkObjectResult contains the correct success message
            Assert.That(okResult?.Value, Is.EqualTo(_mockLocalizer.Object["DeleteSuccessful"]), "The OkObjectResult should contain the correct success message.");
        }
        [Test]
        public async Task appointmentRecord_ShouldReturnOkResult_WhenAppointmentsAreSuccessfullyPosted()
        {
            // Arrange: Create mock data for appointments
            var appointmentrecords = new List<Appointment>
        {
            new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),

                FacilityId =  Guid.NewGuid(),

                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now.AddHours(1),
                EndTime = DateTime.Now.AddHours(2),
                AppointmentDate = DateTime.Now.AddDays(1),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Subscription = true
            }
        };

            // Setup the mock handler to simulate a successful AddAppointment operation
            _mockAppointmentCommandHandler.Setup(handler => handler.AddAppointment(It.IsAny<List<Appointment>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);  // Simulate successful addition of appointments

            // Act: Call the appointmentRecord method
            _mockLocalizer.Setup(l => l["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Appointments successfully registered"));

            // Act: Call the appointmentRecord method
            var result = await _controller.AppointmentRecord(appointmentrecords);

            // Assert: Check if the result is OkObjectResult
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "The result should be OkObjectResult.");

            var okResult = result as OkObjectResult;

            // Ensure that the OkObjectResult is not null
            Assert.That(okResult, Is.Not.Null, "OkObjectResult should not be null.");

            // Extract values from okResult
            var responseValue = okResult.Value as dynamic;

            // Assert that the OkObjectResult contains the correct success message and first appointment ID
            Assert.That(responseValue, Is.Not.Null, "The response value should not be null.");
        }
        [Test]
        public void AddShard_ShouldReturnOkResult_WhenShardIsSuccessfullyAdded()
        {
            // Arrange: Create a mock ShardRequest object
            var request = new ShardRequest
            {
                ServerName = "TestServer",
                DatabaseName = "TestDatabase",
                Key = Guid.NewGuid(),  // Using Key instead of ShardId as per the ShardRequest definition
                Subscription = 1       // Example subscription value
            };

            // Setup the mock AppointmentDataHandler to simulate a successful AddShard operation
            _mockAppointmentCommandHandler.Setup(handler => handler.AddShard(It.IsAny<ShardRequest>()))
                .Verifiable();  // Simulate the successful addition of the shard

            // Setup the mock Localizer to return a success message
            _mockLocalizer.Setup(l => l["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard has been successfully added"));

            // Act: Call the AddShard method on the controller
            IActionResult result = _controller.AddShard(request); // No need for await

            // Assert: Check that the result is OkObjectResult
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "The result should be OkObjectResult.");

            var okResult = result as OkObjectResult;

            // Ensure that okResult is not null
            Assert.That(okResult, Is.Not.Null, "OkObjectResult should not be null.");

            // Ensure that the OkObjectResult contains the correct success message
            Assert.That(okResult.Value, Is.EqualTo(_mockLocalizer.Object["ShardAdded"]), "The OkObjectResult should contain the correct success message.");
        }

    }
}