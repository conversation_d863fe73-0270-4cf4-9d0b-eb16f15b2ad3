﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    /// <summary>
    /// Repository implementation for handling member-related database operations.
    /// </summary>
    public class MemberRepository : GenericRepository<Member>, IMemberRepository
    {
        private readonly AccountDatabaseContext _context;

        /// <summary>
        /// Initializes a new instance of the <see cref="MemberRepository"/> class.
        /// </summary>
        /// <param name="context">Database context for member-related operations.</param>
        public MemberRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }

        /// <summary>
        /// Retrieves a list of existing emails from the database.
        /// </summary>
        /// <param name="emails">List of emails to check.</param>
        /// <returns>A list of emails that already exist in the database.</returns>
        public async Task<List<string>> GetExistingEmailsAsync(List<string> emails)
        {
            return await _context.Members
                .Where(member => emails.Contains(member.Email))
                .Select(member => member.Email)
                .ToListAsync();
        }

        /// <summary>
        /// Searches members by their username based on a search term.
        /// </summary>
        /// <param name="searchTerm">The search keyword for filtering members.</param>
        /// <returns>A collection of members that match the search term.</returns>
        public async Task<IEnumerable<Member>> SearchMembersAsync(string searchTerm)
        {
            return await _context.Members
                .Where(member => member.Email.ToLower().Contains(searchTerm.ToLower()))
                .ToListAsync();
        }

        /// <summary>
        /// Retrieves patient details for the specified list of patient IDs.
        /// </summary>
        /// <param name="patientIds">List of patient unique identifiers.</param>
        /// <returns>A collection of office visit members with matching patient IDs.</returns>
        public async Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds)
        {
            return await _context.Members
                .Where(member => patientIds.Contains(member.Id))
                .Select(member => new Office_visit_members
                {
                    PatientId = member.Id,
                    UserName = member.UserName,
                    DateOfBirth = member.DateOfBirth,
                    SexualOrientation = member.SexualOrientation
                })
                .ToListAsync();
        }

        /// <summary>
        /// Get ProviderPatients belonging to a specific Organization
        /// </summary>
        /// <param name="organizationId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid organizationId)
        {
            return await _context.Members
                         .Where(member => member.OrganizationID == organizationId 
                          && member.RoleName == "Provider")
                         .Select(member => new ProviderPatient
                         {
                             SSN = member.SSN,
                             Id = member.Id,
                             Username = member.UserName,
                             PCPId = member.PCPId
                         })
                         .ToListAsync();
        }

        /// <summary>
        /// Get Patients belonging to a specific Organization
        /// </summary>
        /// <param name="organizationId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid organizationId)
        {
            return await _context.Members
                         .Where(member => member.OrganizationID == organizationId
                          && member.RoleName == "Patient")
                         .Select(member => new ProviderPatient
                         {
                             SSN = member.SSN,
                             Id = member.Id,
                             Username = member.UserName,
                             PCPId = member.PCPId
                         })
                         .ToListAsync();
        }
    }
}
