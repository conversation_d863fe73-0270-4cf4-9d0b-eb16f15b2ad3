using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IPageRoleMappingRepository
    {
        Task AddAsync(PageRoleMapping pageRoleMapping);
        Task UpdateAsync(PageRoleMapping pageRoleMapping);
        Task DeleteByIdAsync(Guid id);
        Task<PageRoleMapping> GetByIdAsync(Guid id);
        Task<List<PageRoleMapping>> GetAllAsync();
        Task<List<PageRoleMapping>> GetByPagePathAsync(string pagePath);
        Task<List<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId);
    }
}
