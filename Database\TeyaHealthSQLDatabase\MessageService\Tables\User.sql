﻿CREATE TABLE [MessageService].[User] (
    [UserId]             UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [Name]               NVARCHAR (100)   NOT NULL,
    [EmailId]            NVARCHAR (255)   NOT NULL,
    [AcsUserId]          NVARCHAR (255)   NULL,
    [AcsAccessToken]     NVARCHAR (MAX)   NULL,
    [AcsTokenExpiryTime] DATETIME2 (7)    NULL,
    [CreatedDate]        DATETIME2 (7)    DEFAULT (getutcdate()) NOT NULL,
    [UpdatedDate]        DATETIME2 (7)    DEFAULT (getutcdate()) NOT NULL,
    [LastActiveDateTime] DATETIME2 (7)    NULL,
    [IsActive]           BIT              DEFAULT ((1)) NOT NULL,
    [PreferredLanguage]  NVARCHAR (50)    NULL,
    [TimeZone]           NVARCHAR (100)   NULL,
    PRIMARY KEY CLUSTERED ([UserId] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_Users_IsActive]
    ON [MessageService].[User]([IsActive] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_Users_CreatedDate]
    ON [MessageService].[User]([CreatedDate] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_Users_AcsUserId]
    ON [MessageService].[User]([AcsUserId] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_User_EmailId]
    ON [MessageService].[User]([EmailId] ASC);

