using System;

namespace MessageContracts
{
    public class User : IContract
    {
        public Guid UserId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string EmailId { get; set; } = string.Empty;
        public string? AcsUserId { get; set; }
        public string? AcsAccessToken { get; set; }
        public DateTime? AcsTokenExpiryTime { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;
        public DateTime? LastActiveDateTime { get; set; }
        public bool IsActive { get; set; } = true;
        public string? PreferredLanguage { get; set; }
        public string? TimeZone { get; set; }
    }
}
