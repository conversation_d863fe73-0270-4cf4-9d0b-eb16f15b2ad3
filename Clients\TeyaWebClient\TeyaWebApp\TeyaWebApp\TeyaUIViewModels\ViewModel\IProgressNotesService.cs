﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IProgressNotesService
    {
        Task<List<Record>> GetRecordsByPatientIdAsync(Guid id);
        Task<List<Record>> GetRecordsByPCPIdAsync(Guid pcpId);
        Task<HttpResponseMessage> SaveRecordAsync(Record updatedRecord);
        Task<HttpResponseMessage> UploadRecordAsync(Record updatedRecord);
    }
}
