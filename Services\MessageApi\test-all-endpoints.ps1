# COMPREHENSIVE REAL ENDPOINT TESTING
# Testing ALL MessageAPI endpoints with real inputs and actual execution

Write-Host "🧪 COMPREHENSIVE REAL ENDPOINT TESTING" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host "Testing ALL endpoints with REAL inputs - NO MOCKING!" -ForegroundColor Yellow
Write-Host ""

$baseUrl = "http://localhost:5000"
$testResults = @()

# Test data
$senderEmail = "<EMAIL>"
$receiverEmail = "<EMAIL>"
$senderName = "Dr. <PERSON>"
$receiverName = "Dr. <PERSON>"

# Create test attachment file
$attachmentContent = @"
REAL ENDPOINT TEST - MEDICAL CONSULTATION REPORT
===============================================

Patient: <PERSON>
DOB: 1990-05-15
Patient ID: P67890
Date: $(Get-Date -Format "yyyy-MM-dd")

Chief Complaint:
Patient reports severe headache and nausea for 2 days.

Physical Examination:
- Temperature: 101.5°F
- Blood Pressure: 140/90 mmHg
- Heart Rate: 95 bpm
- Neurological: Alert and oriented

Assessment:
Possible migraine with associated symptoms.
Recommend CT scan to rule out other causes.

Treatment Plan:
1. Sumatriptan 50mg for acute migraine relief
2. CT scan of head
3. Follow-up in 48 hours

Physician: Dr. Sarah Johnson, MD
Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

---
This is a REAL endpoint test file for MessageAPI.
File created at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Test Purpose: Comprehensive endpoint testing with real Azure Blob Storage
"@

$attachmentFile = "real-endpoint-test-report.txt"
$attachmentContent | Out-File -FilePath $attachmentFile -Encoding UTF8

$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($attachmentContent)
$base64Content = [Convert]::ToBase64String($fileBytes)
$fileSize = $fileBytes.Length

Write-Host "📎 Created test attachment: $attachmentFile ($fileSize bytes)" -ForegroundColor Green
Write-Host ""

# Function to test endpoint
function Test-Endpoint {
    param($Name, $Method, $Url, $Body = $null, $ExpectedStatus = 200)

    Write-Host "🔍 TESTING: $Name" -ForegroundColor Yellow
    Write-Host "   Method: $Method" -ForegroundColor Gray
    Write-Host "   URL: $Url" -ForegroundColor Gray

    try {
        $headers = @{ "Content-Type" = "application/json" }

        if ($Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -Headers $headers
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }

        Write-Host "   ✅ SUCCESS: $Name" -ForegroundColor Green
        return @{ Success = $true; Response = $response; Error = $null }
    }
    catch {
        Write-Host "   ❌ FAILED: $Name - $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false; Response = $null; Error = $_.Exception.Message }
    }
}

Write-Host "🚀 STARTING COMPREHENSIVE ENDPOINT TESTING..." -ForegroundColor Cyan
Write-Host ""

# TEST 1: Send Message with Attachment (POST /api/message/send)
Write-Host "📧 TEST 1: SEND MESSAGE WITH ATTACHMENT" -ForegroundColor Magenta
$sendMessageRequest = @{
    senderName = $senderName
    senderEmailId = $senderEmail
    receiverName = $receiverName
    receiverEmailId = $receiverEmail
    subject = "REAL ENDPOINT TEST - Urgent Medical Consultation"
    messageContent = @"
Dear Dr. Brown,

This is a REAL endpoint test for the MessageAPI system.

I'm sending you an urgent medical consultation report for patient Jane Doe (P67890).
The patient presented with severe headache and nausea. Please review the attached
detailed report and provide your neurological assessment.

Key findings:
- Temperature: 101.5°F
- Blood Pressure: 140/90 mmHg
- Neurological symptoms present

I've recommended a CT scan and would appreciate your input on the treatment plan.

Please respond as soon as possible.

Best regards,
Dr. Sarah Johnson, MD
Internal Medicine Department
TeyaHealth Medical Center

---
This message was sent via REAL endpoint testing at $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Testing Azure Blob Storage integration with real file upload.
"@
    attachments = @(
        @{
            fileName = $attachmentFile
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSize
        }
    )
} | ConvertTo-Json -Depth 10

$sendResult = Test-Endpoint "Send Message with Attachment" "POST" "$baseUrl/api/message/send" $sendMessageRequest
$testResults += $sendResult

if ($sendResult.Success) {
    $messageId = $sendResult.Response.messageId
    Write-Host "   📨 Message ID: $messageId" -ForegroundColor Cyan
    Write-Host "   📎 Attachments Processed: $($sendResult.Response.attachmentsProcessed)" -ForegroundColor Cyan
} else {
    Write-Host "   ⚠️  Cannot continue with other tests - Send Message failed" -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# TEST 2: Get Message Attachments (GET /api/message/{messageId}/attachments)
Write-Host "📎 TEST 2: GET MESSAGE ATTACHMENTS" -ForegroundColor Magenta
$getAttachmentsResult = Test-Endpoint "Get Message Attachments" "GET" "$baseUrl/api/message/$messageId/attachments"
$testResults += $getAttachmentsResult

if ($getAttachmentsResult.Success) {
    $attachments = $getAttachmentsResult.Response
    Write-Host "   📊 Found $($attachments.Count) attachment(s)" -ForegroundColor Cyan

    if ($attachments.Count -gt 0) {
        $attachment = $attachments[0]
        $attachmentId = $attachment.attachmentId
        Write-Host "   📎 Attachment Details:" -ForegroundColor Gray
        Write-Host "      - ID: $($attachment.attachmentId)" -ForegroundColor Gray
        Write-Host "      - Original Name: $($attachment.originalFileName)" -ForegroundColor Gray
        Write-Host "      - Blob Name: $($attachment.blobFileName)" -ForegroundColor Gray
        Write-Host "      - Size: $($attachment.fileSizeBytes) bytes" -ForegroundColor Gray
        Write-Host "      - Azure URL: $($attachment.blobStorageUrl)" -ForegroundColor Yellow
        Write-Host "      - Container: $($attachment.blobContainerName)" -ForegroundColor Yellow

        # Verify real Azure URL
        if ($attachment.blobStorageUrl -like "*teyarecordingsdev*") {
            Write-Host "      ✅ VERIFIED: Real Azure Blob Storage URL!" -ForegroundColor Green
        } else {
            Write-Host "      ❌ WARNING: Not using real Azure storage!" -ForegroundColor Red
        }
    }
}

Write-Host ""

# TEST 3: Download Attachment (GET /api/message/attachment/{attachmentId}/download)
Write-Host "⬇️ TEST 3: DOWNLOAD ATTACHMENT" -ForegroundColor Magenta
if ($getAttachmentsResult.Success -and $attachments.Count -gt 0) {
    $downloadResult = Test-Endpoint "Download Attachment" "GET" "$baseUrl/api/message/attachment/$attachmentId/download"
    $testResults += $downloadResult

    if ($downloadResult.Success) {
        $downloadedFile = "downloaded-endpoint-test-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
        $downloadResult.Response | Out-File -FilePath $downloadedFile -Encoding UTF8
        Write-Host "   💾 Downloaded to: $downloadedFile" -ForegroundColor Cyan

        # Verify content
        $originalSize = $fileSize
        $downloadedSize = (Get-Item $downloadedFile).Length
        Write-Host "   📊 Original size: $originalSize bytes" -ForegroundColor Gray
        Write-Host "   📊 Downloaded size: $downloadedSize bytes" -ForegroundColor Gray

        if ($downloadedSize -gt 0) {
            Write-Host "   ✅ VERIFIED: File downloaded successfully!" -ForegroundColor Green
        } else {
            Write-Host "   ❌ WARNING: Downloaded file is empty!" -ForegroundColor Red
        }
    }
} else {
    Write-Host "   ⚠️  Skipping download test - no attachments found" -ForegroundColor Yellow
}

Write-Host ""

# TEST 4: Send Another Message (for inbox/sent testing)
Write-Host "📧 TEST 4: SEND SECOND MESSAGE (for inbox/sent testing)" -ForegroundColor Magenta
$sendMessage2Request = @{
    senderName = $receiverName
    senderEmailId = $receiverEmail
    receiverName = $senderName
    receiverEmailId = $senderEmail
    subject = "RE: REAL ENDPOINT TEST - Medical Consultation Response"
    messageContent = @"
Dear Dr. Johnson,

Thank you for the consultation request regarding patient Jane Doe (P67890).

I've reviewed the case details and agree with your assessment. The symptoms
are consistent with migraine, but given the severity and associated fever,
I recommend:

1. Immediate CT scan to rule out secondary causes
2. Blood work including CBC and inflammatory markers
3. Consider lumbar puncture if CT is normal
4. Start on IV hydration and pain management

I'm available for immediate consultation if needed.

Best regards,
Dr. Michael Brown, MD
Neurology Department
TeyaHealth Medical Center

---
This is a response message for REAL endpoint testing at $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
    attachments = @()
} | ConvertTo-Json -Depth 10

$sendResult2 = Test-Endpoint "Send Second Message" "POST" "$baseUrl/api/message/send" $sendMessage2Request
$testResults += $sendResult2

if ($sendResult2.Success) {
    $messageId2 = $sendResult2.Response.messageId
    Write-Host "   📨 Second Message ID: $messageId2" -ForegroundColor Cyan
}

Write-Host ""

# TEST 5: Get Inbox Messages (GET /api/message/inbox/{userEmail})
Write-Host "📥 TEST 5: GET INBOX MESSAGES" -ForegroundColor Magenta
$getInboxResult = Test-Endpoint "Get Inbox Messages" "GET" "$baseUrl/api/message/inbox/$senderEmail"
$testResults += $getInboxResult

if ($getInboxResult.Success) {
    $inboxMessages = $getInboxResult.Response
    Write-Host "   📊 Found $($inboxMessages.Count) inbox message(s)" -ForegroundColor Cyan

    foreach ($msg in $inboxMessages) {
        Write-Host "   📧 Inbox Message:" -ForegroundColor Gray
        Write-Host "      - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "      - From: $($msg.senderName) <$($msg.senderEmailId)>" -ForegroundColor Gray
        Write-Host "      - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "      - Has Attachments: $($msg.hasAttachments)" -ForegroundColor Gray
        Write-Host "      - Sent: $($msg.sentDateTime)" -ForegroundColor Gray
    }
}

Write-Host ""

# TEST 6: Get Sent Messages (GET /api/message/sent/{userEmail})
Write-Host "📤 TEST 6: GET SENT MESSAGES" -ForegroundColor Magenta
$getSentResult = Test-Endpoint "Get Sent Messages" "GET" "$baseUrl/api/message/sent/$senderEmail"
$testResults += $getSentResult

if ($getSentResult.Success) {
    $sentMessages = $getSentResult.Response
    Write-Host "   📊 Found $($sentMessages.Count) sent message(s)" -ForegroundColor Cyan

    foreach ($msg in $sentMessages) {
        Write-Host "   📧 Sent Message:" -ForegroundColor Gray
        Write-Host "      - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "      - To: $($msg.receiverName) <$($msg.receiverEmailId)>" -ForegroundColor Gray
        Write-Host "      - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "      - Has Attachments: $($msg.hasAttachments)" -ForegroundColor Gray
        Write-Host "      - Sent: $($msg.sentDateTime)" -ForegroundColor Gray
    }
}

Write-Host ""

# TEST 7: Get Message by ID (GET /api/message/{messageId})
Write-Host "📄 TEST 7: GET MESSAGE BY ID" -ForegroundColor Magenta
$getMessageResult = Test-Endpoint "Get Message by ID" "GET" "$baseUrl/api/message/$messageId"
$testResults += $getMessageResult

if ($getMessageResult.Success) {
    $message = $getMessageResult.Response
    Write-Host "   📧 Message Details:" -ForegroundColor Cyan
    Write-Host "      - ID: $($message.messageId)" -ForegroundColor Gray
    Write-Host "      - From: $($message.senderName) <$($message.senderEmailId)>" -ForegroundColor Gray
    Write-Host "      - To: $($message.receiverName) <$($message.receiverEmailId)>" -ForegroundColor Gray
    Write-Host "      - Subject: $($message.subject)" -ForegroundColor Gray
    Write-Host "      - Content Length: $($message.messageContent.Length) characters" -ForegroundColor Gray
    Write-Host "      - Has Attachments: $($message.hasAttachments)" -ForegroundColor Gray
    Write-Host "      - Attachment Count: $($message.attachmentCount)" -ForegroundColor Gray
    Write-Host "      - Status: $($message.status)" -ForegroundColor Gray
    Write-Host "      - Sent: $($message.sentDateTime)" -ForegroundColor Gray
}

Write-Host ""

# TEST 8: Mark Message as Read (PUT /api/message/{messageId}/read)
Write-Host "👁️ TEST 8: MARK MESSAGE AS READ" -ForegroundColor Magenta
$markReadResult = Test-Endpoint "Mark Message as Read" "PUT" "$baseUrl/api/message/$messageId/read"
$testResults += $markReadResult

if ($markReadResult.Success) {
    Write-Host "   ✅ Message marked as read successfully" -ForegroundColor Green
}

Write-Host ""

# TEST 9: Mark Message as Important (PUT /api/message/{messageId}/important)
Write-Host "⭐ TEST 9: MARK MESSAGE AS IMPORTANT" -ForegroundColor Magenta
$markImportantResult = Test-Endpoint "Mark Message as Important" "PUT" "$baseUrl/api/message/$messageId/important"
$testResults += $markImportantResult

if ($markImportantResult.Success) {
    Write-Host "   ⭐ Message marked as important successfully" -ForegroundColor Green
}

Write-Host ""

# TEST 10: Archive Message (PUT /api/message/{messageId}/archive)
Write-Host "📦 TEST 10: ARCHIVE MESSAGE" -ForegroundColor Magenta
$archiveMessageRequest = @{
    archivedBy = $senderEmail
} | ConvertTo-Json

$archiveResult = Test-Endpoint "Archive Message" "PUT" "$baseUrl/api/message/$messageId/archive" $archiveMessageRequest
$testResults += $archiveResult

if ($archiveResult.Success) {
    Write-Host "   📦 Message archived successfully" -ForegroundColor Green
}

Write-Host ""

# TEST 11: Get Archived Messages (GET /api/message/archived/{userEmail})
Write-Host "📦 TEST 11: GET ARCHIVED MESSAGES" -ForegroundColor Magenta
$getArchivedResult = Test-Endpoint "Get Archived Messages" "GET" "$baseUrl/api/message/archived/$senderEmail"
$testResults += $getArchivedResult

if ($getArchivedResult.Success) {
    $archivedMessages = $getArchivedResult.Response
    Write-Host "   📊 Found $($archivedMessages.Count) archived message(s)" -ForegroundColor Cyan

    foreach ($msg in $archivedMessages) {
        Write-Host "   📦 Archived Message:" -ForegroundColor Gray
        Write-Host "      - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "      - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "      - Archived By: $($msg.archivedBy)" -ForegroundColor Gray
        Write-Host "      - Archived Date: $($msg.archivedDateTime)" -ForegroundColor Gray
    }
}

Write-Host ""

# TEST 12: Unarchive Message (PUT /api/message/{messageId}/unarchive)
Write-Host "📤 TEST 12: UNARCHIVE MESSAGE" -ForegroundColor Magenta
$unarchiveResult = Test-Endpoint "Unarchive Message" "PUT" "$baseUrl/api/message/$messageId/unarchive"
$testResults += $unarchiveResult

if ($unarchiveResult.Success) {
    Write-Host "   📤 Message unarchived successfully" -ForegroundColor Green
}

Write-Host ""

# TEST 13: Delete Attachment (DELETE /api/message/attachment/{attachmentId})
Write-Host "🗑️ TEST 13: DELETE ATTACHMENT" -ForegroundColor Magenta
if ($getAttachmentsResult.Success -and $attachments.Count -gt 0) {
    $deleteAttachmentResult = Test-Endpoint "Delete Attachment" "DELETE" "$baseUrl/api/message/attachment/$attachmentId"
    $testResults += $deleteAttachmentResult

    if ($deleteAttachmentResult.Success) {
        Write-Host "   🗑️ Attachment deleted successfully from Azure Blob Storage" -ForegroundColor Green

        # Verify deletion by trying to get attachments again
        $verifyDeleteResult = Test-Endpoint "Verify Attachment Deletion" "GET" "$baseUrl/api/message/$messageId/attachments"
        if ($verifyDeleteResult.Success) {
            $remainingAttachments = $verifyDeleteResult.Response
            Write-Host "   📊 Remaining attachments: $($remainingAttachments.Count)" -ForegroundColor Cyan
        }
    }
} else {
    Write-Host "   ⚠️  Skipping delete test - no attachments found" -ForegroundColor Yellow
}

Write-Host ""

# TEST 14: Soft Delete Message (DELETE /api/message/{messageId})
Write-Host "🗑️ TEST 14: SOFT DELETE MESSAGE" -ForegroundColor Magenta
$deleteMessageRequest = @{
    deletedBy = $senderEmail
} | ConvertTo-Json

$deleteMessageResult = Test-Endpoint "Soft Delete Message" "DELETE" "$baseUrl/api/message/$messageId" $deleteMessageRequest
$testResults += $deleteMessageResult

if ($deleteMessageResult.Success) {
    Write-Host "   🗑️ Message soft deleted successfully" -ForegroundColor Green
}

Write-Host ""

# TEST 15: Get Deleted Messages (GET /api/message/deleted/{userEmail})
Write-Host "🗑️ TEST 15: GET DELETED MESSAGES" -ForegroundColor Magenta
$getDeletedResult = Test-Endpoint "Get Deleted Messages" "GET" "$baseUrl/api/message/deleted/$senderEmail"
$testResults += $getDeletedResult

if ($getDeletedResult.Success) {
    $deletedMessages = $getDeletedResult.Response
    Write-Host "   📊 Found $($deletedMessages.Count) deleted message(s)" -ForegroundColor Cyan

    foreach ($msg in $deletedMessages) {
        Write-Host "   🗑️ Deleted Message:" -ForegroundColor Gray
        Write-Host "      - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "      - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "      - Deleted By: $($msg.deletedBy)" -ForegroundColor Gray
        Write-Host "      - Deleted Date: $($msg.deletedDateTime)" -ForegroundColor Gray
    }
}

Write-Host ""

# FINAL RESULTS SUMMARY
Write-Host "📊 COMPREHENSIVE ENDPOINT TESTING RESULTS" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

$successCount = ($testResults | Where-Object { $_.Success }).Count
$totalCount = $testResults.Count
$failureCount = $totalCount - $successCount

Write-Host ""
Write-Host "📈 SUMMARY:" -ForegroundColor Yellow
Write-Host "   Total Tests: $totalCount" -ForegroundColor Gray
Write-Host "   Successful: $successCount" -ForegroundColor Green
Write-Host "   Failed: $failureCount" -ForegroundColor Red
Write-Host "   Success Rate: $([math]::Round(($successCount / $totalCount) * 100, 2))%" -ForegroundColor Cyan

Write-Host ""
if ($failureCount -eq 0) {
    Write-Host "🎉 ALL ENDPOINTS TESTED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "✅ MessageAPI is fully functional with real Azure Blob Storage!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some endpoints failed. Check the details above." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🏁 Comprehensive endpoint testing completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
