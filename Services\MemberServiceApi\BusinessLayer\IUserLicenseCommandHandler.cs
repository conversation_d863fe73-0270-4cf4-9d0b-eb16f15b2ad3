﻿using System;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IUserLicenseCommandHandler<T>
    {
        Task AddUserLicenseAsync(T license);
        Task UpdateUserLicenseAsync(T license);
        Task DeleteUserLicenseAsync(Guid id);
        Task IncrementActiveUserCountAsync(Guid organizationId);
        Task ResetActiveUsersAsync(Guid organizationId);
        Task SetLicenseStatusAsync(Guid organizationId, bool status);
        Task<bool> CheckLicenseExpiryAsync(Guid organizationId);
    }
}
