var BlazorAudioRecorder = {};
(function () {
    let mediaStream;
    let audioContext;
    let processor;
    let caller;
    let mMediaRecorder;
    let mAudioChunks = [];

    BlazorAudioRecorder.Initialize = function (vCaller) {
        caller = vCaller;
    };
    BlazorAudioRecorder.StartRecord = async function () {
        try {
            mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false
                }
            });

            audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000
            });

            processor = audioContext.createScriptProcessor(2048, 1, 1);

            processor.onaudioprocess = function (e) {
                if (!caller) return;

                const audioData = e.inputBuffer.getChannelData(0);
                const pcmData = floatTo16BitPCM(audioData);

                // Convert to base64 string for Blazor interop
                const base64String = arrayBufferToBase64(pcmData.buffer);
                caller.invokeMethodAsync('ProcessAudioChunk', base64String);
            };

            const source = audioContext.createMediaStreamSource(mediaStream);
            source.connect(processor);
            processor.connect(audioContext.destination);

            // Create MediaRecorder from the same mediaStream
            mMediaRecorder = new MediaRecorder(mediaStream);
            mAudioChunks = [];

            mMediaRecorder.addEventListener('dataavailable', vEvent => {
                mAudioChunks.push(vEvent.data);
            });

            mMediaRecorder.addEventListener('error', vError => {
                console.warn('media recorder error: ' + vError);
            });

            mMediaRecorder.addEventListener('stop', () => {
                console.log('Recording stopped');
            });

            mMediaRecorder.start();

        } catch (error) {
            console.error('Error starting recording:', error);
            if (caller) {
                caller.invokeMethodAsync('OnRecordingError', error.message);
            }
        }
    };

    function floatTo16BitPCM(input) {
        const output = new Int16Array(input.length);
        for (let i = 0; i < input.length; i++) {
            const s = Math.max(-1, Math.min(1, input[i]));
            output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        return output;
    }

    function arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }

    BlazorAudioRecorder.PauseRecord = function () {
        if (processor) {
            processor.disconnect();
        }
        if (mMediaRecorder && mMediaRecorder.state === "recording") {
            mMediaRecorder.pause();
        }
    };

    BlazorAudioRecorder.ResumeRecord = function () {
        if (processor && audioContext && mediaStream) {
            const source = audioContext.createMediaStreamSource(mediaStream);
            source.connect(processor);
            processor.connect(audioContext.destination);
        }
        if (mMediaRecorder && mMediaRecorder.state === "paused") {
            mMediaRecorder.resume();
        }
    };
    BlazorAudioRecorder.CancelRecord = async function () {
        try {
            if (processor) {
                processor.disconnect();
                processor = null;
            }

            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
            }

            if (audioContext) {
                await audioContext.close();
                audioContext = null;
            }

            // Just stop the media recorder without uploading
            if (mMediaRecorder && mMediaRecorder.state !== "inactive") {
                mMediaRecorder.stop();
                mAudioChunks = []; // Clear the chunks
            }
            return true;
        } catch (error) {
            console.error('Error canceling recording:', error);
            return false;
        }
    };
    BlazorAudioRecorder.StopRecord = async function (currentRecordingId, accessToken) {
        try {
            if (processor) {
                processor.disconnect();
                processor = null;
            }

            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
            }

            if (audioContext) {
                await audioContext.close();
                audioContext = null;
            }

            // Check if mMediaRecorder exists before calling stop
            if (mMediaRecorder && mMediaRecorder.state !== "inactive") {
                mMediaRecorder.stop();
                await new Promise(resolve => setTimeout(resolve, 100));

                console.log('Preparing to upload...');
                console.log(currentRecordingId);
                var audioBlob = new Blob(mAudioChunks, { type: "audio/mp3;" });
                const formData = new FormData();
                const filename = currentRecordingId;
                formData.append('audioFile', audioBlob, filename);
                const uploadUrl = window.AppSettings.uploadUrl;

                console.log('Sending request to server...');
                try {
                    const response = await fetch(uploadUrl, {
                        method: 'POST',
                        headers: {
                            'Accept': 'application/json',
                            'Authorization': `Bearer ${accessToken}`
                        },
                        body: formData
                    });
                    console.log('Response status:', response.status);

                    if (response.ok) {
                        const result = await response.json();
                        console.log('Upload successful, ID:', result.id);
                        if (caller) {
                            await caller.invokeMethodAsync('OnRecordingComplete', result.id);
                        }
                    } else {
                        console.error('Upload failed:', await response.text());
                    }
                } catch (error) {
                    console.error('Upload error:', error);
                }
            }
            return true;
        } catch (error) {
            console.error('Error stopping recording:', error);
            return false;
        }
    };
})();
