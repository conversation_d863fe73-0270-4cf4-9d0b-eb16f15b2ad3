﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IReferralOutgoingService
    {
        Task<List<PatientReferralOutgoing>> GetReferralOutgoingsByIdAsync(Guid id);
        Task<List<PatientReferralOutgoing>> GetReferralOutgoingsByIdAsyncAndIsActive(Guid id);
        Task AddReferralOutgoingAsync(List<PatientReferralOutgoing> ReferralOutgoings);
        Task DeleteReferralOutgoingAsync(PatientReferralOutgoing ReferralOutgoing);
        Task UpdateReferralOutgoingAsync(PatientReferralOutgoing ReferralOutgoing);
        Task UpdateReferralOutgoingsListAsync(List<PatientReferralOutgoing> ReferralOutgoings);
    }
}
