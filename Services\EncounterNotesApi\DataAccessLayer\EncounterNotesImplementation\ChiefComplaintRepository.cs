﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ChiefComplaintRepository : GenericRepository<ChiefComplaint>, IChiefComplaintRepository<ChiefComplaint>
    {
        private readonly RecordDatabaseContext _context;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;

        public ChiefComplaintRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
            _localizer = localizer;
        }
        public async Task AddAsync(IEnumerable<ChiefComplaint> entities)
        {
            await _context.ChiefComplaint.AddRangeAsync(entities);
            await _context.SaveChangesAsync();
        }
        public async Task<IEnumerable<ChiefComplaint>> GetAllAsync()
        {            
            return await _context.ChiefComplaint.ToListAsync();
        }
        public async Task<ChiefComplaint> GetByIdAsync(Guid id)
        {
            return await _context.ChiefComplaint.FirstOrDefaultAsync(cc => cc.Id == id && !cc.IsDeleted);
        }

        public async Task<IEnumerable<ChiefComplaint>> GetByPatientIdAsync(Guid patientId)
        {
            return await _context.ChiefComplaint.Where(cc => cc.PatientId == patientId && !cc.IsDeleted).ToListAsync();
        }
        public async Task UpdateAsync(ChiefComplaint entity)
        {
            _context.ChiefComplaint.Update(entity);
            await _context.SaveChangesAsync();
        }
        public async Task DeleteByEntityAsync(ChiefComplaint entity)
        {
            entity.IsDeleted = true;  
            _context.ChiefComplaint.Update(entity);
            await _context.SaveChangesAsync();
        }
        public async Task DeleteByIdAsync(Guid id)
        {
            var entity = await GetByIdAsync(id);
            if (entity != null)
            {
                entity.IsDeleted = true;  
                await UpdateAsync(entity); 
            }
        }
        public async Task UpdateChiefComplaintListAsync(List<ChiefComplaint> complaints)
        {
            if (complaints == null || complaints.Count == 0)
                throw new ArgumentException("Invalid records provided.", nameof(complaints));

            foreach (var complaint in complaints)
            {
                var existingComplaint = await _context.ChiefComplaint
                    .FirstOrDefaultAsync(cc => cc.Id == complaint.Id);

                if (existingComplaint != null)
                {
                    existingComplaint.Description = complaint.Description;
                    existingComplaint.DateOfComplaint = complaint.DateOfComplaint;
                    existingComplaint.IsDeleted = complaint.IsDeleted;                 
                    _context.ChiefComplaint.Update(existingComplaint);
                }
            }
            await _context.SaveChangesAsync();
        }
    }
}

