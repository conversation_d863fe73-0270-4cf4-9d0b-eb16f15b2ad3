﻿using Microsoft.Extensions.Configuration;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PracticeBusinessLayer.QueryHandler
{
    public class PracticeQueryHandler : IPracticeQueryHandler<Tasks>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PracticeQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Tasks>> GetTasks()
        {
            return await _unitOfWork.PracticeRepository.GetAllAsync();
        }

        public async Task<Tasks> GetTaskById(Guid id)
        {
            return await _unitOfWork.PracticeRepository.GetByIdAsync(id);
        }
    }
}
