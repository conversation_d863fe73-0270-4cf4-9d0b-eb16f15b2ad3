﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using Azure.Core;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class FamilyMemberService : IFamilyMemberService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<FamilyMemberService> _localizer;
        private readonly ILogger<FamilyMemberService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public FamilyMemberService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<FamilyMemberService> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        ///  Get Active Family Member by Id 
        /// </summary>
        public async Task<List<FamilyMember>> GetFamilyMemberByIdAsyncAndIsActive(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/FamilyMember/{id}/isActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<FamilyMember>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        ///  Get All Family Member by Id 
        /// </summary>
        public async Task<List<FamilyMember>> GetMemberAsync(Guid id)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/FamilyMember/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<FamilyMember>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["MemberRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Add new Member
        /// </summary>
        public async Task CreateMemberAsync(List<FamilyMember> Member)
        {
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(Member);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var apiUrl = $"{_EncounterNotes}/api/FamilyMember";
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };

            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update an existing member
        /// </summary>
        public async Task UpdateMemberAsync(FamilyMember Member)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/FamilyMember/{Member.RecordID}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(Member);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        ///  Delete an existing Member By Id
        /// </summary>
        public async Task DeleteMemberAsync(Guid MemberId)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/FamilyMember/{MemberId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }

        /// <summary>
        ///  Update an existing List of Family Members
        /// </summary>
        public async Task UpdateFamilyMemberList(List<FamilyMember> familyMembers)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/FamilyMember/updateMember";

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var bodyContent = System.Text.Json.JsonSerializer.Serialize(familyMembers);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new HttpRequestException(_localizer["UpdateFamilyMemberListFailure"], ex);
            }
        }
    }
}