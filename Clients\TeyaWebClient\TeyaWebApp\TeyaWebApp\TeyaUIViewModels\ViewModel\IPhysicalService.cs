﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPhysicalService
    {
        Task<List<Physicalexamination>> GetExaminationsByIdAsync(Guid id);
        Task AddExaminationAsync(List<Physicalexamination> examinations);
        Task DeleteExaminationAsync(Physicalexamination physical);
        Task UpdateExaminationAsync(Physicalexamination examination);
        Task<List<Physicalexamination>> GetExaminationByIdAsyncAndIsActive(Guid id);
        Task UpdateExaminationListAsync(List<Physicalexamination> physicals);
    }
}
