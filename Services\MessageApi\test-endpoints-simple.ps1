# COMPREHENSIVE REAL ENDPOINT TESTING - SIMPLE VERSION
Write-Host "COMPREHENSIVE REAL ENDPOINT TESTING" -ForegroundColor Cyan
Write-Host "Testing ALL endpoints with REAL inputs - NO MOCKING!" -ForegroundColor Yellow

$baseUrl = "http://localhost:5000"
$testResults = @()

# Test data
$senderEmail = "<EMAIL>"
$receiverEmail = "<EMAIL>"
$senderName = "Dr. <PERSON>"
$receiverName = "Dr. <PERSON>"

# Create test attachment
$attachmentContent = @"
REAL ENDPOINT TEST - MEDICAL CONSULTATION REPORT
===============================================

Patient: Jane Doe
DOB: 1990-05-15
Patient ID: P67890
Date: $(Get-Date -Format "yyyy-MM-dd")

Chief Complaint:
Patient reports severe headache and nausea for 2 days.

Physical Examination:
- Temperature: 101.5F
- Blood Pressure: 140/90 mmHg
- Heart Rate: 95 bpm

Assessment:
Possible migraine with associated symptoms.

Treatment Plan:
1. Sumatriptan 50mg for acute migraine relief
2. CT scan of head
3. Follow-up in 48 hours

Physician: Dr. Sarah Johnson, MD
Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

This is a REAL endpoint test file for MessageAPI.
"@

$attachmentFile = "real-endpoint-test-report.txt"
$attachmentContent | Out-File -FilePath $attachmentFile -Encoding UTF8

$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($attachmentContent)
$base64Content = [Convert]::ToBase64String($fileBytes)
$fileSize = $fileBytes.Length

Write-Host "Created test attachment: $attachmentFile ($fileSize bytes)" -ForegroundColor Green

# Function to test endpoint
function Test-Endpoint {
    param($Name, $Method, $Url, $Body = $null)
    
    Write-Host "TESTING: $Name" -ForegroundColor Yellow
    Write-Host "  Method: $Method" -ForegroundColor Gray
    Write-Host "  URL: $Url" -ForegroundColor Gray
    
    try {
        $headers = @{ "Content-Type" = "application/json" }
        
        if ($Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -Headers $headers
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }
        
        Write-Host "  SUCCESS: $Name" -ForegroundColor Green
        return @{ Success = $true; Response = $response; Error = $null }
    }
    catch {
        Write-Host "  FAILED: $Name - $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false; Response = $null; Error = $_.Exception.Message }
    }
}

Write-Host "STARTING COMPREHENSIVE ENDPOINT TESTING..." -ForegroundColor Cyan

# TEST 1: Send Message with Attachment
Write-Host "TEST 1: SEND MESSAGE WITH ATTACHMENT" -ForegroundColor Magenta
$sendMessageRequest = @{
    senderName = $senderName
    senderEmailId = $senderEmail
    receiverName = $receiverName
    receiverEmailId = $receiverEmail
    subject = "REAL ENDPOINT TEST - Urgent Medical Consultation"
    messageContent = "Dear Dr. Brown, This is a REAL endpoint test for the MessageAPI system. Please review the attached medical consultation report for patient Jane Doe (P67890). Best regards, Dr. Sarah Johnson, MD"
    attachments = @(
        @{
            fileName = $attachmentFile
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSize
        }
    )
} | ConvertTo-Json -Depth 10

$sendResult = Test-Endpoint "Send Message with Attachment" "POST" "$baseUrl/api/message/send" $sendMessageRequest
$testResults += $sendResult

if ($sendResult.Success) {
    $messageId = $sendResult.Response.messageId
    Write-Host "  Message ID: $messageId" -ForegroundColor Cyan
    Write-Host "  Attachments Processed: $($sendResult.Response.attachmentsProcessed)" -ForegroundColor Cyan
} else {
    Write-Host "  Cannot continue with other tests - Send Message failed" -ForegroundColor Yellow
    exit 1
}

# TEST 2: Get Message Attachments
Write-Host "TEST 2: GET MESSAGE ATTACHMENTS" -ForegroundColor Magenta
$getAttachmentsResult = Test-Endpoint "Get Message Attachments" "GET" "$baseUrl/api/message/$messageId/attachments"
$testResults += $getAttachmentsResult

if ($getAttachmentsResult.Success) {
    $attachments = $getAttachmentsResult.Response
    Write-Host "  Found $($attachments.Count) attachment(s)" -ForegroundColor Cyan
    
    if ($attachments.Count -gt 0) {
        $attachment = $attachments[0]
        $attachmentId = $attachment.attachmentId
        Write-Host "  Attachment Details:" -ForegroundColor Gray
        Write-Host "    - ID: $($attachment.attachmentId)" -ForegroundColor Gray
        Write-Host "    - Original Name: $($attachment.originalFileName)" -ForegroundColor Gray
        Write-Host "    - Blob Name: $($attachment.blobFileName)" -ForegroundColor Gray
        Write-Host "    - Size: $($attachment.fileSizeBytes) bytes" -ForegroundColor Gray
        Write-Host "    - Azure URL: $($attachment.blobStorageUrl)" -ForegroundColor Yellow
        Write-Host "    - Container: $($attachment.blobContainerName)" -ForegroundColor Yellow
        
        # Verify real Azure URL
        if ($attachment.blobStorageUrl -like "*teyarecordingsdev*") {
            Write-Host "    VERIFIED: Real Azure Blob Storage URL!" -ForegroundColor Green
        } else {
            Write-Host "    WARNING: Not using real Azure storage!" -ForegroundColor Red
        }
    }
}

# TEST 3: Download Attachment
Write-Host "TEST 3: DOWNLOAD ATTACHMENT" -ForegroundColor Magenta
if ($getAttachmentsResult.Success -and $attachments.Count -gt 0) {
    $downloadResult = Test-Endpoint "Download Attachment" "GET" "$baseUrl/api/message/attachment/$attachmentId/download"
    $testResults += $downloadResult
    
    if ($downloadResult.Success) {
        $downloadedFile = "downloaded-endpoint-test-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
        $downloadResult.Response | Out-File -FilePath $downloadedFile -Encoding UTF8
        Write-Host "  Downloaded to: $downloadedFile" -ForegroundColor Cyan
        
        # Verify content
        $downloadedSize = (Get-Item $downloadedFile).Length
        Write-Host "  Original size: $fileSize bytes" -ForegroundColor Gray
        Write-Host "  Downloaded size: $downloadedSize bytes" -ForegroundColor Gray
        
        if ($downloadedSize -gt 0) {
            Write-Host "  VERIFIED: File downloaded successfully!" -ForegroundColor Green
        } else {
            Write-Host "  WARNING: Downloaded file is empty!" -ForegroundColor Red
        }
    }
} else {
    Write-Host "  Skipping download test - no attachments found" -ForegroundColor Yellow
}

# TEST 4: Send Second Message
Write-Host "TEST 4: SEND SECOND MESSAGE" -ForegroundColor Magenta
$sendMessage2Request = @{
    senderName = $receiverName
    senderEmailId = $receiverEmail
    receiverName = $senderName
    receiverEmailId = $senderEmail
    subject = "RE: REAL ENDPOINT TEST - Medical Consultation Response"
    messageContent = "Dear Dr. Johnson, Thank you for the consultation request. I agree with your assessment. Best regards, Dr. Michael Brown, MD"
    attachments = @()
} | ConvertTo-Json -Depth 10

$sendResult2 = Test-Endpoint "Send Second Message" "POST" "$baseUrl/api/message/send" $sendMessage2Request
$testResults += $sendResult2

if ($sendResult2.Success) {
    $messageId2 = $sendResult2.Response.messageId
    Write-Host "  Second Message ID: $messageId2" -ForegroundColor Cyan
}

# TEST 5: Get Inbox Messages
Write-Host "TEST 5: GET INBOX MESSAGES" -ForegroundColor Magenta
$getInboxResult = Test-Endpoint "Get Inbox Messages" "GET" "$baseUrl/api/message/inbox/$senderEmail"
$testResults += $getInboxResult

if ($getInboxResult.Success) {
    $inboxMessages = $getInboxResult.Response
    Write-Host "  Found $($inboxMessages.Count) inbox message(s)" -ForegroundColor Cyan
    
    foreach ($msg in $inboxMessages) {
        Write-Host "  Inbox Message:" -ForegroundColor Gray
        Write-Host "    - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "    - From: $($msg.senderName) <$($msg.senderEmailId)>" -ForegroundColor Gray
        Write-Host "    - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "    - Has Attachments: $($msg.hasAttachments)" -ForegroundColor Gray
    }
}

# TEST 6: Get Sent Messages
Write-Host "TEST 6: GET SENT MESSAGES" -ForegroundColor Magenta
$getSentResult = Test-Endpoint "Get Sent Messages" "GET" "$baseUrl/api/message/sent/$senderEmail"
$testResults += $getSentResult

if ($getSentResult.Success) {
    $sentMessages = $getSentResult.Response
    Write-Host "  Found $($sentMessages.Count) sent message(s)" -ForegroundColor Cyan
    
    foreach ($msg in $sentMessages) {
        Write-Host "  Sent Message:" -ForegroundColor Gray
        Write-Host "    - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "    - To: $($msg.receiverName) <$($msg.receiverEmailId)>" -ForegroundColor Gray
        Write-Host "    - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "    - Has Attachments: $($msg.hasAttachments)" -ForegroundColor Gray
    }
}

# TEST 7: Get Message by ID
Write-Host "TEST 7: GET MESSAGE BY ID" -ForegroundColor Magenta
$getMessageResult = Test-Endpoint "Get Message by ID" "GET" "$baseUrl/api/message/$messageId"
$testResults += $getMessageResult

if ($getMessageResult.Success) {
    $message = $getMessageResult.Response
    Write-Host "  Message Details:" -ForegroundColor Cyan
    Write-Host "    - ID: $($message.messageId)" -ForegroundColor Gray
    Write-Host "    - From: $($message.senderName) <$($message.senderEmailId)>" -ForegroundColor Gray
    Write-Host "    - To: $($message.receiverName) <$($message.receiverEmailId)>" -ForegroundColor Gray
    Write-Host "    - Subject: $($message.subject)" -ForegroundColor Gray
    Write-Host "    - Content Length: $($message.messageContent.Length) characters" -ForegroundColor Gray
    Write-Host "    - Has Attachments: $($message.hasAttachments)" -ForegroundColor Gray
    Write-Host "    - Attachment Count: $($message.attachmentCount)" -ForegroundColor Gray
    Write-Host "    - Status: $($message.status)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "BASIC ENDPOINT TESTING COMPLETED" -ForegroundColor Cyan

$successCount = ($testResults | Where-Object { $_.Success }).Count
$totalCount = $testResults.Count
$failureCount = $totalCount - $successCount

Write-Host "SUMMARY:" -ForegroundColor Yellow
Write-Host "  Total Tests: $totalCount" -ForegroundColor Gray
Write-Host "  Successful: $successCount" -ForegroundColor Green
Write-Host "  Failed: $failureCount" -ForegroundColor Red
Write-Host "  Success Rate: $([math]::Round(($successCount / $totalCount) * 100, 2))%" -ForegroundColor Cyan

if ($failureCount -eq 0) {
    Write-Host "ALL BASIC ENDPOINTS TESTED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "MessageAPI is functional with real Azure Blob Storage!" -ForegroundColor Green
} else {
    Write-Host "Some endpoints failed. Check the details above." -ForegroundColor Yellow
}

Write-Host "Testing completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
