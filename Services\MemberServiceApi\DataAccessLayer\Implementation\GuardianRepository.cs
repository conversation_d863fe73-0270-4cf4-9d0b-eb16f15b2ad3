﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class GuardianRepository : GenericRepository<Guardian>, IGuardianRepository
    {
        private readonly AccountDatabaseContext _context;

        public GuardianRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }


    }
}