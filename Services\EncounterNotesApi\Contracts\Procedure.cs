﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class Procedure : IContract
    {
        public Guid Id { get; set; }
        public Guid PatientId { get; set; }
        public string CPTCode { get; set; }
               
        public string Description { get; set; }
        public string Notes { get; set; }       
        public DateTime OrderDate { get; set; }
        public DateTime? LastUpdatedDate { get; set; }
        public Guid CreatedByUserId { get; set; }
        public Guid UpdatedByUserId { get; set; }
        public Guid OrganizationId { get; set; }
        public string OrderedBy { get; set; }
        public Guid PcpId { get; set; }
        public bool IsDeleted { get; set; } = true;
        public string? AssessmentData { get; set; }
        public Guid AssessmentId { get; set; }
    }
}
