﻿CREATE TABLE [AccountService].[Users] (
    [Id]                     UNIQUEIDENTIFIER NOT NULL,
    [UserName]               NVARCHAR (100)   NULL,
    [Password]               NVARCHAR (100)   NULL,
    [Email]                  NVARCHAR (100)   NULL,
    [IsActive]               BIT              NULL,
    [FirstName]              NVARCHAR (100)   NULL,
    [LastName]               NVARCHAR (100)   NULL,
    [PhoneNumber]            NVARCHAR (15)    NULL,
    [DateOfBirth]            DATE             NULL,
    [Country]                NVARCHAR (100)   NULL,
    [Language]               VARCHAR (255)    NULL,
    [Ethnicity]              VARCHAR (255)    NULL,
    [Race]                   VARCHAR (255)    NULL,
    [Nationality]            VARCHAR (255)    NULL,
    [FamilySize]             INT              NULL,
    [FinancialReviewDate]    DATE             NULL,
    [MonthlyIncome]          DECIMAL (15, 2)  NULL,
    [Interpreter]            BIT              NULL,
    [MigrantOrSeasonal]      BIT              NULL,
    [ReferralSource]         VARCHAR (255)    NULL,
    [Religion]               VARCHAR (255)    NULL,
    [PreferredName]          VARCHAR (255)    NULL,
    [ExternalID]             VARCHAR (255)    NULL,
    [MaritalStatus]          VARCHAR (100)    NULL,
    [SexualOrientation]      VARCHAR (100)    NULL,
    [PreviousNames]          TEXT             NULL,
    [DateDeceased]           DATE             NULL,
    [ReasonDeceased]         VARCHAR (255)    NULL,
    [MiddleName]             NVARCHAR (100)   NULL,
    [Suffix]                 NVARCHAR (100)   NULL,
    [FederalTaxId]           NVARCHAR (100)   NULL,
    [DEANumber]              NVARCHAR (100)   NULL,
    [UPIN]                   NVARCHAR (100)   NULL,
    [NPI]                    NVARCHAR (100)   NULL,
    [ProviderType]           NVARCHAR (100)   NULL,
    [MainMenuRole]           NVARCHAR (100)   NULL,
    [PatientMenuRole]        NVARCHAR (100)   NULL,
    [Supervisor]             NVARCHAR (100)   NULL,
    [JobDescription]         NVARCHAR (255)   NULL,
    [Taxonomy]               NVARCHAR (100)   NULL,
    [NewCropERxRole]         NVARCHAR (100)   NULL,
    [AccessControl]          NVARCHAR (100)   NULL,
    [AdditionalInfo]         NVARCHAR (255)   NULL,
    [DefaultBillingFacility] NVARCHAR (255)   NULL,
    [RoleID]                 UNIQUEIDENTIFIER NULL,
    [OrganizationID]         UNIQUEIDENTIFIER NULL,
    [OrganizationName]       NVARCHAR (100)   NULL,
    [AddressId]              UNIQUEIDENTIFIER NULL,
    [PCPName]                NVARCHAR (255)   NULL,
    [PCPId]                  UNIQUEIDENTIFIER NULL,
    [InsuranceId]            UNIQUEIDENTIFIER NULL,
    [SSN]                    NVARCHAR (100)   NULL,
    [RoleName]               NVARCHAR (50)    NULL,
    [ProfileImageUrl]        NVARCHAR (MAX)   NULL,
    [GuardianId]             UNIQUEIDENTIFIER NULL,
    [EmployerId]             UNIQUEIDENTIFIER NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Users_Addresses] FOREIGN KEY ([AddressId]) REFERENCES [AccountService].[Addresses] ([AddressId]) ON DELETE SET NULL,
    CONSTRAINT [FK_Users_EmployerDetails] FOREIGN KEY ([EmployerId]) REFERENCES [AccountService].[EmployerDetails] ([EmployerId]) ON DELETE SET NULL,
    CONSTRAINT [FK_Users_GuardianDetails] FOREIGN KEY ([GuardianId]) REFERENCES [AccountService].[GuardianDetails] ([GuardianId]) ON DELETE SET NULL,
    CONSTRAINT [FK_Users_Insurance] FOREIGN KEY ([InsuranceId]) REFERENCES [AccountService].[Insurance] ([InsuranceId]) ON DELETE SET NULL,
    CONSTRAINT [FK_Users_Organization] FOREIGN KEY ([OrganizationID]) REFERENCES [AccountService].[Organization] ([OrganizationId]) ON DELETE CASCADE,
    CONSTRAINT [FK_Users_Role] FOREIGN KEY ([RoleID]) REFERENCES [AccountService].[Role] ([RoleId])
);

