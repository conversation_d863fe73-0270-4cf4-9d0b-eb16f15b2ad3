﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class ProductCommandHandler : IProductCommandHandler<Product>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProductCommandHandler> _logger;

        public ProductCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<ProductCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddProduct(List<Product> products)
        {
            await _unitOfWork.ProductRepository.AddAsync(products);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateProduct(Product product)
        {
            await _unitOfWork.ProductRepository.UpdateAsync(product);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteProductById(Guid id)
        {
            await _unitOfWork.ProductRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteProductByEntity(Product product)
        {
            await _unitOfWork.ProductRepository.DeleteByEntityAsync(product);
            await _unitOfWork.SaveAsync();
        }
    }
}
