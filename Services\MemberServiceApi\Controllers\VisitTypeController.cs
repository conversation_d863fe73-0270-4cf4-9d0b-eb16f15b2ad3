﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using MemberServiceBusinessLayer;
using Contracts;
using Microsoft.Extensions.Localization;

namespace MemberServiceApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VisitTypeController : ControllerBase
    {
        private readonly IVisitTypeQueryHandler<VisitType> _visitTypeQueryHandler;
        private readonly IVisitTypeCommandHandler<VisitType> _visitTypeCommandHandler;
        private readonly ILogger<VisitTypeController> _logger;
        private readonly IStringLocalizer<VisitTypeController> _localizer;

        public VisitTypeController(
            IVisitTypeQueryHandler<VisitType> queryHand<PERSON>,
            IVisitTypeCommandHandler<VisitType> commandHand<PERSON>,
            ILogger<VisitTypeController> logger,
            IStringLocalizer<VisitTypeController> localizer
        )
        {
            _visitTypeQueryHandler = queryHandler;
            _visitTypeCommandHandler = commandHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Retrieves a list of all visit types.
        /// </summary>
        /// <returns>A list of visit types.</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<VisitType>>> Get()
        {
            ActionResult<IEnumerable<VisitType>> result;
            try
            {
                var visitTypes = await _visitTypeQueryHandler.GetAllVisitTypesAsync();
                result = Ok(visitTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingVisitTypes"]);
                result = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return result;
        }

        /// <summary>
        /// Retrieves visit types by Organization ID.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <returns>List of visit types for the given Organization ID.</returns>
        [HttpGet("by-organization/{orgId}")]
        public async Task<ActionResult<IEnumerable<VisitType>>> GetByOrganizationId(Guid orgId)
        {
            ActionResult<IEnumerable<VisitType>> result;
            try
            {
                var visitTypes = await _visitTypeQueryHandler.GetByOrganizationIdAsync(orgId);

                result = visitTypes == null
                    ? NotFound(_localizer["VisitTypesNotFound"])
                    : Ok(visitTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingVisitTypes"]);
                result = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return result;
        }

        /// <summary>
        /// Adds a new visit type record.
        /// </summary>
        /// <param name="visitType">The visit type details.</param>
        /// <returns>Status of the creation operation.</returns>
        [HttpPost]
        public async Task<ActionResult> AddVisitType([FromBody] VisitType visitType)
        {
            ActionResult result;
            try
            {
                if (visitType == null)
                {
                    result = BadRequest(_localizer["InvalidVisitTypeData"]);
                }
                else
                {
                    await _visitTypeCommandHandler.AddVisitTypeAsync(visitType);
                    result = CreatedAtAction(nameof(GetByOrganizationId), new { orgId = visitType.OrganizationId }, visitType);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingVisitType"]);
                result = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return result;
        }

        /// <summary>
        /// Updates the CPT code for a specific visit type by organization ID and visit name.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <param name="visitName">The name of the visit type.</param>
        /// <param name="newCptCode">The new CPT code to update (use "null-values" to clear it).</param>
        /// <returns>Status of the update operation.</returns>
        [HttpPut("update-cpt-code/{orgId}/{visitName}/{newCptCode}")]
        public async Task<ActionResult> UpdateCptCode(Guid orgId, string visitName, string newCptCode)
        {
            ActionResult result;
            try
            {
                var updateSuccessful = await _visitTypeCommandHandler.UpdateCptCodeAsync(orgId, visitName, newCptCode);

                result = updateSuccessful
                    ? Ok(_localizer["CptCodeUpdatedSuccessfully"])
                    : NotFound(_localizer["VisitTypeNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingCptCode"]);
                result = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return result;
        }

        /// <summary>
        /// Deletes a visit type based on Organization ID and visit name.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <param name="visitName">The name of the visit type.</param>
        /// <returns>Status of the delete operation.</returns>
        [HttpDelete("delete/{orgId}/{visitName}")]
        public async Task<ActionResult> DeleteVisitType(Guid orgId, string visitName)
        {
            ActionResult result;
            try
            {
                var deleteSuccessful = await _visitTypeCommandHandler.DeleteVisitTypeAsync(orgId, visitName);

                result = deleteSuccessful
                    ? Ok(_localizer["VisitTypeDeletedSuccessfully"])
                    : NotFound(_localizer["VisitTypeNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingVisitType"]);
                result = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return result;
        }
    }
}