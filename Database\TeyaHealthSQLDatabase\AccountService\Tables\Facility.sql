﻿CREATE TABLE [AccountService].[Facility] (
    [FacilityId]     UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [FacilityName]   NVARCHAR (100)   NOT NULL,
    [StreetName]     NVARCHAR (100)   NULL,
    [City]           NVARCHAR (100)   NULL,
    [State]          NVARCHAR (100)   NULL,
    [Zipcode]        NVARCHAR (100)   NULL,
    [Country]        NVARCHAR (100)   NULL,
    [CreatedDate]    DATETIME         DEFAULT (getdate()) NOT NULL,
    [UpdatedDate]    DATETIME         NULL,
    [OrganizationId] UNIQUEIDENTIFIER NULL,
    [IsActive]       BIT              NULL,
    [UpdatedBy]      UNIQUEIDENTIFIER DEFAULT (newid()) NULL,
    PRIMARY KEY CLUSTERED ([FacilityId] ASC),
    CONSTRAINT [UQ_Facility_FacilityName_OrganizationId] UNIQUE NONCLUSTERED ([FacilityName] ASC, [OrganizationId] ASC)
);

