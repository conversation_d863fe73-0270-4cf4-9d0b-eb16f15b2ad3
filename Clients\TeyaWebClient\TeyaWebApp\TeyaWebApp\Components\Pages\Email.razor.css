﻿/* Base Styles */
:root {
    --primary-color: #1a73e8;
    --hover-bg: #f1f3f4;
    --active-bg: #e8f0fe;
    --border-color: #e0e0e0;
    --text-color: #202124;
    --secondary-text: #5f6368;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.email-app {
    display: flex;
    height: 100vh;
    font-family: 'Google Sans', Roboto, sans-serif;
    background: #fff;
    color: var(--text-color);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: #f6f8fc;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.profile {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.profile-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.menu {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.menu-icon {
    width: 24px;
    margin-right: 16px;
}

.category {
    color: var(--secondary-text);
    font-size: 0.75rem;
    text-transform: uppercase;
    padding: 16px 24px 8px;
    cursor: default;
}

/* Main Content */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.toolbar {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    gap: 16px;
    border-bottom: 1px solid var(--border-color);
}

.tabs {
    display: flex;
    gap: 4px;
}

.tab {
    padding: 8px 16px;
    border-radius: 16px;
    border: none;
    background: none;
    cursor: pointer;
}

    .tab.active {
        background: var(--active-bg);
        color: var(--primary-color);
    }

.search-box {
    flex: 1;
    max-width: 600px;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 8px 16px 8px 40px;
    border-radius: 24px;
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.search-box i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-text);
}

/* Email List */
.content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.email-list {
    width: 35%;
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.time {
    font-size: 0.8rem;
    color: var(--secondary-text);
}

.subject-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.email-indicators {
    display: flex;
    align-items: center;
    gap: 8px;
}

.subject {
    font-weight: 500;
}

.preview-text {
    color: var(--secondary-text);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 8px;
}

.labels {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.label {
    background: var(--active-bg);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
}

.email-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

/* Compose Modal */
.compose-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.compose-modal {
    position: fixed;
    bottom: 0;
    right: 24px;
    width: 600px;
    background: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 0 16px rgba(0,0,0,0.2);
}

.compose-input {
    width: 100%;
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.compose-textarea {
    width: 100%;
    height: 300px;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: none;
}

/* Email Detail */
.email-detail {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

/* Responsive Design */
@@media (max-width: 768px) {
    .email-list {
        width: 100%;
    }

    .email-detail {
        display: none;
    }
}

/* Compact Compose Window Styles */
.compose-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 500px;
    max-width: 90vw;
    max-height: 80vh;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.compose-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 8px 8px 0 0;
}

    .compose-header span {
        font-weight: 500;
        font-size: 0.9rem;
    }

.compose-body {
    padding: 16px;
    flex-grow: 1;
    overflow-y: auto;
}

.compose-to, .compose-subject {
    width: 100%;
    border: none;
    padding: 8px 0;
    margin-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 0.9rem;
}

    .compose-to:focus, .compose-subject:focus {
        outline: none;
        border-bottom: 2px solid #1a73e8;
    }

.compose-editor {
    margin-top: 12px;
    height: 200px;
}

    .compose-editor textarea {
        width: 100%;
        height: 100%;
        border: none;
        resize: none;
        font-family: inherit;
        font-size: 0.9rem;
        padding: 8px;
    }

        .compose-editor textarea:focus {
            outline: none;
        }

.compose-footer {
    padding: 12px 16px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.send-btn {
    background: #1a73e8;
    color: white;
    padding: 8px 24px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-icon {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #5f6368;
}

/* Responsive Design */
@@media (max-width: 600px) {
    .compose-container {
        width: 100%;
        bottom: 0;
        right: 0;
        border-radius: 8px 8px 0 0;
    }
}

.reply-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 500px;
    max-height: 70vh;
    z-index: 1000;
}

.reply-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.left-controls {
    display: flex;
    gap: 8px;
}

/* Attachment Styles */
.attachments-section {
    margin-top: 12px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.attachments-section h4 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: var(--secondary-text);
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    margin-bottom: 4px;
    border: 1px solid #e0e0e0;
}

.attachment-item i {
    color: var(--primary-color);
}

.attachment-item span {
    flex: 1;
    font-size: 0.85rem;
}

.btn-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
}

.btn-remove:hover {
    background: #f8d7da;
}

.file-upload-section {
    margin-top: 12px;
    position: relative;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.file-upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--secondary-text);
    transition: 0.2s;
}

.file-upload-btn:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.compose-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.left-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.attachment-count {
    font-size: 0.85rem;
    color: var(--secondary-text);
    display: flex;
    align-items: center;
    gap: 4px;
}

.send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading indicator */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    background: white;
    padding: 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

/* Email Attachments Display */
.email-attachments {
    margin-top: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.email-attachments h4 {
    margin: 0 0 12px 0;
    font-size: 0.9rem;
    color: var(--secondary-text);
    display: flex;
    align-items: center;
    gap: 8px;
}

.attachment-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.attachment-item-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    transition: 0.2s;
}

.attachment-item-display:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.attachment-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.attachment-info i {
    font-size: 1.2rem;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.attachment-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.attachment-name {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-color);
}

.attachment-size {
    font-size: 0.8rem;
    color: var(--secondary-text);
}

.download-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85rem;
    transition: 0.2s;
}

.download-btn:hover {
    background: #1557b0;
    transform: translateY(-1px);
}

.load-attachments-btn {
    background: #f1f3f4;
    color: var(--secondary-text);
    border: 1px solid #e0e0e0;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    transition: 0.2s;
}

.load-attachments-btn:hover {
    background: #e8f0fe;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Enhanced Email List Styles */
.email-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.sender-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.sender-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.email-time {
    font-size: 0.8rem;
    color: var(--secondary-text);
    margin-left: auto;
}

.email-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
}

.email-item:hover .email-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--secondary-text);
    transition: all 0.2s;
    font-size: 0.85rem;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

.action-btn.important.active {
    color: #ffa000;
}

.action-btn.important:hover {
    color: #ffa000;
}

.action-btn.archive:hover {
    color: #4caf50;
}

.action-btn.archive.active {
    color: #4caf50;
    background: #e8f5e8;
}

.action-btn.delete:hover {
    color: #f44336;
}

.action-btn.restore:hover {
    color: #2196f3;
}

.action-btn.delete-permanent:hover {
    color: #d32f2f;
    background: #ffebee;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-dialog {
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-header i {
    color: #ff9800;
}

.modal-body {
    padding: 24px;
}

.modal-body p {
    margin: 0 0 16px 0;
    color: var(--text-color);
}

.message-preview {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
    font-size: 0.9rem;
    line-height: 1.5;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background: #f8f9fa;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Professional Enhancements */
.email-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.email-item:hover {
    background: var(--hover-bg);
    border-left-color: var(--primary-color);
    transform: translateX(2px);
}

.email-item.selected {
    background: var(--active-bg);
    border-left-color: var(--primary-color);
    box-shadow: inset 0 0 0 1px rgba(26, 115, 232, 0.2);
}

.unread-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Menu Item Enhancements */
.menu li {
    display: flex;
    align-items: center;
    padding: 8px 24px;
    margin: 2px 0;
    border-radius: 0 24px 24px 0;
    cursor: pointer;
    transition: 0.2s;
    position: relative;
    overflow: hidden;
}

.menu li.active {
    background: var(--active-bg);
    color: var(--primary-color);
    font-weight: 500;
}

.menu li:hover:not(.category) {
    background: var(--hover-bg);
}

.menu li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(26, 115, 232, 0.1), transparent);
    transition: width 0.3s ease;
}

.menu li:hover::before {
    width: 100%;
}

.count {
    margin-left: auto;
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

/* Improved Attachment Display */
.attachment-icon {
    color: var(--primary-color);
    font-size: 0.9rem;
}

/* Enhanced Compose Button */
.compose-btn {
    background: #c2e7ff;
    color: #001d35;
    padding: 12px 24px;
    border-radius: 24px;
    margin: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: 0.2s;
    position: relative;
    overflow: hidden;
}

.compose-btn:hover {
    background: #b8dfff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.compose-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.compose-btn:hover::before {
    width: 200px;
    height: 200px;
}

/* Gmail-like Email Detail Header */
.email-detail-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.subject-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
}

.email-subject {
    font-size: 1.375rem;
    font-weight: 400;
    color: var(--text-color);
    margin: 0;
    line-height: 1.3;
    flex: 1;
}

.header-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

/* Gmail-like Participants Section */
.email-participants {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
}

.participant-row {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.participant-avatar {
    flex-shrink: 0;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.participant-details {
    flex: 1;
    min-width: 0;
}

.participant-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.participant-name {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.875rem;
}

.participant-email {
    color: var(--secondary-text);
    font-size: 0.875rem;
}

.email-recipients {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    color: var(--secondary-text);
    flex-wrap: wrap;
}

.recipient-label, .cc-label {
    color: var(--secondary-text);
    font-weight: 400;
}

.recipient-email, .cc-list {
    color: var(--text-color);
}

.recipient-separator {
    color: var(--secondary-text);
}

.email-timestamp {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    flex-shrink: 0;
}

.email-date {
    font-size: 0.875rem;
    color: var(--secondary-text);
    white-space: nowrap;
}

.reply-btn {
    padding: 6px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.reply-btn:hover {
    background: var(--hover-bg);
}

/* Email Body Styles */
.email-body {
    padding: 24px;
    line-height: 1.6;
    white-space: pre-wrap;
    font-size: 0.875rem;
    color: var(--text-color);
}

/* Additional Email Indicators */
.delivery-status {
    font-size: 0.75rem;
    color: var(--secondary-text);
    font-weight: 500;
}

.delivery-status[title*="Delivered"], .delivery-status[title*="Read"] {
    color: #4caf50;
}

.delivery-status[title*="Failed"] {
    color: #f44336;
}

.archived-indicator {
    font-size: 0.75rem;
    color: #4caf50;
    display: flex;
    align-items: center;
    gap: 2px;
}

.archived-indicator i {
    font-size: 0.7rem;
}

/* Compose Field Styles */
.compose-field {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
}

.compose-field label {
    min-width: 40px;
    font-weight: 500;
    color: var(--secondary-text);
    font-size: 0.9rem;
}

.compose-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

/* No Email Selected State */
.no-email-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--secondary-text);
    font-size: 1.1rem;
}

.no-email-selected i {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Email Reply Section */
.email-reply {
    margin-top: 24px;
    padding: 16px;
    border-top: 1px solid var(--border-color);
}

.email-reply textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    font-size: 0.9rem;
    margin-bottom: 12px;
}

.email-reply textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}
