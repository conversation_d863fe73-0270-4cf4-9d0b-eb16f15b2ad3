﻿using System;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class SocialHistoryCommandHandler : ISocialHistoryCommandHandler<PatientSocialHistory>
    {
        private readonly IUnitOfWork _unitOfWork;

        public SocialHistoryCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Adds a new social history record to the database.
        /// </summary>
        /// <param name="history">The social history record to add.</param>
        public async Task AddHistory(List<PatientSocialHistory> histories)
        {
            await _unitOfWork.SocialHistoryRepository.AddAsync(histories);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Updates an existing social history record in the database.
        /// </summary>
        /// <param name="histories">The social history record to update.</param>
        public async Task UpdateHistoryList(List<PatientSocialHistory> histories)
        {
            await _unitOfWork.SocialHistoryRepository.UpdateRangeAsync(histories);
            await _unitOfWork.SaveAsync();
        }
    }
}
