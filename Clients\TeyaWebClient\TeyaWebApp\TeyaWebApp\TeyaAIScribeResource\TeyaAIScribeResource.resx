﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="DelayMilliseconds" xml:space="preserve">
    <value>500</value>
  </data>
  <data name="NoResultsCount" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="FirstIndex" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="emailAddress" xml:space="preserve">
    <value>emailAddress</value>
  </data>
  <data name="AdditionalInfoRequired" xml:space="preserve">
    <value>Additional Info is required!</value>
  </data>
  <data name="AdditionalInfoMinLength" xml:space="preserve">
    <value>Additional Info must be at least 5 characters long.</value>
  </data>
  <data name="AdditionalInfoMaxLength" xml:space="preserve">
    <value>Additional Info must not exceed 300 characters.</value>
  </data>
  <data name="JobDescriptionMinLength" xml:space="preserve">
    <value>Job Description must be at least 10 characters long.</value>
  </data>
  <data name="JobDescriptionMaxLength" xml:space="preserve">
    <value>Job Description must not exceed 200 characters.</value>
  </data>
  <data name="UsernameRequired" xml:space="preserve">
    <value>UsernameRequired</value>
  </data>
  <data name="UsernameMinLength" xml:space="preserve">
    <value>Username must be at least 5 characters long.</value>
  </data>
  <data name="UsernameInvalidCharacters" xml:space="preserve">
    <value>Username can only contain letters, numbers, dots, underscores, and hyphens.</value>
  </data>
  <data name="UsernameNoSpaces" xml:space="preserve">
    <value>Username cannot contain spaces.</value>
  </data>
  <data name="FirstNameRequired" xml:space="preserve">
    <value>First Name is required!</value>
  </data>
  <data name="MiddleNameMinLength" xml:space="preserve">
    <value>Middle Name should be at least 2 characters long.</value>
  </data>
  <data name="TaxIdRequired" xml:space="preserve">
    <value>Federal Tax ID is required!</value>
  </data>
  <data name="TaxIdInvalidFormat" xml:space="preserve">
    <value>Federal Tax ID must be exactly 9 digits.</value>
  </data>
  <data name="UPINRequired" xml:space="preserve">
    <value>UPIN is required!</value>
  </data>
  <data name="UPINInvalidFormat" xml:space="preserve">
    <value>UPIN must be 5 letters followed by 1 digit.</value>
  </data>
  <data name="ErrorFetchingRoles" xml:space="preserve">
    <value>ErrorFetchingRoles</value>
  </data>
  <data name="GetLogError" xml:space="preserve">
    <value>An error occurred while fetching members.</value>
  </data>
  <data name="PostLogError" xml:space="preserve">
    <value>An error occurred while registering members.</value>
  </data>
  <data name="NoMembers" xml:space="preserve">
    <value>No members to register.</value>
  </data>
  <data name="SuccessfulRegistration" xml:space="preserve">
    <value>Members registered successfully.</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>A database error occurred.</value>
  </data>
  <data name="DeleteSuccessful" xml:space="preserve">
    <value>Delete Successful</value>
  </data>
  <data name="MemberNotFound" xml:space="preserve">
    <value>Member Not Found</value>
  </data>
  <data name="UpdateLogError" xml:space="preserve">
    <value>Log not updated</value>
  </data>
  <data name="UpdateSuccessful" xml:space="preserve">
    <value>Update Successful</value>
  </data>
  <data name="InvalidId" xml:space="preserve">
    <value>The given Id is not valid</value>
  </data>
  <data name="InvalidMember" xml:space="preserve">
    <value>Not a valid member</value>
  </data>
  <data name="DeleteLogError" xml:space="preserve">
    <value>Item is not deleted</value>
  </data>
  <data name="application/json" xml:space="preserve">
    <value>application/json</value>
  </data>
  <data name="AccessUpdateSuccessful" xml:space="preserve">
    <value>Access updated successfully</value>
  </data>
  <data name="AccessNotFound" xml:space="preserve">
    <value>Access record not found</value>
  </data>
  <data name="RegistrationFailed" xml:space="preserve">
    <value>Registration Failed</value>
  </data>
  <data name="HTTPRequestError" xml:space="preserve">
    <value>HTTP Request Error:</value>
  </data>
  <data name="MemberServiceApi_base_url" xml:space="preserve">
    <value>http://localhost/MemberServiceApi/api/Products</value>
  </data>
  <data name="ProductRetrievalFailure" xml:space="preserve">
    <value>Failed to retrieve products
</value>
  </data>
  <data name="SuccessfulRequest" xml:space="preserve">
    <value>Request successful</value>
  </data>
  <data name="ApiSettings:RegistrationUrl" xml:space="preserve">
    <value>ApiSettings:RegistrationUrl</value>
  </data>
  <data name="ApiSettings:MembersUrl" xml:space="preserve">
    <value>ApiSettings:MembersUrl</value>
  </data>
  <data name="{productId}" xml:space="preserve">
    <value>{productId}</value>
  </data>
  <data name="ApiSettings:ProductsUrl" xml:space="preserve">
    <value>ApiSettings:ProductsUrl</value>
  </data>
  <data name="ApiSettings:UpdateAccessUrl" xml:space="preserve">
    <value>ApiSettings:UpdateAccessUrl</value>
  </data>
  <data name="productId" xml:space="preserve">
    <value>productId</value>
  </data>
  <data name="LicenseRetrievalFailure" xml:space="preserve">
    <value>License Retrieval Failure</value>
  </data>
  <data name="ErrorFetchingProducts" xml:space="preserve">
    <value>Error fetching products</value>
  </data>
  <data name="ErrorFetchingMembersForProduct" xml:space="preserve">
    <value>Error Fetching Members For Product</value>
  </data>
  <data name="ErrorSavingMemberAccessUpdates" xml:space="preserve">
    <value>Error Saving Member Access Updates</value>
  </data>
  <data name="ErrorDuringRegistration" xml:space="preserve">
    <value>Error During Registration</value>
  </data>
  <data name="ErrorFetchingLicenses" xml:space="preserve">
    <value>Error Fetching Licenses</value>
  </data>
  <data name="ErrorSavingLicenses" xml:space="preserve">
    <value>Error Saving Licenses</value>
  </data>
  <data name="API Response: {ResponseData}" xml:space="preserve">
    <value> API Response: {ResponseData}</value>
  </data>
  <data name="Error deserializing response: {0}" xml:space="preserve">
    <value>Error deserializing response: {0}</value>
  </data>
  <data name="MemberRegistrationError" xml:space="preserve">
    <value>An error occurred during member registration.</value>
  </data>
  <data name="JsonDeserializationError" xml:space="preserve">
    <value>An error occurred while deserializing the response: {0}</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>PasswordRequired</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>InvalidEmailFormat</value>
  </data>
  <data name="EmailRequired" xml:space="preserve">
    <value>EmailRequired</value>
  </data>
  <data name="UserAlreadyExists" xml:space="preserve">
    <value>UserAlreadyExists</value>
  </data>
  <data name="ErrorParsingUserDetails" xml:space="preserve">
    <value>ErrorParsingUserDetails</value>
  </data>
  <data name="ErrorFetchingFullUserDetails" xml:space="preserve">
    <value>ErrorFetchingFullUserDetails</value>
  </data>
  <data name="MemberRegisteredSuccessfully" xml:space="preserve">
    <value>MemberRegisteredSuccessfully</value>
  </data>
  <data name="ErrorRegisteringMember" xml:space="preserve">
    <value>ErrorRegisteringMember</value>
  </data>
  <data name="Error parsing user details" xml:space="preserve">
    <value>Error parsing user details</value>
  </data>
  <data name="Error registering member" xml:space="preserve">
    <value>Error registering member</value>
  </data>
  <data name="Member registered successfully!" xml:space="preserve">
    <value>Member registered successfully!</value>
  </data>
  <data name="Error fetching full user details" xml:space="preserve">
    <value>Error fetching full user details</value>
  </data>
  <data name="AccessTokenMissing" xml:space="preserve">
    <value>AccessTokenMissing</value>
  </data>
  <data name="FailedToGetUserDetails" xml:space="preserve">
    <value>FailedToGetUserDetails</value>
  </data>
  <data name="ErrorGettingUserDetails" xml:space="preserve">
    <value>ErrorGettingUserDetails</value>
  </data>
  <data name="ErrorGettingFullUserDetails" xml:space="preserve">
    <value>ErrorGettingFullUserDetails</value>
  </data>
  <data name="FailedToGetFullUserDetails" xml:space="preserve">
    <value>FailedToGetFullUserDetails</value>
  </data>
  <data name="UserProfileUpdated" xml:space="preserve">
    <value>User profile updated successfully.</value>
  </data>
  <data name="UpdateFailed" xml:space="preserve">
    <value>Failed to update user profile.</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>Error updating profile.</value>
  </data>
  <data name="AccountDeleted" xml:space="preserve">
    <value>Account deleted successfully.</value>
  </data>
  <data name="DeleteError" xml:space="preserve">
    <value>Error deleting account.</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome to Teya Web App!</value>
  </data>
  <data name="Saved" xml:space="preserve">
    <value>User details saved successfully.</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Failed to save user data.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error occurred while processing.</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="Guest" xml:space="preserve">
    <value>Guest</value>
  </data>
  <data name="ErrorFetchingUsers" xml:space="preserve">
    <value>ErrorFetchingUsers</value>
  </data>
  <data name="UserCreated" xml:space="preserve">
    <value>UserCreated</value>
  </data>
  <data name="UserCreationFailed" xml:space="preserve">
    <value>UserCreationFailed</value>
  </data>
  <data name="ErrorCreatingUser" xml:space="preserve">
    <value>ErrorCreatingUser</value>
  </data>
  <data name="ErrorDeletingUsers" xml:space="preserve">
    <value>ErrorDeletingUsers</value>
  </data>
  <data name="SelectSingleUser" xml:space="preserve">
    <value>SelectSingleUser</value>
  </data>
  <data name="MemberDeletionFailed" xml:space="preserve">
    <value>MemberDeletionFailed</value>
  </data>
  <data name="DeleteFailedStatusCode" xml:space="preserve">
    <value>DeleteFailedStatusCode</value>
  </data>
  <data name="MemberDeletedSuccessfully" xml:space="preserve">
    <value>MemberDeletedSuccessfully</value>
  </data>
  <data name="ApiSettings:DeleteRegistrationMemberUrl" xml:space="preserve">
    <value>ApiSettings:DeleteRegistrationMemberUrl</value>
  </data>
  <data name="ErrorInvitingUser" xml:space="preserve">
    <value>ErrorInvitingUser</value>
  </data>
  <data name="FailedToSendInvitation" xml:space="preserve">
    <value>FailedToSendInvitation</value>
  </data>
  <data name="UserInvitedSuccessfully" xml:space="preserve">
    <value>UserInvitedSuccessfully</value>
  </data>
  <data name="UserCreationError" xml:space="preserve">
    <value>UserCreationError</value>
  </data>
  <data name="UserCreatedSuccess" xml:space="preserve">
    <value>UserCreatedSuccess</value>
  </data>
  <data name="InviteRedirectUrlDev" xml:space="preserve">
    <value>https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io</value>
    <comment>developer mode</comment>
  </data>
  <data name="InviteRedirectUrl" xml:space="preserve">
    <value>https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io</value>
  </data>
  <data name="ErrorDuringMemberSubmissionUpdate" xml:space="preserve">
    <value>ErrorDuringMemberSubmissionUpdate</value>
  </data>
  <data name="wel" xml:space="preserve">
    <value>welcome to teya</value>
    <comment>welcome to teya</comment>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>mail</value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="GivenName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="Display_Name" xml:space="preserve">
    <value>displayName</value>
  </data>
  <data name="StreetAddress" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Postal Code</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="ExtensionCompanyName" xml:space="preserve">
    <value>ExtensionCompanyName</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="UserType" xml:space="preserve">
    <value>User Type</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>Job Title</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>companyName</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>department</value>
  </data>
  <data name="OfficeLocation" xml:space="preserve">
    <value>OfficeLocation</value>
  </data>
  <data name="MobilePhone" xml:space="preserve">
    <value>MobilePhone</value>
  </data>
  <data name="Id" xml:space="preserve">
    <value>id</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>CreateUser</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="UserPrincipalName" xml:space="preserve">
    <value>UserPrincipalName</value>
  </data>
  <data name="MailNickname" xml:space="preserve">
    <value>MailNickname</value>
  </data>
  <data name="DisplayName" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="UserManagementRoute" xml:space="preserve">
    <value>/usermanagement</value>
  </data>
  <data name="InviteUrl" xml:space="preserve">
    <value>https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io</value>
  </data>
  <data name="InviteUser" xml:space="preserve">
    <value>InviteUser</value>
  </data>
  <data name="UserEmailAddress" xml:space="preserve">
    <value>UserEmailAddress</value>
  </data>
  <data name="EnterEmailAddress" xml:space="preserve">
    <value>EnterEmailAddress</value>
  </data>
  <data name="EnterDisplayName" xml:space="preserve">
    <value>EnterDisplayName</value>
  </data>
  <data name="RedirectUrl" xml:space="preserve">
    <value>RedirectUrl</value>
  </data>
  <data name="EnterRedirectUrl" xml:space="preserve">
    <value>EnterRedirectUrl</value>
  </data>
  <data name="CustomMessage" xml:space="preserve">
    <value>CustomMessage</value>
  </data>
  <data name="EnterCustomMessage" xml:space="preserve">
    <value>EnterCustomMessage</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="DeleteAccount" xml:space="preserve">
    <value>DeleteAccount</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Given Name" xml:space="preserve">
    <value>GivenName</value>
  </data>
  <data name="Patients" xml:space="preserve">
    <value>Patients</value>
  </data>
  <data name="Who" xml:space="preserve">
    <value>Who</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Choices" xml:space="preserve">
    <value>Choices</value>
  </data>
  <data name="Employer" xml:space="preserve">
    <value>Employer</value>
  </data>
  <data name="Stats" xml:space="preserve">
    <value>Stats</value>
  </data>
  <data name="Misc" xml:space="preserve">
    <value>Misc</value>
  </data>
  <data name="Guardian" xml:space="preserve">
    <value>Guardian</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>Insurance</value>
  </data>
  <data name="AmbientSolutionRoute" xml:space="preserve">
    <value>/ambientsolution</value>
  </data>
  <data name="LoadingUserData" xml:space="preserve">
    <value>LoadingUserData</value>
  </data>
  <data name="UserDataNotLoaded" xml:space="preserve">
    <value>UserDataNotLoaded</value>
  </data>
  <data name="EditUser" xml:space="preserve">
    <value>EditUser</value>
  </data>
  <data name="UserInformationForm" xml:space="preserve">
    <value>UserInformationForm</value>
  </data>
  <data name="UserInformationFormHeader" xml:space="preserve">
    <value>UserInformationFormHeader</value>
  </data>
  <data name="UserID" xml:space="preserve">
    <value>UserID</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>UserName</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>FirstName</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>LastName</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>PhoneNumber</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>DateOfBirth</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="TeyaHealth" xml:space="preserve">
    <value>Teya Health</value>
  </data>
  <data name="SearchUsername" xml:space="preserve">
    <value>Search by Username</value>
  </data>
  <data name="AddMember" xml:space="preserve">
    <value>Add Member</value>
  </data>
  <data name="NoMemberSelected" xml:space="preserve">
    <value>NoMemberSelected</value>
  </data>
  <data name="PatientsPagePath" xml:space="preserve">
    <value>/patients/</value>
  </data>
  <data name="PatientsPathTemplate" xml:space="preserve">
    <value>/patients/{0}</value>
  </data>
  <data name="ManagYourTeyaAccount" xml:space="preserve">
    <value>Manage Your Teya Account</value>
  </data>
  <data name="SignOut" xml:space="preserve">
    <value>Sign Out</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>Ok</value>
  </data>
  <data name="GusestUser" xml:space="preserve">
    <value>U</value>
  </data>
  <data name="ManageProfile" xml:space="preserve">
    <value>/manageprofile</value>
  </data>
  <data name="authentication/logout" xml:space="preserve">
    <value>authentication/logout</value>
  </data>
  <data name="UsernameClaim" xml:space="preserve">
    <value>preferred_username</value>
  </data>
  <data name="NameClaim" xml:space="preserve">
    <value>name</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>stop_circle</value>
  </data>
  <data name="Mic" xml:space="preserve">
    <value>mic</value>
  </data>
  <data name="Play" xml:space="preserve">
    <value>play_arrow</value>
  </data>
  <data name="Pause" xml:space="preserve">
    <value>pause</value>
  </data>
  <data name="NoUsersFound" xml:space="preserve">
    <value>NoUsersFound</value>
  </data>
  <data name="SearchUsers" xml:space="preserve">
    <value>SearchUsers</value>
  </data>
  <data name="SearchByEmail" xml:space="preserve">
    <value>SearchByEmail</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="InviteUserPath" xml:space="preserve">
    <value>/inviteuser</value>
  </data>
  <data name="UpdateUserPath" xml:space="preserve">
    <value>/updateuser</value>
  </data>
  <data name="UserManagementHeading" xml:space="preserve">
    <value>UserManagement</value>
  </data>
  <data name="ManageId" xml:space="preserve">
    <value>id</value>
  </data>
  <data name="ManageDisplayName" xml:space="preserve">
    <value>displayName</value>
  </data>
  <data name="ManageGivenName" xml:space="preserve">
    <value>givenName</value>
  </data>
  <data name="ManageSurname" xml:space="preserve">
    <value>surname</value>
  </data>
  <data name="ManageMail" xml:space="preserve">
    <value>mail</value>
  </data>
  <data name="ManageUserType" xml:space="preserve">
    <value>userType</value>
  </data>
  <data name="ManageJobTitle" xml:space="preserve">
    <value>jobTitle</value>
  </data>
  <data name="ManageCompanyName" xml:space="preserve">
    <value>companyName</value>
  </data>
  <data name="ManageDepartment" xml:space="preserve">
    <value>department</value>
  </data>
  <data name="ManageOfficeLocation" xml:space="preserve">
    <value>officeLocation</value>
  </data>
  <data name="ManageMobilePhone" xml:space="preserve">
    <value>mobilePhone</value>
  </data>
  <data name="ManageStreetAddress" xml:space="preserve">
    <value>streetAddress</value>
  </data>
  <data name="ManageCity" xml:space="preserve">
    <value>city</value>
  </data>
  <data name="ManageState" xml:space="preserve">
    <value>state</value>
  </data>
  <data name="ManagePostalCode" xml:space="preserve">
    <value>postalCode</value>
  </data>
  <data name="ManageCountry" xml:space="preserve">
    <value>country</value>
  </data>
  <data name="ManageExtensionCompanyName" xml:space="preserve">
    <value>extensionCompanyName</value>
  </data>
  <data name="ErrorDeletingMember" xml:space="preserve">
    <value>ErrorDeletingMember</value>
  </data>
  <data name="MemberDeleted" xml:space="preserve">
    <value>MemberDeleted</value>
  </data>
  <data name="ErrorSubmittingMemberData" xml:space="preserve">
    <value>ErrorSubmittingMemberData</value>
  </data>
  <data name="MemberUpdated" xml:space="preserve">
    <value>MemberUpdated</value>
  </data>
  <data name="MemberRegistered" xml:space="preserve">
    <value>MemberRegistered</value>
  </data>
  <data name="RecordsUrl" xml:space="preserve">
    <value>RecordsUrl</value>
  </data>
  <data name="AudioBaseUrl" xml:space="preserve">
    <value>AudioBaseUrl</value>
  </data>
  <data name="TeyaRecorder" xml:space="preserve">
    <value>Teya Recorder</value>
  </data>
  <data name="EmployerDetails" xml:space="preserve">
    <value>Employer Details</value>
  </data>
  <data name="GuardianDetails" xml:space="preserve">
    <value>Guardian Details</value>
  </data>
  <data name="StatsDetails" xml:space="preserve">
    <value>Stats Details</value>
  </data>
  <data name="InsuranceDetails" xml:space="preserve">
    <value>Insurance Details</value>
  </data>
  <data name="MiscellaneousDetails" xml:space="preserve">
    <value>Miscellaneous Details</value>
  </data>
  <data name="ExceptionOccurred" xml:space="preserve">
    <value>ExceptionOccurred</value>
  </data>
  <data name="GetByIdFailed" xml:space="preserve">
    <value>GetByIdFailed</value>
  </data>
  <data name="ErrorFetchingMemberById" xml:space="preserve">
    <value>ErrorFetchingMemberById</value>
  </data>
  <data name="FailedToUpdateUser" xml:space="preserve">
    <value>FailedToUpdateUser</value>
  </data>
  <data name="PRODUCT" xml:space="preserve">
    <value>PRODUCT</value>
  </data>
  <data name="EHRINTEGRATIONS" xml:space="preserve">
    <value>EHR INTEGRATIONS</value>
  </data>
  <data name="ABOUT" xml:space="preserve">
    <value>ABOUT</value>
  </data>
  <data name="CONTACTUS" xml:space="preserve">
    <value>CONTACT US</value>
  </data>
  <data name="LOGIN" xml:space="preserve">
    <value>LOGIN</value>
  </data>
  <data name="OrganizationDeletedSuccessfully" xml:space="preserve">
    <value>OrganizationDeletedSuccessfully</value>
  </data>
  <data name="ErrorDeletingOrganization" xml:space="preserve">
    <value>ErrorDeletingOrganization</value>
  </data>
  <data name="InvalidOrganization" xml:space="preserve">
    <value>InvalidOrganization</value>
  </data>
  <data name="ErrorUpdatingOrganization" xml:space="preserve">
    <value>ErrorUpdatingOrganization</value>
  </data>
  <data name="OrganizationRegistration" xml:space="preserve">
    <value>OrganizationRegistration</value>
  </data>
  <data name="ErrorFetchingOrganizationsForProduct" xml:space="preserve">
    <value>ErrorFetchingOrganizationsForProduct</value>
  </data>
  <data name="ErrorFetchingAllOrganizations" xml:space="preserve">
    <value>ErrorFetchingAllOrganizations</value>
  </data>
  <data name="ErrorFetchingOrganizationById" xml:space="preserve">
    <value>ErrorFetchingOrganizationById</value>
  </data>
  <data name="OrganizationAlreadyExists" xml:space="preserve">
    <value>OrganizationAlreadyExists</value>
  </data>
  <data name="RoleAlreadyExists" xml:space="preserve">
    <value>RoleAlreadyExists</value>
  </data>
  <data name="RoleRegistrationError" xml:space="preserve">
    <value>RoleRegistrationError</value>
  </data>
  <data name="ErrorFetchingRoleById" xml:space="preserve">
    <value>ErrorFetchingRoleById</value>
  </data>
  <data name="ErrorFetchingAllRoles" xml:space="preserve">
    <value>ErrorFetchingAllRoles</value>
  </data>
  <data name="RoleDeletedSuccessfully" xml:space="preserve">
    <value>RoleDeletedSuccessfully</value>
  </data>
  <data name="RoleDeletionFailed" xml:space="preserve">
    <value>RoleDeletionFailed</value>
  </data>
  <data name="ErrorDeletingRole" xml:space="preserve">
    <value>ErrorDeletingRole</value>
  </data>
  <data name="InvalidRole" xml:space="preserve">
    <value>InvalidRole</value>
  </data>
  <data name="ErrorUpdatingRole" xml:space="preserve">
    <value>ErrorUpdatingRole</value>
  </data>
  <data name="ErrorFetchingOrganizations" xml:space="preserve">
    <value>ErrorFetchingOrganizations</value>
  </data>
  <data name="ErrorFetchingFacilities" xml:space="preserve">
    <value>ErrorFetchingFacilities</value>
  </data>
  <data name="OrganizationRegistered" xml:space="preserve">
    <value>OrganizationRegistered</value>
  </data>
  <data name="ErrorSubmittingOrganizationData" xml:space="preserve">
    <value>ErrorSubmittingOrganizationData</value>
  </data>
  <data name="RoleRegistered" xml:space="preserve">
    <value>RoleRegistered</value>
  </data>
  <data name="ErrorSubmittingRoleData" xml:space="preserve">
    <value>ErrorSubmittingRoleData</value>
  </data>
  <data name="FacilityRegistered" xml:space="preserve">
    <value>FacilityRegistered</value>
  </data>
  <data name="ErrorSubmittingFacilityData" xml:space="preserve">
    <value>ErrorSubmittingFacilityData</value>
  </data>
  <data name="FacilityAlreadyExists" xml:space="preserve">
    <value>FacilityAlreadyExists</value>
  </data>
  <data name="FacilityRegistrationError" xml:space="preserve">
    <value>FacilityRegistrationError</value>
  </data>
  <data name="ErrorFetchingFacilityById" xml:space="preserve">
    <value>ErrorFetchingFacilityById</value>
  </data>
  <data name="ErrorFetchingAllFacilities" xml:space="preserve">
    <value>ErrorFetchingAllFacilities</value>
  </data>
  <data name="FacilityDeletedSuccessfully" xml:space="preserve">
    <value>FacilityDeletedSuccessfully</value>
  </data>
  <data name="FacilityDeletionFailed" xml:space="preserve">
    <value>FacilityDeletionFailed</value>
  </data>
  <data name="ErrorDeletingFacility" xml:space="preserve">
    <value>ErrorDeletingFacility</value>
  </data>
  <data name="InvalidFacility" xml:space="preserve">
    <value>InvalidFacility</value>
  </data>
  <data name="ErrorUpdatingFacility" xml:space="preserve">
    <value>ErrorUpdatingFacility</value>
  </data>
  <data name="NavigateToUserPage" xml:space="preserve">
    <value>/usercreation</value>
  </data>
  <data name="NavigateToExternalUserPage" xml:space="preserve">
    <value>/externalusercreation</value>
  </data>
  <data name="NavigateToSendMailPage" xml:space="preserve">
    <value>/sendmail</value>
  </data>
  <data name="NavigateToUserManagementPage" xml:space="preserve">
    <value>/usermanagement</value>
  </data>
  <data name="active-tab" xml:space="preserve">
    <value>active-tab</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Roles</value>
  </data>
  <data name="Facilities" xml:space="preserve">
    <value>Facilities</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Organizations</value>
  </data>
  <data name="Error loading organizations:" xml:space="preserve">
    <value>Error loading organizations:</value>
  </data>
  <data name="Inactive Organization:" xml:space="preserve">
    <value>Inactive Organization:</value>
  </data>
  <data name="EmailSenderAddress" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="EmailSubject" xml:space="preserve">
    <value>Welcome to Teya Health</value>
  </data>
  <data name="EmailBody" xml:space="preserve">
    <value>Hello, your account has been activated!</value>
  </data>
  <data name="PageTitle" xml:space="preserve">
    <value>Welcome to Teya Health!</value>
  </data>
  <data name="Greeting" xml:space="preserve">
    <value>Dear</value>
  </data>
  <data name="AccountCreationMessage" xml:space="preserve">
    <value>Congratulations on creating your account with Teya Health! We’re thrilled to have you on board as we revolutionize the healthcare experience using AI technology. Our innovative products, especially for generating SOAP notes, will make your daily routine simpler and more efficient.</value>
  </data>
  <data name="AIProductsMessage" xml:space="preserve">
    <value>With Teya Health, you’ll have access to cutting-edge AI tools that automate mundane tasks, giving you more time to focus on what really matters—caring for your patients and streamlining your workflow.</value>
  </data>
  <data name="EfficiencyMessage" xml:space="preserve">
    <value>Unleash the full potential of our products now! Your daily tasks are about to become easier, faster, and more efficient.</value>
  </data>
  <data name="LoginCTA" xml:space="preserve">
    <value>Log in to your account</value>
  </data>
  <data name="SupportMessage" xml:space="preserve">
    <value>If you have any questions, feel free to reach out to our support <NAME_EMAIL>.</value>
  </data>
  <data name="ThankYouMessage" xml:space="preserve">
    <value>Thank you for choosing Teya Health!</value>
  </data>
  <data name="InvalidEmailMessage" xml:space="preserve">
    <value>Invalid email input correct format</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Welcome to Teya Health!</value>
  </data>
  <data name="WelcomeHeading" xml:space="preserve">
    <value>Welcome to Teya Health!</value>
  </data>
  <data name="LoginButtonText" xml:space="preserve">
    <value>Login Here</value>
  </data>
  <data name="LoginButtonText1" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="RandomPasswordChars" xml:space="preserve">
    <value>ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&amp;*()</value>
  </data>
  <data name="GreetingMessage" xml:space="preserve">
    <value>Dear</value>
  </data>
  <data name="CongratulationsMessage" xml:space="preserve">
    <value>Congratulations on creating your account with Teya Health! We’re thrilled to have you on board.</value>
  </data>
  <data name="BenefitsMessage" xml:space="preserve">
    <value>With Teya Health, you’ll have access to cutting-edge AI tools that simplify your daily workflow.</value>
  </data>
  <data name="CallToActionMessage" xml:space="preserve">
    <value>Unleash the full potential of our products now!</value>
  </data>
  <data name="LoginDetailsMessage" xml:space="preserve">
    <value>Your login details are as follows:</value>
  </data>
  <data name="EmailLabel" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="PasswordLabel" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="PatientName" xml:space="preserve">
    <value>string.Empty</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>DateTime.MinValue</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>DateTime.MinValue</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>string.Empty</value>
  </data>
  <data name="SearchCriteria" xml:space="preserve">
    <value>null</value>
  </data>
  <data name="SearchUser" xml:space="preserve">
    <value>string.Empty</value>
  </data>
  <data name="Facility " xml:space="preserve">
    <value>null</value>
  </data>
  <data name="Provider" xml:space="preserve">
    <value>null</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>DateTime.MinValue</value>
  </data>
  <data name="VisitType" xml:space="preserve">
    <value>null</value>
  </data>
  <data name="VisitStatus" xml:space="preserve">
    <value>null</value>
  </data>
  <data name="Reason " xml:space="preserve">
    <value>string.Empty</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="Arrived" xml:space="preserve">
    <value>Arrived</value>
  </data>
  <data name="In Progress" xml:space="preserve">
    <value>In Progress</value>
  </data>
  <data name="ReScheduled" xml:space="preserve">
    <value>ReScheduled</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="AppointmentRetrievalFailure" xml:space="preserve">
    <value>AppointmentRetrievalFailure</value>
  </data>
  <data name="ErrorFetchingProviderTypes" xml:space="preserve">
    <value>ErrorFetchingProviderTypes</value>
  </data>
  <data name="ErrorFetchingVisitTypes" xml:space="preserve">
    <value>ErrorFetchingVisitTypes</value>
  </data>
  <data name="An error occurred while fetching visit type names." xml:space="preserve">
    <value>An error occurred while fetching visit type names.</value>
  </data>
  <data name="AccessTokenNotFound" xml:space="preserve">
    <value>AccessTokenNotFound</value>
  </data>
  <data name="ErrorFetchingFacilityNames" xml:space="preserve">
    <value>ErrorFetchingFacilityNames</value>
  </data>
  <data name="SelectProvider" xml:space="preserve">
    <value>SelectProvider</value>
  </data>
  <data name="AddAppointment" xml:space="preserve">
    <value>AddAppointment</value>
  </data>
  <data name="SearchBy:" xml:space="preserve">
    <value>SearchBy:</value>
  </data>
  <data name="SearchUser:" xml:space="preserve">
    <value>SearchUser:</value>
  </data>
  <data name="EnterSearchTerm" xml:space="preserve">
    <value>EnterSearchTerm</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="NoUserFound" xml:space="preserve">
    <value>NoUserFound</value>
  </data>
  <data name="Facility:" xml:space="preserve">
    <value>Facility:</value>
  </data>
  <data name="SelectFacility" xml:space="preserve">
    <value>SelectFacility</value>
  </data>
  <data name="Provider:" xml:space="preserve">
    <value>Provider:</value>
  </data>
  <data name="Date:" xml:space="preserve">
    <value>Date:</value>
  </data>
  <data name="StartTime:" xml:space="preserve">
    <value>StartTime:</value>
  </data>
  <data name="EndTime:" xml:space="preserve">
    <value>EndTime:</value>
  </data>
  <data name="VisitType:" xml:space="preserve">
    <value>VisitType:</value>
  </data>
  <data name="SelectVisitType" xml:space="preserve">
    <value>SelectVisitType</value>
  </data>
  <data name="VisitStatus:" xml:space="preserve">
    <value>VisitStatus:</value>
  </data>
  <data name="SelectVisitStatus" xml:space="preserve">
    <value>SelectVisitStatus</value>
  </data>
  <data name="Reason:" xml:space="preserve">
    <value>Reason:</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>EnterReason</value>
  </data>
  <data name="Notes:" xml:space="preserve">
    <value>Notes:</value>
  </data>
  <data name="EnterNotes" xml:space="preserve">
    <value>EnterNotes</value>
  </data>
  <data name="SSN" xml:space="preserve">
    <value>SSN</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Res1UName" xml:space="preserve">
    <value>Username Required</value>
  </data>
  <data name="Res2UName" xml:space="preserve">
    <value>Username Invalid</value>
  </data>
  <data name="Res1FName" xml:space="preserve">
    <value>First Name Required</value>
  </data>
  <data name="Res2FName" xml:space="preserve">
    <value>First Name Invalid</value>
  </data>
  <data name="Res1LName" xml:space="preserve">
    <value>Last Name Required</value>
  </data>
  <data name="Res2LName" xml:space="preserve">
    <value>Last Name Invalid</value>
  </data>
  <data name="Res1Field" xml:space="preserve">
    <value>Field Required</value>
  </data>
  <data name="Res2Field" xml:space="preserve">
    <value>Field Invalid</value>
  </data>
  <data name="Res1PCode" xml:space="preserve">
    <value>Postal Code Required</value>
  </data>
  <data name="Res2PCode" xml:space="preserve">
    <value>Postal Code Invalid</value>
  </data>
  <data name="Res1Email" xml:space="preserve">
    <value>Email Required</value>
  </data>
  <data name="Res2Email" xml:space="preserve">
    <value>Invalid Email</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="SearchDN" xml:space="preserve">
    <value>Search Drug Names</value>
  </data>
  <data name="Allergies" xml:space="preserve">
    <value>Allergies</value>
  </data>
  <data name="AllergyCol1" xml:space="preserve">
    <value>Classification</value>
  </data>
  <data name="AllergyCol2" xml:space="preserve">
    <value>Agent</value>
  </data>
  <data name="AllergyCol3" xml:space="preserve">
    <value>Reaction</value>
  </data>
  <data name="AllergyCol4" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="AllergyCol5" xml:space="preserve">
    <value>DrugName</value>
  </data>
  <data name="Please fill all required fields." xml:space="preserve">
    <value>Please fill all required fields.</value>
  </data>
  <data name="ErrorFetchingAllPageRoleMappings" xml:space="preserve">
    <value>ErrorFetchingAllPageRoleMappings</value>
  </data>
  <data name="ErrorFetchingPageRoleMappingById" xml:space="preserve">
    <value>ErrorFetchingPageRoleMappingById</value>
  </data>
  <data name="AddPageRoleMappingFailed" xml:space="preserve">
    <value>AddPageRoleMappingFailed</value>
  </data>
  <data name="ErrorAddingPageRoleMapping" xml:space="preserve">
    <value>ErrorAddingPageRoleMapping</value>
  </data>
  <data name="PageRoleMappingUpdatedSuccessfully" xml:space="preserve">
    <value>PageRoleMappingUpdatedSuccessfully</value>
  </data>
  <data name="PageRoleMappingUpdateFailed" xml:space="preserve">
    <value>PageRoleMappingUpdateFailed</value>
  </data>
  <data name="ErrorUpdatingPageRoleMapping" xml:space="preserve">
    <value>ErrorUpdatingPageRoleMapping</value>
  </data>
  <data name="PageRoleMappingDeletedSuccessfully" xml:space="preserve">
    <value>PageRoleMappingDeletedSuccessfully</value>
  </data>
  <data name="PageRoleMappingDeletionFailed" xml:space="preserve">
    <value>PageRoleMappingDeletionFailed</value>
  </data>
  <data name="ErrorDeletingPageRoleMapping" xml:space="preserve">
    <value>ErrorDeletingPageRoleMapping</value>
  </data>
  <data name="GetPagesByRoleIdFailed" xml:space="preserve">
    <value>GetPagesByRoleIdFailed</value>
  </data>
  <data name="ErrorFetchingPagesByRoleId" xml:space="preserve">
    <value>ErrorFetchingPagesByRoleId</value>
  </data>
  <data name="PagePathRequired" xml:space="preserve">
    <value>PagePathRequired</value>
  </data>
  <data name="PagePathRequiredMessage" xml:space="preserve">
    <value>PagePathRequiredMessage</value>
  </data>
  <data name="RolesNotFoundMessage" xml:space="preserve">
    <value>RolesNotFoundMessage</value>
  </data>
  <data name="Tasks" xml:space="preserve">
    <value>Tasks</value>
  </data>
  <data name="OfficeVisit" xml:space="preserve">
    <value>OfficeVisit</value>
  </data>
  <data name="Practice" xml:space="preserve">
    <value>Practice</value>
  </data>
  <data name="Visit Type" xml:space="preserve">
    <value>Visit Type</value>
  </data>
  <data name="Appointment Time" xml:space="preserve">
    <value>Appointment Time</value>
  </data>
  <data name="Patient Name" xml:space="preserve">
    <value>Patient Name</value>
  </data>
  <data name="P/R" xml:space="preserve">
    <value>P/R</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="Sex" xml:space="preserve">
    <value>Sex</value>
  </data>
  <data name="Date of Birth" xml:space="preserve">
    <value>Date of Birth</value>
  </data>
  <data name="Visit Status" xml:space="preserve">
    <value>Visit Status</value>
  </data>
  <data name="Arrival Time" xml:space="preserve">
    <value>Arrival Time</value>
  </data>
  <data name="Duration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="Room Number" xml:space="preserve">
    <value>Room Number</value>
  </data>
  <data name="View Log" xml:space="preserve">
    <value>View Log</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="Web View" xml:space="preserve">
    <value>Web View</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="AllergyServiceError1" xml:space="preserve">
    <value>TasksRetrievalFailure</value>
  </data>
  <data name="AllergyServiceError2" xml:space="preserve">
    <value>AddressRetrievalFailure</value>
  </data>
  <data name="RxNormError1" xml:space="preserve">
    <value>Unexpected error in getting drug names</value>
  </data>
  <data name="AllergiesMsg1" xml:space="preserve">
    <value>Please select drug name</value>
  </data>
  <data name="SurName1" xml:space="preserve">
    <value>surname</value>
  </data>
  <data name="User profile" xml:space="preserve">
    <value>User profile</value>
  </data>
  <data name="AddressRetrievalFailure" xml:space="preserve">
    <value>AddressRetrievalFailure</value>
  </data>
  <data name="InvalidPatientIds" xml:space="preserve">
    <value>InvalidPatientIds</value>
  </data>
  <data name="BP" xml:space="preserve">
    <value>BP</value>
  </data>
  <data name="Weight" xml:space="preserve">
    <value>Weight</value>
  </data>
  <data name="PCP" xml:space="preserve">
    <value>PCP</value>
  </data>
  <data name="Policy" xml:space="preserve">
    <value>Policy</value>
  </data>
  <data name="Medical Summary" xml:space="preserve">
    <value>Medical Summary</value>
  </data>
  <data name="Labs" xml:space="preserve">
    <value>Labs</value>
  </data>
  <data name="DI" xml:space="preserve">
    <value>DI</value>
  </data>
  <data name="Procedures" xml:space="preserve">
    <value>Procedures</value>
  </data>
  <data name="Growth Chart" xml:space="preserve">
    <value>Growth Chart</value>
  </data>
  <data name="Immunization" xml:space="preserve">
    <value>Immunization</value>
  </data>
  <data name="Encounter Notes" xml:space="preserve">
    <value>Encounter Notes</value>
  </data>
  <data name="Patient Docs" xml:space="preserve">
    <value>Patient Docs</value>
  </data>
  <data name="Office Visits" xml:space="preserve">
    <value>Office Visits</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="Minutes" xml:space="preserve">
    <value>Minutes</value>
  </data>
  <data name="R-1111" xml:space="preserve">
    <value>R-1111</value>
  </data>
  <data name="An error occurred while fetching visit Status names." xml:space="preserve">
    <value>An error occurred while fetching visit Status names.</value>
  </data>
  <data name="ErrorFetchingVisitStatus" xml:space="preserve">
    <value>ErrorFetchingVisitStatus</value>
  </data>
  <data name="Add2" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="SelectRole" xml:space="preserve">
    <value>SelectRole</value>
  </data>
  <data name="AdminRole" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="CreateFirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>Middle Name</value>
  </data>
  <data name="CreateLastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="FederalTaxId" xml:space="preserve">
    <value>Federal TaxId</value>
  </data>
  <data name="NewCropeRxRole" xml:space="preserve">
    <value>NewCrop ex Role</value>
  </data>
  <data name="AdditionalInfo" xml:space="preserve">
    <value>Additional Info</value>
  </data>
  <data name="SelectProviderType" xml:space="preserve">
    <value>Select Provider Type</value>
  </data>
  <data name="PatientMenuRole" xml:space="preserve">
    <value>Patient Menu Role</value>
  </data>
  <data name="SearchOrganization" xml:space="preserve">
    <value>Search Organization</value>
  </data>
  <data name="DefaultBillingFacility" xml:space="preserve">
    <value>Default Billing Facility</value>
  </data>
  <data name="DEANumber" xml:space="preserve">
    <value>DEA number</value>
  </data>
  <data name="MenuPageRoute" xml:space="preserve">
    <value>/Menu</value>
  </data>
  <data name="AllergyCol6" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="ErrorAddingVisitType" xml:space="preserve">
    <value>ErrorAddingVisitType</value>
  </data>
  <data name="Current Medication" xml:space="preserve">
    <value>Current Medication</value>
  </data>
  <data name="Search Brand Names" xml:space="preserve">
    <value>Search Brand Names</value>
  </data>
  <data name="Dosage &amp; InTake" xml:space="preserve">
    <value>Dosage &amp; InTake</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="VisitName" xml:space="preserve">
    <value>VisitName</value>
  </data>
  <data name="CPTCode" xml:space="preserve">
    <value>CPTCode</value>
  </data>
  <data name="ErrorDeletingVisitType" xml:space="preserve">
    <value>ErrorDeletingVisitType</value>
  </data>
  <data name="Drug Details" xml:space="preserve">
    <value>Drug Details</value>
  </data>
  <data name="Brand Name" xml:space="preserve">
    <value>Brand Name</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="Edit Medication" xml:space="preserve">
    <value>Edit Medication</value>
  </data>
  <data name="End Date" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="DEAInvalidFormat" xml:space="preserve">
    <value>DEAInvalidFormat", "Format: 2 letters followed by 7 digits (e.g., AB1234567).</value>
  </data>
  <data name="AllergiesGridField0" xml:space="preserve">
    <value>MedicineId</value>
  </data>
  <data name="AllergiesGridField1" xml:space="preserve">
    <value>Classification</value>
  </data>
  <data name="AllergiesGridField2" xml:space="preserve">
    <value>Agent</value>
  </data>
  <data name="AllergiesGridField3" xml:space="preserve">
    <value>Reaction</value>
  </data>
  <data name="AllergiesGridField4" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="AllergiesGridField5" xml:space="preserve">
    <value>DrugName</value>
  </data>
  <data name="AddressLine1" xml:space="preserve">
    <value>Address Line 1</value>
  </data>
  <data name="AddressLine2" xml:space="preserve">
    <value>Address Line 2</value>
  </data>
  <data name="ReasonDeceased" xml:space="preserve">
    <value>Reason Deceased</value>
  </data>
  <data name="DateDeceased" xml:space="preserve">
    <value>Date Deceased</value>
  </data>
  <data name="AddressDetails" xml:space="preserve">
    <value>Address Details</value>
  </data>
  <data name="PrimaryInsuranceProvider" xml:space="preserve">
    <value>Primary Insurance Provider</value>
  </data>
  <data name="PlanName" xml:space="preserve">
    <value>Plan Name</value>
  </data>
  <data name="Subscriber" xml:space="preserve">
    <value>Subscriber</value>
  </data>
  <data name="EffectiveDate" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="Relationship" xml:space="preserve">
    <value>Relationship</value>
  </data>
  <data name="PolicyNumber" xml:space="preserve">
    <value>Policy Number</value>
  </data>
  <data name="GroupNumber" xml:space="preserve">
    <value>Group Number</value>
  </data>
  <data name="AcceptAssignment" xml:space="preserve">
    <value>Accept Assignment</value>
  </data>
  <data name="CoPay" xml:space="preserve">
    <value>Co-Pay</value>
  </data>
  <data name="SubscriberEmployer" xml:space="preserve">
    <value>Subscriber Employer</value>
  </data>
  <data name="SubscriberAddressLine1" xml:space="preserve">
    <value>Subscriber Address Line 1</value>
  </data>
  <data name="SubscriberAddressLine2" xml:space="preserve">
    <value>Subscriber Address Line 2</value>
  </data>
  <data name="SubscriberCity" xml:space="preserve">
    <value>Subscriber City</value>
  </data>
  <data name="SubscriberState" xml:space="preserve">
    <value>Subscriber State</value>
  </data>
  <data name="SubscriberZipCode" xml:space="preserve">
    <value>Subscriber Zip Code</value>
  </data>
  <data name="SubscriberCountry" xml:space="preserve">
    <value>Subscriber Country</value>
  </data>
  <data name="SubscriberPhone" xml:space="preserve">
    <value>Subscriber Phone</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="Ethnicity" xml:space="preserve">
    <value>Ethnicity</value>
  </data>
  <data name="Race" xml:space="preserve">
    <value>Race</value>
  </data>
  <data name="Nationality" xml:space="preserve">
    <value>Nationality</value>
  </data>
  <data name="FamilySize" xml:space="preserve">
    <value>Family Size</value>
  </data>
  <data name="FinancialReviewDate" xml:space="preserve">
    <value>Financial Review Date</value>
  </data>
  <data name="MonthlyIncome" xml:space="preserve">
    <value>Monthly Income</value>
  </data>
  <data name="ReferralSource" xml:space="preserve">
    <value>Referral Source</value>
  </data>
  <data name="Religion" xml:space="preserve">
    <value>Religion</value>
  </data>
  <data name="NoChangesDetected" xml:space="preserve">
    <value>No changes detected</value>
  </data>
  <data name="AuthServiceUpdateFailed" xml:space="preserve">
    <value>AuthServiceUpdateFailed</value>
  </data>
  <data name="ClassificationRequired" xml:space="preserve">
    <value>Classification is required.</value>
  </data>
  <data name="ClassificationInvalid" xml:space="preserve">
    <value>Classification must contain alphabets only.</value>
  </data>
  <data name="AgentRequired" xml:space="preserve">
    <value>Agent is required.</value>
  </data>
  <data name="AgentInvalid" xml:space="preserve">
    <value>Agent must contain alphabets only.</value>
  </data>
  <data name="ReactionRequired" xml:space="preserve">
    <value>Reaction is required.</value>
  </data>
  <data name="ReactionInvalid" xml:space="preserve">
    <value>Reaction must contain alphabets only.</value>
  </data>
  <data name="TypeRequired" xml:space="preserve">
    <value>Type is required.</value>
  </data>
  <data name="TypeInvalid" xml:space="preserve">
    <value>Type must be alphanumeric or contains hyphens.</value>
  </data>
  <data name="BackdropDisabledMessage" xml:space="preserve">
    <value>Backdrop is Disabled! - When Dialog was Opened</value>
  </data>
  <data name="ReferralOutgoing" xml:space="preserve">
    <value>ReferralOutgoing</value>
  </data>
  <data name="Date Added" xml:space="preserve">
    <value>Date Added</value>
  </data>
  <data name="Treatment Plan" xml:space="preserve">
    <value>Treatment Plan</value>
  </data>
  <data name="Medications" xml:space="preserve">
    <value>Medications</value>
  </data>
  <data name="Specialist Referral" xml:space="preserve">
    <value>Specialist Referral</value>
  </data>
  <data name="Referral Reason" xml:space="preserve">
    <value>Referral Reason</value>
  </data>
  <data name="UploadPatientPhoto" xml:space="preserve">
    <value>Upload Patient Photo</value>
  </data>
  <data name="FailedAddingUser" xml:space="preserve">
    <value>Failed Adding User</value>
  </data>
  <data name="Assessments" xml:space="preserve">
    <value>Assessments</value>
  </data>
  <data name="Updated Date" xml:space="preserve">
    <value>Updated Date</value>
  </data>
  <data name="Physical Therapy" xml:space="preserve">
    <value>Physical Therapy</value>
  </data>
  <data name="Therapy Assessment" xml:space="preserve">
    <value>Therapy Assessment</value>
  </data>
  <data name="Short Term Goals" xml:space="preserve">
    <value>Short Term Goals</value>
  </data>
  <data name="Long Term Goals" xml:space="preserve">
    <value>Long Term Goals</value>
  </data>
  <data name="Physical Therapy Diagnosis" xml:space="preserve">
    <value>Physical Therapy Diagnosis</value>
  </data>
  <data name="Physical Therapy Program" xml:space="preserve">
    <value>Physical Therapy Program</value>
  </data>
  <data name="Disabilities" xml:space="preserve">
    <value>Disabilities</value>
  </data>
  <data name="Functionals" xml:space="preserve">
    <value>Functionals</value>
  </data>
  <data name="Limitations" xml:space="preserve">
    <value>Limitations</value>
  </data>
  <data name="Referral From" xml:space="preserve">
    <value>Referral From</value>
  </data>
  <data name="Referral To" xml:space="preserve">
    <value>Referral To</value>
  </data>
  <data name="Tests" xml:space="preserve">
    <value>Tests</value>
  </data>
  <data name="Referral Outgoing" xml:space="preserve">
    <value>Referral Outgoing</value>
  </data>
  <data name="Prescription Medication" xml:space="preserve">
    <value>Prescription Medication</value>
  </data>
  <data name="Therapeutic Interventions" xml:space="preserve">
    <value>Therapeutic Interventions</value>
  </data>
  <data name="Search Therapy" xml:space="preserve">
    <value>Search Therapy</value>
  </data>
  <data name="Therapy Type" xml:space="preserve">
    <value>Therapy Type</value>
  </data>
  <data name="Error retrieving TherapeuticInterventionsList codes:" xml:space="preserve">
    <value>Error retrieving TherapeuticInterventionsList codes:</value>
  </data>
  <data name="TherapeuticInterventionsListCodeRetrievalFailure" xml:space="preserve">
    <value>TherapeuticInterventionsListCodeRetrievalFailure</value>
  </data>
  <data name="Invalid Appointment Date or Time" xml:space="preserve">
    <value>Invalid Appointment Date or Time</value>
  </data>
  <data name="New Appointment" xml:space="preserve">
    <value>New Appointment</value>
  </data>
  <data name="Search Assessments" xml:space="preserve">
    <value>Search Assessments</value>
  </data>
  <data name="ErrorFetchingDiagnosis" xml:space="preserve">
    <value>ErrorFetchingDiagnosis</value>
  </data>
  <data name="Patient Address" xml:space="preserve">
    <value>Patient Address</value>
  </data>
  <data name="Doctor Name" xml:space="preserve">
    <value>Doctor Name</value>
  </data>
  <data name="Pharmacy Name" xml:space="preserve">
    <value>Pharmacy Name</value>
  </data>
  <data name="Pharmacy Address" xml:space="preserve">
    <value>Pharmacy Address</value>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="Rx Eligibility" xml:space="preserve">
    <value>Rx Eligibility</value>
  </data>
  <data name="Patient Hub" xml:space="preserve">
    <value>Patient Hub</value>
  </data>
  <data name="Rx External History" xml:space="preserve">
    <value>Rx External History</value>
  </data>
  <data name="Link Pharmacy" xml:space="preserve">
    <value>Link Pharmacy</value>
  </data>
  <data name="E-Prescription Type" xml:space="preserve">
    <value>E-Prescription Type</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="Ret-Rx" xml:space="preserve">
    <value>Ret-Rx</value>
  </data>
  <data name="Response" xml:space="preserve">
    <value>Response</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>Denied</value>
  </data>
  <data name="Denied/New Rx to follow" xml:space="preserve">
    <value>Denied/New Rx to follow</value>
  </data>
  <data name="Refill Details" xml:space="preserve">
    <value>Refill Details</value>
  </data>
  <data name="Show Preview RX" xml:space="preserve">
    <value>Show Preview RX</value>
  </data>
  <data name="Send E Prescription" xml:space="preserve">
    <value>Send E Prescription</value>
  </data>
  <data name="Save as Pdf" xml:space="preserve">
    <value>Save as Pdf</value>
  </data>
  <data name="ErrorFetchingAllPredefinedVisitTypes" xml:space="preserve">
    <value>ErrorFetchingAllPredefinedVisitTypes</value>
  </data>
  <data name="ErrorFetchingAllRoleslists" xml:space="preserve">
    <value>ErrorFetchingAllRoleslists</value>
  </data>
  <data name="ErrorFetchingRoleNames" xml:space="preserve">
    <value>ErrorFetchingRoleNames</value>
  </data>
  <data name="Select Facility" xml:space="preserve">
    <value>Select Facility</value>
  </data>
  <data name="Select Provider" xml:space="preserve">
    <value>Select Provider</value>
  </data>
  <data name="Enter Search Term" xml:space="preserve">
    <value>Enter Search Term</value>
  </data>
  <data name="No User Found" xml:space="preserve">
    <value>No User Found</value>
  </data>
  <data name="Phone Number" xml:space="preserve">
    <value>Phone Number</value>
  </data>
  <data name="Select Appointment Date" xml:space="preserve">
    <value>Select Appointment Date</value>
  </data>
  <data name="Select Start Time" xml:space="preserve">
    <value>Select Start Time</value>
  </data>
  <data name="Select End Time" xml:space="preserve">
    <value>Select End Time</value>
  </data>
  <data name="Select Visit Type" xml:space="preserve">
    <value>Select Visit Type</value>
  </data>
  <data name="Select Visit Status" xml:space="preserve">
    <value>Select Visit Status</value>
  </data>
  <data name="Enter Reason" xml:space="preserve">
    <value>Enter Reason</value>
  </data>
  <data name="Enter Notes" xml:space="preserve">
    <value>Enter Notes</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="Role Selection is required!" xml:space="preserve">
    <value>Role Selection is required!</value>
  </data>
  <data name="Email is Required" xml:space="preserve">
    <value>Email is Required</value>
  </data>
  <data name="Last Name" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="Last Name is required!" xml:space="preserve">
    <value>Last Name is required!</value>
  </data>
  <data name="First Name is required!" xml:space="preserve">
    <value>First Name is required!</value>
  </data>
  <data name="Username is required!" xml:space="preserve">
    <value>Username is required!</value>
  </data>
  <data name="Invalid Email Message" xml:space="preserve">
    <value>Invalid Email Message</value>
  </data>
  <data name="Email Already Exists" xml:space="preserve">
    <value>Email Already Exists</value>
  </data>
  <data name="Please Enter Country" xml:space="preserve">
    <value>Please Enter Country</value>
  </data>
  <data name="Please Fill All Required Fields" xml:space="preserve">
    <value>Please Fill All Required Fields</value>
  </data>
  <data name="RefillRx" xml:space="preserve">
    <value>RefillRx</value>
  </data>
</root>
