﻿

 using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using Microsoft.Extensions.Configuration;
using TeyaUIViewModels.TeyaUIViewModelResources;
using DotNetEnv;

namespace TeyaUIViewModels.ViewModel
{
    public class CPTService : ICPTService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public CPTService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<CPT>> GetAllCPTCodesAsync()
        {
            var baseUrl = $"{_EncounterNotes}/api/CPT";
            var accessToken = _tokenService.AccessToken;
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, baseUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CPT>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["CPTCodeRetrievalFailure"]);
            }
        }
    }
}
