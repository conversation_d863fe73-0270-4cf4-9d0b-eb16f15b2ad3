# Test with Real Message IDs from Server Logs
Write-Host "TESTING WITH REAL MESSAGE IDs FROM SERVER LOGS" -ForegroundColor Cyan

$baseUrl = "http://localhost:5000"

# Real message IDs from server logs
$messageWithAttachment = "f4ece05b-f209-4cdc-a55c-391034730e28"
$messageWithoutAttachment = "26f30cfb-ef6b-481d-bc60-cf6798804f33"

Write-Host "Message with attachment: $messageWithAttachment" -ForegroundColor Yellow
Write-Host "Message without attachment: $messageWithoutAttachment" -ForegroundColor Yellow
Write-Host ""

# Function to test endpoint
function Test-Endpoint {
    param($Name, $Method, $Url)
    
    Write-Host "TESTING: $Name" -ForegroundColor Yellow
    Write-Host "  URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method $Method
        Write-Host "  SUCCESS: $Name" -ForegroundColor Green
        return @{ Success = $true; Response = $response }
    }
    catch {
        Write-Host "  FAILED: $Name - $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false; Response = $null }
    }
}

# TEST 1: Get Message with Attachment
Write-Host "TEST 1: GET MESSAGE WITH ATTACHMENT" -ForegroundColor Magenta
$getMessageResult = Test-Endpoint "Get Message with Attachment" "GET" "$baseUrl/api/message/$messageWithAttachment"

if ($getMessageResult.Success) {
    $message = $getMessageResult.Response
    Write-Host "  Message Details:" -ForegroundColor Cyan
    Write-Host "    - ID: $($message.messageId)" -ForegroundColor Gray
    Write-Host "    - From: $($message.senderName) <$($message.senderEmailId)>" -ForegroundColor Gray
    Write-Host "    - To: $($message.receiverName) <$($message.receiverEmailId)>" -ForegroundColor Gray
    Write-Host "    - Subject: $($message.subject)" -ForegroundColor Gray
    Write-Host "    - Has Attachments: $($message.hasAttachments)" -ForegroundColor Gray
    Write-Host "    - Attachment Count: $($message.attachmentCount)" -ForegroundColor Gray
    Write-Host "    - Status: $($message.status)" -ForegroundColor Gray
}

Write-Host ""

# TEST 2: Get Attachments for Message
Write-Host "TEST 2: GET ATTACHMENTS FOR MESSAGE" -ForegroundColor Magenta
$getAttachmentsResult = Test-Endpoint "Get Attachments" "GET" "$baseUrl/api/message/$messageWithAttachment/attachments"

if ($getAttachmentsResult.Success) {
    $attachments = $getAttachmentsResult.Response
    Write-Host "  Found $($attachments.Count) attachment(s)" -ForegroundColor Cyan
    
    if ($attachments.Count -gt 0) {
        $attachment = $attachments[0]
        $attachmentId = $attachment.attachmentId
        Write-Host "  Attachment Details:" -ForegroundColor Gray
        Write-Host "    - ID: $($attachment.attachmentId)" -ForegroundColor Gray
        Write-Host "    - Original Name: $($attachment.originalFileName)" -ForegroundColor Gray
        Write-Host "    - Blob Name: $($attachment.blobFileName)" -ForegroundColor Gray
        Write-Host "    - Size: $($attachment.fileSizeBytes) bytes" -ForegroundColor Gray
        Write-Host "    - Azure URL: $($attachment.blobStorageUrl)" -ForegroundColor Yellow
        Write-Host "    - Container: $($attachment.blobContainerName)" -ForegroundColor Yellow
        
        # Verify real Azure URL
        if ($attachment.blobStorageUrl -like "*teyarecordingsdev*") {
            Write-Host "    VERIFIED: Real Azure Blob Storage URL!" -ForegroundColor Green
        } else {
            Write-Host "    WARNING: Not using real Azure storage!" -ForegroundColor Red
        }
        
        Write-Host ""
        
        # TEST 3: Download Attachment
        Write-Host "TEST 3: DOWNLOAD ATTACHMENT FROM REAL AZURE BLOB STORAGE" -ForegroundColor Magenta
        $downloadResult = Test-Endpoint "Download Attachment" "GET" "$baseUrl/api/message/attachment/$attachmentId/download"
        
        if ($downloadResult.Success) {
            $downloadedFile = "downloaded-real-test-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
            $downloadResult.Response | Out-File -FilePath $downloadedFile -Encoding UTF8
            Write-Host "  Downloaded to: $downloadedFile" -ForegroundColor Cyan
            
            # Verify content
            $downloadedSize = (Get-Item $downloadedFile).Length
            Write-Host "  Downloaded size: $downloadedSize bytes" -ForegroundColor Gray
            
            if ($downloadedSize -gt 0) {
                Write-Host "  VERIFIED: File downloaded successfully from real Azure Blob Storage!" -ForegroundColor Green
                
                # Show first few lines of downloaded content
                $content = Get-Content $downloadedFile -TotalCount 3
                Write-Host "  Content preview:" -ForegroundColor Gray
                foreach ($line in $content) {
                    Write-Host "    $line" -ForegroundColor Gray
                }
            } else {
                Write-Host "  WARNING: Downloaded file is empty!" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""

# TEST 4: Get Sent Messages
Write-Host "TEST 4: GET SENT MESSAGES" -ForegroundColor Magenta
$getSentResult = Test-Endpoint "Get Sent Messages" "GET" "$baseUrl/api/message/sent/<EMAIL>"

if ($getSentResult.Success) {
    $sentMessages = $getSentResult.Response
    Write-Host "  Found $($sentMessages.Count) sent message(s)" -ForegroundColor Cyan
    
    foreach ($msg in $sentMessages | Select-Object -First 3) {
        Write-Host "  Sent Message:" -ForegroundColor Gray
        Write-Host "    - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "    - To: $($msg.receiverName) <$($msg.receiverEmailId)>" -ForegroundColor Gray
        Write-Host "    - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "    - Has Attachments: $($msg.hasAttachments)" -ForegroundColor Gray
        Write-Host "    - Sent: $($msg.sentDateTime)" -ForegroundColor Gray
    }
}

Write-Host ""

# TEST 5: Get Inbox Messages
Write-Host "TEST 5: GET INBOX MESSAGES" -ForegroundColor Magenta
$getInboxResult = Test-Endpoint "Get Inbox Messages" "GET" "$baseUrl/api/message/inbox/<EMAIL>"

if ($getInboxResult.Success) {
    $inboxMessages = $getInboxResult.Response
    Write-Host "  Found $($inboxMessages.Count) inbox message(s)" -ForegroundColor Cyan
    
    foreach ($msg in $inboxMessages | Select-Object -First 3) {
        Write-Host "  Inbox Message:" -ForegroundColor Gray
        Write-Host "    - ID: $($msg.messageId)" -ForegroundColor Gray
        Write-Host "    - From: $($msg.senderName) <$($msg.senderEmailId)>" -ForegroundColor Gray
        Write-Host "    - Subject: $($msg.subject)" -ForegroundColor Gray
        Write-Host "    - Has Attachments: $($msg.hasAttachments)" -ForegroundColor Gray
        Write-Host "    - Sent: $($msg.sentDateTime)" -ForegroundColor Gray
    }
}

Write-Host ""

# TEST 6: Mark Message as Read
Write-Host "TEST 6: MARK MESSAGE AS READ" -ForegroundColor Magenta
$markReadResult = Test-Endpoint "Mark Message as Read" "PUT" "$baseUrl/api/message/$messageWithAttachment/read"

if ($markReadResult.Success) {
    Write-Host "  Message marked as read successfully" -ForegroundColor Green
}

Write-Host ""

# TEST 7: Mark Message as Important
Write-Host "TEST 7: MARK MESSAGE AS IMPORTANT" -ForegroundColor Magenta
$markImportantResult = Test-Endpoint "Mark Message as Important" "PUT" "$baseUrl/api/message/$messageWithAttachment/important"

if ($markImportantResult.Success) {
    Write-Host "  Message marked as important successfully" -ForegroundColor Green
}

Write-Host ""

Write-Host "REAL ENDPOINT TESTING COMPLETED" -ForegroundColor Cyan
Write-Host "ALL TESTS USED REAL MESSAGE IDs AND REAL AZURE BLOB STORAGE!" -ForegroundColor Green
Write-Host "Testing completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
