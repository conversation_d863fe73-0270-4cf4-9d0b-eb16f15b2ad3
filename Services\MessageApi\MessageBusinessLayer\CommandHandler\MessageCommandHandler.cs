using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageBusinessLayer.CommandHandler
{
    public class MessageCommandHandler : IMessageCommandHandler<Message>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MessageCommandHandler> _logger;
        private readonly IAzureCommunicationService _azureCommunicationService;
        private readonly IAttachmentService _attachmentService;

        public MessageCommandHandler(
            IConfiguration configuration,
            IUnitOfWork unitOfWork,
            ILogger<MessageCommandHandler> logger,
            IAzureCommunicationService azureCommunicationService,
            IAttachmentService attachmentService)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _azureCommunicationService = azureCommunicationService;
            _attachmentService = attachmentService;
        }

        public async Task<Guid> SendMessage(Message message, List<AttachmentRequest>? attachments = null)
        {
            try
            {
                // Generate new message ID
                message.MessageId = Guid.NewGuid();
                message.SentDateTime = DateTime.UtcNow;
                message.Status = 1; // 1 = Sent

                // Process attachments first
                if (attachments != null && attachments.Any())
                {
                    _logger.LogInformation($"Starting to process {attachments.Count} attachments for message {message.MessageId}");

                    var processedAttachments = await _attachmentService.ProcessAttachmentsAsync(message.MessageId, attachments);
                    message.HasAttachments = processedAttachments.Any();
                    message.AttachmentCount = processedAttachments.Count;

                    _logger.LogInformation($"AttachmentService returned {processedAttachments.Count} processed attachments");

                    // Add attachments to the message
                    foreach (var attachment in processedAttachments)
                    {
                        message.Attachments.Add(attachment);
                        _logger.LogInformation($"Added attachment {attachment.OriginalFileName} to message collection");
                    }

                    _logger.LogInformation($"Message now has {message.Attachments.Count} attachments, HasAttachments: {message.HasAttachments}, AttachmentCount: {message.AttachmentCount}");
                }
                else
                {
                    _logger.LogInformation($"No attachments provided for message {message.MessageId}");
                }

                // Send message via Azure Communication Services
                var azureMessageId = await _azureCommunicationService.SendMessageAsync(
                    message.SenderEmailId,
                    message.ReceiverEmailId,
                    message.MessageContent);

                message.AzureMessageId = azureMessageId;

                // Get or create conversation ID
                var conversationId = await _azureCommunicationService.GetOrCreateConversationAsync(
                    message.SenderEmailId,
                    message.ReceiverEmailId);

                message.ConversationId = conversationId;

                // Ensure all attachments have the correct MessageId before saving
                if (message.HasAttachments && message.Attachments.Any())
                {
                    _logger.LogInformation($"Preparing {message.Attachments.Count} attachments for database save");
                    foreach (var attachment in message.Attachments)
                    {
                        attachment.MessageId = message.MessageId;
                    }
                }

                // Save message with attachments (Entity Framework will handle the relationship)
                await _unitOfWork.MessageRepository.AddAsync(new List<Message> { message });
                await _unitOfWork.SaveAsync();

                if (message.HasAttachments && message.Attachments.Any())
                {
                    _logger.LogInformation($"Successfully saved message with {message.Attachments.Count} attachments to database");
                }

                _logger.LogInformation($"Message saved successfully. MessageId: {message.MessageId}, HasAttachments: {message.HasAttachments}, AttachmentCount: {message.AttachmentCount}");

                // Mark as delivered in ACS
                await _azureCommunicationService.MarkMessageAsDeliveredAsync(azureMessageId);

                _logger.LogInformation($"Message sent successfully. MessageId: {message.MessageId}, Attachments: {message.AttachmentCount}");
                return message.MessageId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending message from {message.SenderEmailId} to {message.ReceiverEmailId}");
                throw;
            }
        }

        public async Task MarkMessageAsRead(Guid messageId)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository.GetByIdAsync(messageId);
                if (message != null)
                {
                    message.Status = 3; // 3 = Read
                    message.ReadDateTime = DateTime.UtcNow;

                    await _unitOfWork.MessageRepository.UpdateAsync(message);
                    await _unitOfWork.SaveAsync();

                    _logger.LogInformation($"Message marked as read. MessageId: {messageId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error marking message as read. MessageId: {messageId}");
                throw;
            }
        }

        public async Task DeleteMessage(Guid messageId)
        {
            try
            {
                await _unitOfWork.MessageRepository.DeleteByIdAsync(messageId);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation($"Message permanently deleted. MessageId: {messageId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error permanently deleting message. MessageId: {messageId}");
                throw;
            }
        }

        public async Task<bool> SoftDeleteMessage(Guid messageId, string deletedBy, string? reason = null)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository.GetByIdAsync(messageId);
                if (message == null)
                {
                    _logger.LogWarning($"Message not found for soft delete. MessageId: {messageId}");
                    return false;
                }

                if (message.IsDeleted)
                {
                    _logger.LogWarning($"Message already soft deleted. MessageId: {messageId}");
                    return false;
                }

                message.IsDeleted = true;
                message.DeletedDateTime = DateTime.UtcNow;
                message.DeletedBy = deletedBy;
                // UpdatedAt will be handled by database trigger or SaveChanges override

                await _unitOfWork.MessageRepository.UpdateAsync(message);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Message soft deleted successfully. MessageId: {messageId}, DeletedBy: {deletedBy}, Reason: {reason}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error soft deleting message. MessageId: {messageId}, DeletedBy: {deletedBy}");
                throw;
            }
        }

        public async Task<bool> RestoreMessage(Guid messageId, string restoredBy, string? reason = null)
        {
            try
            {
                // Use IgnoreQueryFilters to find soft-deleted messages
                var message = await _unitOfWork.MessageRepository.GetByIdAsync(messageId, includeDeleted: true);
                if (message == null)
                {
                    _logger.LogWarning($"Message not found for restore. MessageId: {messageId}");
                    return false;
                }

                if (!message.IsDeleted)
                {
                    _logger.LogWarning($"Message is not deleted, cannot restore. MessageId: {messageId}");
                    return false;
                }

                message.IsDeleted = false;
                message.DeletedDateTime = null;
                message.DeletedBy = null;
                message.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.MessageRepository.UpdateAsync(message);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Message restored successfully. MessageId: {messageId}, RestoredBy: {restoredBy}, Reason: {reason}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error restoring message. MessageId: {messageId}, RestoredBy: {restoredBy}");
                throw;
            }
        }

        public async Task<bool> ArchiveMessage(Guid messageId, string archivedBy)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository.GetByIdAsync(messageId);
                if (message == null)
                {
                    _logger.LogWarning($"Message not found for archive. MessageId: {messageId}");
                    return false;
                }

                if (message.IsArchived)
                {
                    _logger.LogWarning($"Message already archived. MessageId: {messageId}");
                    return false;
                }

                message.IsArchived = true;
                message.ArchivedDateTime = DateTime.UtcNow;
                message.ArchivedBy = archivedBy;
                message.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.MessageRepository.UpdateAsync(message);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Message archived successfully. MessageId: {messageId}, ArchivedBy: {archivedBy}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error archiving message. MessageId: {messageId}, ArchivedBy: {archivedBy}");
                throw;
            }
        }

        public async Task<bool> UnarchiveMessage(Guid messageId, string unarchivedBy)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository.GetByIdAsync(messageId);
                if (message == null)
                {
                    _logger.LogWarning($"Message not found for unarchive. MessageId: {messageId}");
                    return false;
                }

                if (!message.IsArchived)
                {
                    _logger.LogWarning($"Message is not archived, cannot unarchive. MessageId: {messageId}");
                    return false;
                }

                message.IsArchived = false;
                message.ArchivedDateTime = null;
                message.ArchivedBy = null;
                message.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.MessageRepository.UpdateAsync(message);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Message unarchived successfully. MessageId: {messageId}, UnarchivedBy: {unarchivedBy}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error unarchiving message. MessageId: {messageId}, UnarchivedBy: {unarchivedBy}");
                throw;
            }
        }

        public async Task<bool> ToggleImportant(Guid messageId, bool isImportant, string updatedBy)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository.GetByIdAsync(messageId);
                if (message == null)
                {
                    _logger.LogWarning($"Message not found for toggle important. MessageId: {messageId}");
                    return false;
                }

                message.IsImportant = isImportant;
                message.UpdatedAt = DateTime.UtcNow;

                await _unitOfWork.MessageRepository.UpdateAsync(message);
                await _unitOfWork.SaveAsync();

                var status = isImportant ? "marked as important" : "unmarked as important";
                _logger.LogInformation($"Message {status} successfully. MessageId: {messageId}, UpdatedBy: {updatedBy}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error toggling important status. MessageId: {messageId}, IsImportant: {isImportant}, UpdatedBy: {updatedBy}");
                throw;
            }
        }
    }
}
