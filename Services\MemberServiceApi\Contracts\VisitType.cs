﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts
{
    public class VisitType : IContract
    {
        public Guid ID { get; set; }  
        public string VisitName { get; set; }
        public string CPTCode { get; set; }
        public Guid OrganizationId { get; set; }
        public Boolean? IsActive { get; set; } = true;
    }
}
