﻿
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
 
 
namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class HistoryOfPresentIllnessCommandHandler : IHistoryOfPresentIllnessCommandHandler<HistoryOfPresentIllness>
    {
        private readonly IUnitOfWork _unitOfWork;
        public HistoryOfPresentIllnessCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<HistoryOfPresentIllnessCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Add History of Present Illness
        /// </summary>
        /// <param name="records"></param>
        /// <returns></returns>
        public async Task AddHistoryOfPresentIllness(List<HistoryOfPresentIllness> records)
        {
            await _unitOfWork.HistoryOfPresentIllnessRepository.AddAsync(records);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Update History of Present Illness by Records
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        public async Task UpdateHistoryOfPresentIllness(HistoryOfPresentIllness record)
        {
            await _unitOfWork.HistoryOfPresentIllnessRepository.UpdateAsync(record);
            await _unitOfWork.SaveAsync();
        }
        /// <summary>
        /// Delete a specific History of Present Illness by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task DeleteHistoryOfPresentIllnessById(Guid id)
        {
            await _unitOfWork.HistoryOfPresentIllnessRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
        /// <summary>
        /// Delete  History of Present Illness by Records
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        public async Task DeleteHistoryOfPresentIllnessByEntity(HistoryOfPresentIllness record)
        {
            await _unitOfWork.HistoryOfPresentIllnessRepository.DeleteByEntityAsync(record);
            await _unitOfWork.SaveAsync();
        }
        /// <summary>
        /// Update History of Present Illness in Bulk
        /// </summary>
        /// <param name="hpiRecords"></param>
        /// <returns></returns>
        public async Task UpdateHistoryOfPresentIllnessBulk(List<HistoryOfPresentIllness> hpiRecords)
        {
            await _unitOfWork.HistoryOfPresentIllnessRepository.UpdateRangeAsync(hpiRecords);
            await _unitOfWork.SaveAsync();
        }

    }
}

