﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class FacilityCommandHandler : IFacilityCommandHandler<Facility>
    {
        private readonly IUnitOfWork _unitOfWork;

        public FacilityCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddFacilityAsync(List<Facility> facilities)
        {
            await _unitOfWork.FacilityRepository.AddAsync(facilities);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateFacilityAsync(Facility facility)
        {
            await _unitOfWork.FacilityRepository.UpdateAsync(facility);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteFacilityAsync(Guid id)
        {
            await _unitOfWork.FacilityRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
