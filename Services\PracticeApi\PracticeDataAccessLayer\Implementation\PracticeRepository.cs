﻿using Microsoft.EntityFrameworkCore;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PracticeDataAccessLayer.Implementation
{
    public class PracticeRepository : GenericRepository<Tasks>, IPracticeRepository
    {
        private readonly PracticeDatabaseContext _context;

        public PracticeRepository(PracticeDatabaseContext context) : base(context)
        {
            _context = context;
        }

        public async Task<bool> UpdateTasksByPatientIdAsync(Tasks tasks)
        {
            if (tasks == null)
            {
                throw new ArgumentException("Task object cannot be null.", nameof(tasks));
            }
            var result = await _context.Tasks
                .Where(tasksList => tasksList.SSN == tasks.SSN)
                .FirstOrDefaultAsync();

            if (result == null)
            {
                throw new ArgumentException($"No task found for PatientId: {tasks.SSN}", nameof(tasks.SSN));
            }

            result.PatientName = tasks.PatientName;
            result.AssignedTo = tasks.AssignedTo;
            result.TaskType = tasks.TaskType;
            result.Subject = tasks.Subject;
            result.CreatedBy = tasks.CreatedBy;
            result.Status = tasks.Status;
            result.Frequency = tasks.Frequency;
            result.LastDueDate = tasks.LastDueDate;
            result.DueDate = tasks.DueDate;
            result.CreationDate = tasks.CreationDate;
            result.StartDate = tasks.StartDate;
            result.Priority = tasks.Priority;
            result.RecurringAction = tasks.RecurringAction;
            result.Notes = tasks.Notes;

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
