using MessageContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IAttachmentService
    {
        Task<List<MessageAttachment>> ProcessAttachmentsAsync(Guid messageId, List<AttachmentRequest>? attachments);
        Task<MessageAttachment?> GetAttachmentAsync(Guid attachmentId);
        Task<byte[]> GetAttachmentContentAsync(Guid attachmentId);
        Task<bool> DeleteAttachmentAsync(Guid attachmentId);
        Task<List<MessageAttachment>> GetMessageAttachmentsAsync(Guid messageId);
        bool ValidateAttachment(AttachmentRequest attachment);
    }
}
