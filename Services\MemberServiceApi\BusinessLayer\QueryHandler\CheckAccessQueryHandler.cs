﻿using Contracts;
using DataAccessLayer.Implementation;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Build.Tasks.Deployment.Bootstrapper;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class CheckAccessQueryHandler:ICheckAccessQueryHandler<ProductAccess>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        Task<ProductUserAccess> result;

        public CheckAccessQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public Task<ProductUserAccess> CheckAccessAsync(Guid MemberId,Guid ProductId)
        {
            result= _unitOfWork.ProductUserAccessRepository.GetAccessRecordAsync(ProductId, MemberId);
            return result;
        }
    }
}
