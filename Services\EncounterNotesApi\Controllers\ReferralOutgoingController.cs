﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class ReferralOutgoingController : ControllerBase
    {
        private readonly IReferralOutgoingCommandHandler<PatientReferralOutgoing> _ReferralOutgoingDataHandler;
        private readonly IReferralOutgoingQueryHandler<PatientReferralOutgoing> _ReferralOutgoingQueryHandler;
        private readonly ILogger<ReferralOutgoingController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public ReferralOutgoingController(
            IReferralOutgoingCommandHandler<PatientReferralOutgoing> ReferralOutgoingDataHandler,
            IReferralOutgoingQueryHandler<PatientReferralOutgoing> ReferralOutgoingQueryHandler,
            ILogger<ReferralOutgoingController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _ReferralOutgoingDataHandler = ReferralOutgoingDataHandler;
            _ReferralOutgoingQueryHandler = ReferralOutgoingQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get all by ID
        /// </summary>
        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<PatientReferralOutgoing>>> GetAllById(Guid id)
        {
            ActionResult result;
            try
            {
                var PatientReferralOutgoing = await _ReferralOutgoingQueryHandler.GetAllPatientReferralOutgoingbyId(id);
                result = PatientReferralOutgoing != null ? Ok(PatientReferralOutgoing) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get only PatientReferralOutgoing for an ID
        /// </summary>
        [HttpGet("{id:guid}/isActive")]
        public async Task<ActionResult<IEnumerable<PatientReferralOutgoing>>> GetAllByIdAndIsActive(Guid id)
        {
            ActionResult result;
            try
            {
                var PatientReferralOutgoing = await _ReferralOutgoingQueryHandler.GetPatientReferralOutgoingByIdAndIsActive(id);
                result = PatientReferralOutgoing != null ? Ok(PatientReferralOutgoing) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add List Of new PatientReferralOutgoing
        /// </summary>
        [HttpPost]
        [Route("AddPatientReferralOutgoing")]
        public async Task<IActionResult> Registration([FromBody] List<PatientReferralOutgoing> PatientReferralOutgoing)
        {
            if (PatientReferralOutgoing == null || PatientReferralOutgoing.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _ReferralOutgoingDataHandler.AddReferralOutgoing(PatientReferralOutgoing);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete PatientReferralOutgoing
        /// </summary>
        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] PatientReferralOutgoing ReferralOutgoing)
        {
            IActionResult result;
            if (ReferralOutgoing == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _ReferralOutgoingDataHandler.DeleteReferralOutgoingByEntity(ReferralOutgoing);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update Single ReferralOutgoing By PatientID
        /// </summary>
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateReferralOutgoingById(Guid id, [FromBody] PatientReferralOutgoing ReferralOutgoing)
        {
            IActionResult result;
            if (ReferralOutgoing == null || ReferralOutgoing.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _ReferralOutgoingDataHandler.UpdateReferralOutgoing(ReferralOutgoing);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update List of PatientReferralOutgoing
        /// </summary>
        [HttpPut]
        [Route("UpdatePatientReferralOutgoingList")]
        public async Task<IActionResult> UpdateReferralOutgoingList([FromBody] List<PatientReferralOutgoing> PatientReferralOutgoing)
        {
            IActionResult result;

            try
            {
                await _ReferralOutgoingDataHandler.UpdateReferralOutgoingList(PatientReferralOutgoing);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }
    }
}