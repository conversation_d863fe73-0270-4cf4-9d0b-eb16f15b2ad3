﻿using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class UserAccessCommandHandler : IUserAccessCommandHandler<ProductUserAccess>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserAccessCommandHandler> _logger;

        public UserAccessCommandHandler(IUnitOfWork unitOfWork, ILogger<UserAccessCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<bool> UpdateUserAccessAsync(Guid productId, List<MemberAccessUpdate> memberAccessUpdates)
        {
            foreach (var update in memberAccessUpdates)
            {
                var accessRecord = await _unitOfWork.ProductUserAccessRepository
                    .GetAccessRecordAsync(productId, update.MemberId);

                accessRecord.HasAccess = update.HasAccess;

            }

            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
