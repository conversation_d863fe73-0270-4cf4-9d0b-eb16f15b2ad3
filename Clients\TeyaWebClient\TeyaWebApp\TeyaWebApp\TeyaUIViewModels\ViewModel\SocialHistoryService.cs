﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using Microsoft.Extensions.Logging;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class SocialHistoryService : ISocialHistoryService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<SocialHistoryService> _localizer;
        private readonly ILogger<SocialHistoryService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public SocialHistoryService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<SocialHistoryService> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all Social History records by Patient ID (regardless of active status)
        /// </summary>
        public async Task<List<PatientSocialHistory>> GetAllByPatientIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/SocialHistory/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PatientSocialHistory>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        /// <summary>
        /// Get active Social History records by ID
        /// </summary>
        public async Task<List<PatientSocialHistory>> GetAllByIdAndIsActiveAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/SocialHistory/{id}/isActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PatientSocialHistory>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        /// <summary>
        /// Add a list of new Social History records
        /// </summary>
        public async Task AddHistoryListAsync(List<PatientSocialHistory> histories)
        {
            var apiUrl = $"{_EncounterNotes}/api/SocialHistory/addlist";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(histories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        /// <summary>
        /// Update a list of Social History records
        /// </summary>
        public async Task UpdateHistoryListAsync(List<PatientSocialHistory> histories)
        {
            var apiUrl = $"{_EncounterNotes}/api/SocialHistory/updatelist";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(histories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["UpdateLogError"]);
            }
        }
    }
}
