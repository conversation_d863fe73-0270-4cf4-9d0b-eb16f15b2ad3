﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class OrganizationsQueryHandler : IOrganizationsQueryHandler<Organization>
    {
        private readonly IUnitOfWork _unitOfWork;

        public OrganizationsQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Organization> GetOrganizationByIdAsync(Guid id)
        {
            var organization = await _unitOfWork.OrganizationRepository.GetByIdAsync(id);
            return organization;
        }

        public async Task<List<Organization>> GetAllOrganizationsAsync()
        {
            var organizations = await _unitOfWork.OrganizationRepository.GetAllAsync();
            return organizations.ToList();
        }

        public async Task<List<Organization>> GetOrganizationsByNameAsync(string name)
        {
            var organizations = await _unitOfWork.OrganizationRepository.GetAllAsync();
            return organizations.Where(organization => organization.OrganizationName.Contains(name, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public async Task<bool> FindByNameAsync(string organization)
        {
            var result = await _unitOfWork.OrganizationRepository.FindByNameAsync(organization);
            return  result;
        }
    }
}
