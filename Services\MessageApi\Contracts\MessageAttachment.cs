using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace MessageContracts
{
    public class MessageAttachment : IContract
    {
        [Key]
        public Guid AttachmentId { get; set; }

        [Required]
        public Guid MessageId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ContentType { get; set; } = string.Empty;

        [Required]
        public long FileSizeBytes { get; set; }

        // Azure Blob Storage specific (required for blob-only storage)
        [Required]
        [StringLength(500)]
        public string BlobFileName { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string BlobStorageUrl { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string BlobContainerName { get; set; } = string.Empty;

        // File metadata
        public DateTime UploadedDateTime { get; set; } = DateTime.UtcNow;
        public string? FileHash { get; set; } // For integrity checking

        // Legacy field (for database compatibility - will be removed in future migration)
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = "BLOB_STORAGE_ONLY";

        // Security and validation
        public bool IsScanned { get; set; } = false;
        public bool IsSafe { get; set; } = true;
        public string? ScanResult { get; set; }

        // Navigation property
        [ForeignKey("MessageId")]
        [JsonIgnore] // Prevent circular reference during JSON serialization
        public virtual Message? Message { get; set; }
    }

    public static class AttachmentConstants
    {
        // Maximum file size (10MB)
        public const long MaxFileSizeBytes = 10 * 1024 * 1024;

        // Allowed file types
        public static readonly string[] AllowedContentTypes = {
            // Images
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp",

            // Documents
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "text/csv",
            "text/html",
            "text/xml",
            "application/json",
            "application/xml",
            "application/javascript",

            // Archives
            "application/zip",
            "application/x-rar-compressed",
            "application/x-7z-compressed",

            // Audio
            "audio/mpeg", "audio/wav", "audio/ogg",

            // Video
            "video/mp4", "video/avi", "video/mov", "video/wmv"
        };

        // Dangerous file extensions to block
        public static readonly string[] BlockedExtensions = {
            ".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js", ".jar", ".msi"
        };
    }
}
