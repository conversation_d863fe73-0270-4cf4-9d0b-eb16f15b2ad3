using System;

namespace MessageContracts
{
    public class MessageAttachment : IContract
    {
        public Guid AttachmentId { get; set; }
        public Guid MessageId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
        public string BlobFileName { get; set; } = string.Empty;
        public string BlobStorageUrl { get; set; } = string.Empty;
        public string BlobContainerName { get; set; } = string.Empty;
        public DateTime UploadedDateTime { get; set; } = DateTime.UtcNow;
        public string? FileHash { get; set; }
        public string FilePath { get; set; } = "BLOB_STORAGE_ONLY";
        public bool IsScanned { get; set; } = false;
        public bool IsSafe { get; set; } = true;
        public string? ScanResult { get; set; }
        public virtual Message? Message { get; set; }
    }
}
