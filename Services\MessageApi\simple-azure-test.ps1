# Simple Real Azure Blob Storage Test
Write-Host "Testing Real Azure Blob Storage..." -ForegroundColor Green

$baseUrl = "http://localhost:5000"
$attachmentFile = "test-medical-report.txt"

# Read attachment
$fileContent = Get-Content $attachmentFile -Raw -Encoding UTF8
$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
$base64Content = [Convert]::ToBase64String($fileBytes)
$fileSize = $fileBytes.Length

Write-Host "File size: $fileSize bytes"

# Create request
$emailRequest = @{
    senderName = "Dr. <PERSON>"
    senderEmailId = "<EMAIL>"
    receiverName = "Dr. <PERSON>"
    receiverEmailId = "<EMAIL>"
    subject = "REAL AZURE BLOB TEST"
    messageContent = "Testing real Azure Blob Storage upload to teyarecordingsdev/messageattachment"
    attachments = @(
        @{
            fileName = $attachmentFile
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSize
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Sending email to real Azure Blob Storage..."

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/message/send" -Method POST -Body $emailRequest -ContentType "application/json"
    
    Write-Host "SUCCESS! Message ID: $($response.messageId)" -ForegroundColor Green
    
    $messageId = $response.messageId
    
    if ($messageId) {
        # Get attachments
        Write-Host "Getting attachments..."
        $attachments = Invoke-RestMethod -Uri "$baseUrl/api/message/$messageId/attachments" -Method GET
        
        Write-Host "Found $($attachments.Count) attachment(s)" -ForegroundColor Green
        
        if ($attachments.Count -gt 0) {
            $attachment = $attachments[0]
            Write-Host "Blob URL: $($attachment.blobStorageUrl)" -ForegroundColor Yellow
            Write-Host "Container: $($attachment.blobContainerName)" -ForegroundColor Yellow
            
            # Check if real Azure URL
            if ($attachment.blobStorageUrl -like "*teyarecordingsdev*") {
                Write-Host "SUCCESS! Real Azure Blob Storage used!" -ForegroundColor Green
            } else {
                Write-Host "WARNING: Mock storage still being used!" -ForegroundColor Yellow
            }
        }
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
