﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class FacilityRepository : GenericRepository<Facility>, IFacilityRepository
    {
        private readonly AccountDatabaseContext _context;

        public FacilityRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }

        public async Task<List<Facility>> GetFacilitiesByNameAsync(string name)
        {
            var result = await _context.Facility
                 .Where(facility => facility.FacilityName.Contains(name, StringComparison.OrdinalIgnoreCase))
                 .ToListAsync();
            return result;
        }
    }
}
