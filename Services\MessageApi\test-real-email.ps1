# Real Email Test with Azure Blob Storage Attachment
# Testing MessageAPI with actual medical report attachment

Write-Host "🧪 TESTING AZURE BLOB STORAGE EMAIL WITH REAL INPUT" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Configuration
$baseUrl = "http://localhost:5000"
$senderEmail = "<EMAIL>"
$receiverEmail = "<EMAIL>"
$attachmentFile = "test-medical-report.txt"

# Check if attachment file exists
if (-not (Test-Path $attachmentFile)) {
    Write-Host "❌ ERROR: Attachment file not found: $attachmentFile" -ForegroundColor Red
    exit 1
}

# Read and encode attachment
Write-Host "📎 Reading attachment file: $attachmentFile" -ForegroundColor Yellow
$fileContent = Get-Content $attachmentFile -Raw -Encoding UTF8
$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
$base64Content = [Convert]::ToBase64String($fileBytes)
$fileSize = $fileBytes.Length

Write-Host "📊 File Details:" -ForegroundColor Gray
Write-Host "   - File Name: $attachmentFile" -ForegroundColor Gray
Write-Host "   - File Size: $fileSize bytes" -ForegroundColor Gray
Write-Host "   - Content Type: text/plain" -ForegroundColor Gray
Write-Host "   - Base64 Length: $($base64Content.Length) characters" -ForegroundColor Gray

# Create email request with real medical data
$emailRequest = @{
    senderName = "Dr. Sarah Johnson"
    senderEmailId = $senderEmail
    receiverName = "Dr. Michael Brown"
    receiverEmailId = $receiverEmail
    subject = "Patient Consultation Report - John Smith (P12345)"
    messageContent = @"
Dear Dr. Brown,

Please find attached the consultation report for patient John Smith (ID: P12345) from today's visit.

Key findings:
- Upper respiratory tract infection
- Viral etiology suspected
- Treatment plan initiated

The patient has been advised to follow up in 7 days if symptoms persist. Please review the attached detailed report.

Best regards,
Dr. Sarah Johnson, MD
Internal Medicine Department
TeyaHealth Medical Center
"@
    attachments = @(
        @{
            fileName = $attachmentFile
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSize
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host ""
Write-Host "📧 SENDING EMAIL WITH ATTACHMENT..." -ForegroundColor Green
Write-Host "   From: $senderEmail" -ForegroundColor Gray
Write-Host "   To: $receiverEmail" -ForegroundColor Gray
Write-Host "   Subject: Patient Consultation Report - John Smith (P12345)" -ForegroundColor Gray
Write-Host "   Attachment: $attachmentFile ($fileSize bytes)" -ForegroundColor Gray

try {
    # Send email
    $response = Invoke-RestMethod -Uri "$baseUrl/api/message/send" -Method POST -Body $emailRequest -ContentType "application/json"
    
    Write-Host ""
    Write-Host "✅ EMAIL SENT SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "📨 Message ID: $($response.messageId)" -ForegroundColor Cyan
    Write-Host "📎 Attachments Processed: $($response.attachmentsProcessed)" -ForegroundColor Cyan
    
    $messageId = $response.messageId
    
    # Test: Get message attachments
    Write-Host ""
    Write-Host "🔍 TESTING: Getting message attachments..." -ForegroundColor Yellow
    $attachmentsResponse = Invoke-RestMethod -Uri "$baseUrl/api/message/$messageId/attachments" -Method GET
    
    Write-Host "✅ Found $($attachmentsResponse.Count) attachment(s)" -ForegroundColor Green
    
    foreach ($attachment in $attachmentsResponse) {
        Write-Host ""
        Write-Host "📎 Attachment Details:" -ForegroundColor Cyan
        Write-Host "   - ID: $($attachment.attachmentId)" -ForegroundColor Gray
        Write-Host "   - Original Name: $($attachment.originalFileName)" -ForegroundColor Gray
        Write-Host "   - Blob Name: $($attachment.blobFileName)" -ForegroundColor Gray
        Write-Host "   - Size: $($attachment.fileSizeBytes) bytes" -ForegroundColor Gray
        Write-Host "   - Content Type: $($attachment.contentType)" -ForegroundColor Gray
        Write-Host "   - Blob URL: $($attachment.blobStorageUrl)" -ForegroundColor Gray
        Write-Host "   - Container: $($attachment.blobContainerName)" -ForegroundColor Gray
        Write-Host "   - Upload Time: $($attachment.uploadedDateTime)" -ForegroundColor Gray
        Write-Host "   - File Hash: $($attachment.fileHash)" -ForegroundColor Gray
        
        # Test: Download attachment
        Write-Host ""
        Write-Host "⬇️ TESTING: Downloading attachment..." -ForegroundColor Yellow
        $attachmentId = $attachment.attachmentId
        
        try {
            $downloadResponse = Invoke-RestMethod -Uri "$baseUrl/api/message/attachment/$attachmentId/download" -Method GET
            
            # Save downloaded content to verify
            $downloadedFile = "downloaded-$($attachment.originalFileName)"
            $downloadResponse | Out-File -FilePath $downloadedFile -Encoding UTF8
            
            Write-Host "✅ Attachment downloaded successfully!" -ForegroundColor Green
            Write-Host "💾 Saved as: $downloadedFile" -ForegroundColor Cyan
            
            # Verify content matches
            $originalContent = Get-Content $attachmentFile -Raw
            $downloadedContent = Get-Content $downloadedFile -Raw
            
            if ($originalContent -eq $downloadedContent) {
                Write-Host "✅ CONTENT VERIFICATION: Original and downloaded files match!" -ForegroundColor Green
            } else {
                Write-Host "❌ CONTENT VERIFICATION: Files do not match!" -ForegroundColor Red
            }
            
        } catch {
            Write-Host "❌ ERROR downloading attachment: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "🎉 ALL TESTS COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "✅ Azure Blob Storage attachment system is working perfectly!" -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorDetails = $_.Exception.Response | ConvertFrom-Json
        Write-Host "Error Details: $errorDetails" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🏁 Test completed." -ForegroundColor Cyan
