﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class PhysicalTherapyCommandHandler : IPhysicalTherapyCommandHandler<PhysicalTherapyData>
    {
        private readonly IUnitOfWork _unitOfWork;

        public PhysicalTherapyCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddPhysicalTherapy(List<PhysicalTherapyData> _PhysicalTherapy)
        {
            await _unitOfWork.PhysicalTherapyRepository.AddAsync(_PhysicalTherapy);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdatePhysicalTherapy(PhysicalTherapyData _PhysicalTherapy)
        {
            await _unitOfWork.PhysicalTherapyRepository.UpdateAsync(_PhysicalTherapy);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeletePhysicalTherapyById(Guid id)
        {
            await _unitOfWork.PhysicalTherapyRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeletePhysicalTherapyByEntity(PhysicalTherapyData _PhysicalTherapy)
        {
            await _unitOfWork.PhysicalTherapyRepository.DeleteByEntityAsync(_PhysicalTherapy);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdatePhysicalTherapyList(List<PhysicalTherapyData> _PhysicalTherapy)
        {
            await _unitOfWork.PhysicalTherapyRepository.UpdateRangeAsync(_PhysicalTherapy);
            await _unitOfWork.SaveAsync();
        }
    }
}
