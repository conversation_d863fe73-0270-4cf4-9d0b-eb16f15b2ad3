﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class PrescriptionMedicationController : ControllerBase
    {
        private readonly IPrescriptionMedicationCommandHandler<PrescriptionMedication> _medicDataHandler;
        private readonly IPrescriptionMedicationQueryHandler<PrescriptionMedication> _medicQueryHandler;
        private readonly ILogger<PrescriptionMedicationController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public PrescriptionMedicationController(
            IPrescriptionMedicationCommandHandler<PrescriptionMedication> medicDataHandler,
            IPrescriptionMedicationQueryHandler<PrescriptionMedication> medicQueryHandler,
            ILogger<PrescriptionMedicationController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _medicDataHandler = medicDataHandler;
            _medicQueryHandler = medicQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get all by ID
        /// </summary>


        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<CurrentMedication>>> GetAllById(Guid id)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllMedicationsbyId(id);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get only Active Medication for an ID
        /// </summary>


        [HttpGet("{id:guid}/isActive")]
        public async Task<ActionResult<IEnumerable<PrescriptionMedication>>> GetAllByIdAndIsActive(Guid id)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetMedicationByIdAndIsActive(id);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add List Of new Medication
        /// </summary>

        [HttpPost]
        [Route("AddMedication")]
        public async Task<IActionResult> Registration([FromBody] List<PrescriptionMedication> medications)
        {
            if (medications == null || medications.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _medicDataHandler.AddMedication(medications);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete Medication
        /// </summary>

        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] PrescriptionMedication medication)
        {
            IActionResult result;
            if (medication == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _medicDataHandler.DeleteMedicationByEntity(medication);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update Single Medication By PatientID
        /// </summary>

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateMedicationById(Guid id, [FromBody] PrescriptionMedication CurrMedication)
        {
            IActionResult result;
            if (CurrMedication == null || CurrMedication.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _medicDataHandler.UpdateMedication(CurrMedication);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update List of Medication
        /// </summary>

        [HttpPut]
        [Route("UpdateMedicationList")]
        public async Task<IActionResult> UpdateMedicationList([FromBody] List<PrescriptionMedication> currentMedications)
        {
            IActionResult result;

            try
            {
                await _medicDataHandler.UpdateMedicationList(currentMedications);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }
    }
}