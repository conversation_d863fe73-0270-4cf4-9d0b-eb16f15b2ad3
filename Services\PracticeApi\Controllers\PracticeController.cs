﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using PracticeContracts;
using PracticeBusinessLayer;
using Azure;
using PracticeApi.PracticeApiResources;

namespace PracticeApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PracticeController : ControllerBase
    {
        private readonly IPracticeCommandHandler<Tasks> _practiceDataHandler;
        private readonly IPracticeQueryHandler<Tasks> _practiceQueryHandler;
        private readonly ILogger<PracticeController> _logger;
        private readonly IStringLocalizer<PracticeApiStrings> _localizer;


        public PracticeController(
            IPracticeCommandHandler<Tasks> dataHandler,
            IPracticeQueryHandler<Tasks> queryHandler,
            ILogger<PracticeController> logger,
            IStringLocalizer<PracticeApiStrings> localizer
            )
        {
            _practiceDataHandler = dataHandler;
            _practiceQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer; // Assigning localizer
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Tasks>>> Get()
        {
            ActionResult response;
            try
            {
                var tasks = await _practiceQueryHandler.GetTasks();
                response= Ok(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }
            return response;
        }

        [HttpGet("{id:guid}")]
        public async Task<ActionResult<Tasks>> GetById(Guid id)
        {
            ActionResult response;
            try
            {
                var task = await _practiceQueryHandler.GetTaskById(id);
                response= Ok(task);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] Tasks task)
        {
            IActionResult response;
            try
            {
                await _practiceDataHandler.UpdateTasks(task);
                response= Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }

        [HttpDelete("{id:guid}")]
        public async Task<IActionResult> DeleteById(Guid id)
        {
            IActionResult response;
            try
            {
                await _practiceDataHandler.DeleteTasksById(id);
                response= Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }


        [HttpPost]
        [Route("registration")]
        public async Task<IActionResult> Registration([FromBody] List<Tasks> registrations)
        {
            IActionResult response;
            if (registrations == null || registrations.Count == 0)
            {
                response= BadRequest(_localizer["NoTask"]);
            }

            try
            {
                await _practiceDataHandler.AddTasks(registrations);
                response= Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }
    }
}

