﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class RecordRepository : GenericRepository<Record>, IRecordRepository
    {
        private readonly RecordDatabaseContext _context;

        public RecordRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task AddRecordsAsync(List<Record> EncounterNotes)
        {

            await _context.Records.AddRangeAsync(EncounterNotes);
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    await _context.SaveChangesAsync();

                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    throw;
                }
            }
        }
        public async Task<List<WordTiming>> GetWordTimings(Guid recordId)
        {
            return await _context.WordTimings
                .Where(wt => wt.RecordId == recordId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Record>> GetByPCPIdAsync(Guid PCPId)
        {
            var records = await _context.Records.Where(r => r.PCPId == PCPId).ToListAsync();

            foreach (var record in records)
            {
                record.WordTimings = await GetWordTimings(record.Id);
            }
            records = await _context.Records
                .Where(r => r.PCPId == PCPId)
                .OrderByDescending(r => r.DateTime) 
                .ToListAsync();
            return records;
        }

        public async Task<IEnumerable<Record>> GetByPatientIdAsync(Guid PatientId)
        {
            var records = await _context.Records.Where(r => r.PatientId == PatientId).ToListAsync();

            foreach (var record in records)
            {
                record.WordTimings = await GetWordTimings(record.Id);
            }
            records = await _context.Records
                .Where(r => r.PatientId == PatientId)
                .OrderByDescending(r => r.DateTime)
                .ToListAsync();
            return records;
        }

    }
}
