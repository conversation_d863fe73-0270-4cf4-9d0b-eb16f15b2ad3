﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class ProductUserAccessRepository : IProductUserAccessRepository
    {
        private readonly AccountDatabaseContext _context;

        public ProductUserAccessRepository(AccountDatabaseContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Member>> GetMembersForProduct(Guid productId)
        {
            return await _context.ProductUserAccess
                .Where(pua => pua.ProductId == productId)
                .Select(pua => new Member
                {
                    Id = pua.Member.Id,
                    UserName = pua.Member.UserName,
                    Email = pua.Member.Email,
                    Password = pua.Member.Password,
                    IsActive = pua.HasAccess
                })
                .ToListAsync();
        }

        public async Task<ProductUserAccess> GetAccessRecordAsync(Guid productId, Guid memberId)
        {
            return await _context.ProductUserAccess
                .FirstOrDefaultAsync(a => a.ProductId == productId && a.MemberId == memberId);
        }
    }
}
