﻿using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models.TermStore;
using Syncfusion.Blazor.Navigations.Internal;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;

namespace TeyaWebApp.Components.Pages
{
    public partial class OfficeVisit : ComponentBase
    {
        private List<OfficeVisitModel> OfficeVisits = new List<OfficeVisitModel>();

        [Inject] private IOfficeVisitService _visitService { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private IChartService _chartService { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [CascadingParameter] private Admin AdminLayout { get; set; }
        [Inject] private ActiveUser User { get; set; }
        private Guid userid;
        private Guid? OrgID { get; set; }
        private bool Subscription { get;set; }

        /// <summary>
        /// Initializes the component by fetching the list of office visits for the user.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            userid = Guid.TryParse(User?.id, out Guid parsedId) ? parsedId : Guid.Empty;
            var member = await MemberService.GetMemberByIdAsync(userid);
            OrgID = member.OrganizationID;
            Subscription = false;
            OfficeVisits = await _visitService.GetPatientListByIdAsync(userid,OrgID,Subscription);
        }

        /// <summary>
        /// Redirects the user to the patient chart page.
        /// </summary>
        /// <param name="visitId">patient id - The ID of the office visit.</param>
        private async Task RedirectToChart(Guid visitId, String VisitStatus, String VisitType)
        {
            AdminLayout?.DrawerClose();
            await InvokeAsync(StateHasChanged);
            var patientData = await _chartService.GetPatientByIdAsync(visitId);
            PatientService.PatientData = patientData;
            PatientService.VisitStatus = VisitStatus;
            PatientService.VisitType = VisitType;  
            NavigationManager.NavigateTo("/Chart");
        }
    }
}
