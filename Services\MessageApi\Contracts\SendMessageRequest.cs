using System.Collections.Generic;

namespace MessageContracts
{
    public class SendMessageRequest
    {
        public string SenderName { get; set; } = string.Empty;
        public string SenderEmailId { get; set; } = string.Empty;
        public string ReceiverName { get; set; } = string.Empty;
        public string ReceiverEmailId { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string MessageContent { get; set; } = string.Empty;
        public List<string> ToEmails { get; set; } = new List<string>();
        public List<string> CcEmails { get; set; } = new List<string>();
        public List<string> BccEmails { get; set; } = new List<string>();
        public List<AttachmentRequest>? Attachments { get; set; }
    }
}
