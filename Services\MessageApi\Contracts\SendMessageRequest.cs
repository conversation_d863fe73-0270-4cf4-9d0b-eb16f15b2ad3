using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MessageContracts
{
    public class SendMessageRequest
    {
        [Required]
        [StringLength(100)]
        public string SenderName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string SenderEmailId { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ReceiverName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string ReceiverEmailId { get; set; } = string.Empty;

        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string MessageContent { get; set; } = string.Empty;

        // Multiple recipients support
        public List<string> ToEmails { get; set; } = new List<string>();
        public List<string> CcEmails { get; set; } = new List<string>();
        public List<string> BccEmails { get; set; } = new List<string>();

        // Optional attachments
        public List<AttachmentRequest>? Attachments { get; set; }
    }
}
