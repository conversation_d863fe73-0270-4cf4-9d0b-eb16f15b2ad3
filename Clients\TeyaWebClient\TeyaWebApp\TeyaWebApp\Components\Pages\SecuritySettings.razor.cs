using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaWebApp.TeyaAIScribeResources;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace TeyaWebApp.Components.Pages
{
    public partial class SecuritySettings : ComponentBase
    {
        [Inject]
        private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; } 

        [Inject]
        private ILogger<SecuritySettings> Logger { get; set; }

        private List<Product>? Products { get; set; }
        private List<Member>? Members { get; set; }
        private Product? SelectedProduct { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                Products = await ProductService.GetProductsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingProducts"]);
            }
        }

        private async Task OnRowSelected(RowSelectEventArgs<Product> args)
        {
            SelectedProduct = args.Data;

            try
            {
                Members = await MemberService.GetMembersForProductAsync(SelectedProduct.Id);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingMembersForProduct"], SelectedProduct.Id);
            }
        }

        private void UpdateMemberAccessLocally(Member member, object value)
        {
            member.IsActive = (bool)value;
        }

        private async Task SaveAllMemberAccess()
        {
            if (SelectedProduct != null && Members != null && Members.Any())
            {
                try
                {
                    var memberAccessUpdates = Members.Select(member => new MemberAccessUpdate
                    {
                        MemberId = member.Id,
                        HasAccess = member.IsActive
                    }).ToList();

                    await ProductService.UpdateMembersAccessAsync(SelectedProduct.Id, memberAccessUpdates);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, Localizer["ErrorSavingMemberAccessUpdates"], SelectedProduct.Id);
                }
            }
        }
    }
}