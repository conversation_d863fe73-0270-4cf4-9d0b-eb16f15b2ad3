﻿CREATE TABLE [EncounterNotesService].[PatientPhysicalExamination] (
    [ExaminationId]        UNIQUEIDENTIFIER NOT NULL,
    [PatientId]            UNIQUEIDENTIFIER NOT NULL,
    [OrganizationId]       UNIQUEIDENTIFIER NULL,
    [PCPId]                <PERSON>IQUEIDENTIFIER NULL,
    [CreatedDate]          DATETIME         NULL,
    [UpdatedDate]          DATETIME         NULL,
    [CreatedBy]            UNIQUEIDENTIFIER NOT NULL,
    [UpdatedBy]            UNIQUEIDENTIFIER NOT NULL,
    [Skin]                 NVARCHAR (MAX)   NULL,
    [Rash]                 NVARCHAR (MAX)   NULL,
    [Tester]               NVARCHAR (MAX)   NULL,
    [Mo<PERSON>]                NVARCHAR (MAX)   NULL,
    [<PERSON><PERSON><PERSON><PERSON>]           NVARCHAR (MAX)   NULL,
    [VascularMalformation] NVARCHAR (MAX)   NULL,
    [IsActive]             BIT              NULL,
    CONSTRAINT [PK_PatientPhysicalExamination] PRIMARY KEY CLUSTERED ([ExaminationId] ASC, [PatientId] ASC)
);

