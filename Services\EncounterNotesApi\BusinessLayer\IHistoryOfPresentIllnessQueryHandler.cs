﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
namespace EncounterNotesBusinessLayer
{
    public interface IHistoryOfPresentIllnessQueryHandler<TText>
    {
      
        Task<IEnumerable<TText>> GetHistoryOfPresentIllnessByPatientId(Guid patientId);
        Task<IEnumerable<TText>> GetHistoryOfPresentIllnessByPatientIdAndActive(Guid patientId);
       
    }
}