﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IVitalCommandHandler<TText>
    {
        Task DeleteVitalByEntity(Vitals Vital);
        Task DeleteVitalById(Guid id);
        Task AddVital(List<TText> texts);
        Task UpdateVital(Vitals vital);
        Task UpdateVitalList(List<Vitals> vitals);
    }
}
