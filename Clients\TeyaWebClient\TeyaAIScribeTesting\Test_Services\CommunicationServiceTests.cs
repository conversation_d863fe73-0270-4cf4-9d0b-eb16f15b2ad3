﻿//using Azure;
//using Azure.Communication.Email;
//using Moq;
//using System;
//using System.Collections.Generic;
//using System.Threading.Tasks;
//using NUnit.Framework;
//using TeyaUIViewModels.ViewModel;

//namespace TeyaAIScribeTesting.Test_Services
//{
//    [TestFixture]
//    public class CommunicationServiceTests
//    {
//        private CommunicationService _communicationService;

//        [SetUp]
//        public void Setup()
//        {
//            // Use the actual connection string for the email service
//            Environment.SetEnvironmentVariable("EMAIL_SERVICE_CONNECTION_STRING", "endpoint=https://email-to-teya-health-clients.unitedstates.communication.azure.com/;accesskey=59I0cKeCSXOXTh8YyGwQfADGiTp7DHe3k9A1YD3dkrDP4K3122eEJQQJ99BAACULyCpSlSHjAAAAAZCSYkLF");
//            _communicationService = new CommunicationService();
//        }


//        [Test]
//        public async Task MailService_ValidInput_SendsEmailSuccessfully()
//        {
//            // Arrange
//            var senderAddress = "<EMAIL>";
//            var subject = "Test Email";
//            var plainTextContent = "This is a test email.";
//            var recipientAddresses = new List<string> { "<EMAIL>" };

//            try
//            {
//                // Act
//                await _communicationService.MailService(senderAddress, subject, plainTextContent, recipientAddresses);

//                // Assert - Mocked client does not throw exceptions by default
//                Assert.Pass("Email was 'sent' successfully using the mock.");
//            }
//            catch (RequestFailedException ex)
//            {
//                // Assert - For real services with fake connection strings
//                Assert.Fail($"Expected success but got exception: {ex.Message}");
//            }
//        }

//    }
//}
