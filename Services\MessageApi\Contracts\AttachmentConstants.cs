namespace MessageContracts
{
    public static class AttachmentConstants
    {
        public const long MaxFileSizeBytes = 10 * 1024 * 1024;

        public static readonly string[] AllowedContentTypes = {
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp",
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "text/csv",
            "text/html",
            "text/xml",
            "application/json",
            "application/xml",
            "application/javascript",
            "application/zip",
            "application/x-rar-compressed",
            "application/x-7z-compressed",
            "audio/mpeg", "audio/wav", "audio/ogg",
            "video/mp4", "video/avi", "video/mov", "video/wmv"
        };

        public static readonly string[] BlockedExtensions = {
            ".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js", ".jar", ".msi"
        };
    }
}
