﻿using AppointmentContracts;
using AppointmentDataAccessLayer.AppointmentContext;
using AppointmentDataAccessLayer.DataAccessLayerResources;
using DotNetEnv;
using Microsoft.Azure.SqlDatabase.ElasticScale.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO.Hashing;
using Azure.Core;

namespace AppointmentDataAccessLayer.AppointmentImplementation
{
    public class Migration : IMigration
    {
        private readonly ShardMapManagerService _shardMapManagerService;
        private readonly IStringLocalizer<DataAccessLayerStrings> _localizer;
        private readonly ILogger<AppointmentDatabaseContext> _logger;
        const int zero=0,one=1,two=2;
        public Migration(ShardMapManagerService shardMapManagerService, IStringLocalizer<DataAccessLayerStrings> localizer, ILogger<AppointmentDatabaseContext> logger)
        {
            Env.Load();
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        public void CreateShardForSingleTenant(string serverName, string databaseName, byte[] newShardKey)
        {
            _shardMapManagerService.AddShard(serverName, databaseName, newShardKey);
        }
        public void CreateAndMigrateDataToNewShard(string serverName, string databaseName, byte[] newShardKey)
        {
            _shardMapManagerService.AddShard(serverName, databaseName, newShardKey);
            var allKeys = _shardMapManagerService.GetAllKeys()
            .Where(key => key.Length > zero && key[key.Length - one] == two) 
            .OrderBy(key => BitConverter.ToString(key)) 
            .ToList();

            var nextGreaterKey = allKeys.FirstOrDefault(key => CompareByteArrays(key, newShardKey) > zero);
            if (nextGreaterKey != null)
            {
                var dataToMigrate = GetDataFromShard(nextGreaterKey, newShardKey);
                if (dataToMigrate.Any())
                {
                    var newShard = _shardMapManagerService.GetShardForKey(newShardKey);
                    MigrateDataToNewLocation(dataToMigrate, newShard);
                    UpdateShardMapForKey(newShardKey, newShard);
                    DeleteMigratedDataFromSourceShard(nextGreaterKey, dataToMigrate);
                }
            }
        }

        private IEnumerable<Appointment> GetDataFromShard(byte[] nextGreaterKey, byte[] newShardKey)
        {
            var shard = _shardMapManagerService.GetShardForKey(nextGreaterKey);
            var connectionString = $"Server={shard.Location.Server};Initial Catalog={shard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";
            var optionsBuilder = new DbContextOptionsBuilder<AppointmentDatabaseContext>();
            optionsBuilder.UseSqlServer(connectionString);

            using (var context = new AppointmentDatabaseContext(optionsBuilder.Options, _localizer, _logger))
            {
                return context.Set<Appointment>().AsEnumerable() 
            .Where(a =>
            {
                var idBytes = a.Id.ToByteArray();
                Array.Resize(ref idBytes, idBytes.Length + one); 

                return CompareByteArrays(idBytes, newShardKey) <= zero; 
            })
            .ToList();
            }
        }

        private int CompareByteArrays(byte[] array1, byte[] array2)
        {
            for (int i = zero; i < Math.Min(array1.Length, array2.Length); i++)
            {
                if (array1[i] < array2[i]) return -one;
                if (array1[i] > array2[i]) return one;
            }
            return array1.Length.CompareTo(array2.Length); 
        }

        private void MigrateDataToNewLocation(IEnumerable<Appointment> data, Shard newShard)
        {
            var connectionString = $"Server={newShard.Location.Server};Initial Catalog={newShard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";

            var optionsBuilder = new DbContextOptionsBuilder<AppointmentDatabaseContext>();
            optionsBuilder.UseSqlServer(connectionString);

            using (var context = new AppointmentDatabaseContext(optionsBuilder.Options, _localizer, _logger))
            {
                foreach (var appointment in data)
                {
                    context.Set<Appointment>().Add(appointment);
                }
                context.SaveChanges();
            }
        }

        private void UpdateShardMapForKey(byte[] key, Shard newShard)
        {
            var shardMap = _shardMapManagerService.CreateOrGetListShardMap("MyExampleShardMap");

            try
            {
                if (shardMap.TryGetMappingForKey(key, out PointMapping<byte[]> mapping))
                {
                    if (mapping.Status == MappingStatus.Online)
                    {
                        mapping = shardMap.MarkMappingOffline(mapping);
                    }

                    shardMap.DeleteMapping(mapping);
                }
                shardMap.CreatePointMapping(key, newShard);
            }
            catch (ShardManagementException ex)
            {
                throw ex;
            }
        }

        private void DeleteMigratedDataFromSourceShard(byte[] nextGreaterKey, IEnumerable<Appointment> dataToMigrate)
        {
            var shard = _shardMapManagerService.GetShardForKey(nextGreaterKey);

            var connectionString = $"Server={shard.Location.Server};Initial Catalog={shard.Location.Database}; {Environment.GetEnvironmentVariable("ConnectionString")}";
            var optionsBuilder = new DbContextOptionsBuilder<AppointmentDatabaseContext>();
            optionsBuilder.UseSqlServer(connectionString);

            using (var context = new AppointmentDatabaseContext(optionsBuilder.Options, _localizer, _logger))
            {
                foreach (var appointment in dataToMigrate)
                {
                    context.Set<Appointment>().Remove(appointment);
                }
                context.SaveChanges();
            }
        }
    }
}
