using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public class AttachmentService : IAttachmentService
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AttachmentService> _logger;
        private readonly IAzureBlobStorageService _blobStorageService;

        public AttachmentService(
            IConfiguration configuration,
            IUnitOfWork unitOfWork,
            ILogger<AttachmentService> logger,
            IAzureBlobStorageService blobStorageService)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _blobStorageService = blobStorageService;
        }

        public async Task<List<MessageAttachment>> ProcessAttachmentsAsync(Guid messageId, List<AttachmentRequest>? attachments)
        {
            _logger.LogInformation($"ProcessAttachmentsAsync called for message {messageId} with {attachments?.Count ?? 0} attachments");
            var processedAttachments = new List<MessageAttachment>();

            if (attachments == null || !attachments.Any())
            {
                _logger.LogInformation("No attachments to process");
                return processedAttachments;
            }

            _logger.LogInformation($"Processing {attachments.Count} attachments for message {messageId} using Azure Blob Storage");

            foreach (var attachment in attachments)
            {
                try
                {
                    _logger.LogInformation($"Processing attachment: {attachment.FileName}");

                    // Validate attachment
                    if (!ValidateAttachment(attachment))
                    {
                        _logger.LogWarning($"Invalid attachment: {attachment.FileName} - skipping");
                        continue;
                    }

                    // Decode base64 content
                    var fileContent = Convert.FromBase64String(attachment.FileContentBase64);

                    // Upload to Azure Blob Storage
                    var blobFileName = await _blobStorageService.UploadAttachmentAsync(
                        fileContent,
                        attachment.FileName,
                        attachment.ContentType);

                    // Get blob URL
                    var blobUrl = _blobStorageService.GetAttachmentUrl(blobFileName);

                    // Calculate file hash for integrity
                    var fileHash = CalculateFileHash(fileContent);

                    // Get container name from configuration
                    var containerName = Environment.GetEnvironmentVariable("AZURE_BLOB_CONTAINER_NAME")
                        ?? _configuration["AzureBlobStorage:ContainerName"]
                        ?? "message-attachments";

                    // Create attachment entity (blob storage only)
                    var messageAttachment = new MessageAttachment
                    {
                        AttachmentId = Guid.NewGuid(),
                        MessageId = messageId,
                        FileName = blobFileName, // Use blob file name
                        OriginalFileName = attachment.FileName,
                        ContentType = attachment.ContentType,
                        FileSizeBytes = fileContent.Length,
                        BlobFileName = blobFileName,
                        BlobStorageUrl = blobUrl,
                        BlobContainerName = containerName,
                        FileHash = fileHash,
                        UploadedDateTime = DateTime.UtcNow,
                        IsScanned = false, // In production, integrate with virus scanning
                        IsSafe = true,
                        // Legacy fields - set placeholder values for database compatibility
                        FilePath = $"BLOB_STORAGE:{blobFileName}", // Placeholder indicating blob storage
                        FileContentBase64 = null // Don't store content in database anymore
                    };

                    processedAttachments.Add(messageAttachment);

                    _logger.LogInformation($"Successfully processed attachment: {attachment.FileName} ({fileContent.Length} bytes) -> Blob: {blobFileName}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing attachment: {attachment.FileName}");
                    // Continue processing other attachments even if one fails
                }
            }

            return processedAttachments;
        }

        public async Task<MessageAttachment?> GetAttachmentAsync(Guid attachmentId)
        {
            try
            {
                return await _unitOfWork.AttachmentRepository.GetByIdAsync(attachmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachment: {attachmentId}");
                return null;
            }
        }

        public async Task<byte[]> GetAttachmentContentAsync(Guid attachmentId)
        {
            try
            {
                var attachment = await GetAttachmentAsync(attachmentId);
                if (attachment == null)
                {
                    throw new FileNotFoundException($"Attachment not found: {attachmentId}");
                }

                _logger.LogInformation($"Getting attachment content from blob storage: {attachment.BlobFileName}");

                // Download from Azure Blob Storage
                if (!string.IsNullOrEmpty(attachment.BlobFileName))
                {
                    return await _blobStorageService.DownloadAttachmentAsync(attachment.BlobFileName);
                }

                // Fallback for legacy attachments (if any exist with base64 content)
                if (!string.IsNullOrEmpty(attachment.FileContentBase64))
                {
                    _logger.LogWarning($"Using legacy base64 content for attachment: {attachmentId}");
                    return Convert.FromBase64String(attachment.FileContentBase64);
                }

                throw new FileNotFoundException($"Attachment content not found in blob storage: {attachment.BlobFileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachment content: {attachmentId}");
                throw;
            }
        }

        public async Task<bool> DeleteAttachmentAsync(Guid attachmentId)
        {
            try
            {
                var attachment = await GetAttachmentAsync(attachmentId);
                if (attachment == null)
                {
                    return false;
                }

                // Delete from Azure Blob Storage
                if (!string.IsNullOrEmpty(attachment.BlobFileName))
                {
                    await _blobStorageService.DeleteAttachmentAsync(attachment.BlobFileName);
                    _logger.LogInformation($"Deleted attachment from blob storage: {attachment.BlobFileName}");
                }

                // Delete from database
                await _unitOfWork.AttachmentRepository.DeleteByIdAsync(attachmentId);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Deleted attachment: {attachmentId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting attachment: {attachmentId}");
                return false;
            }
        }

        public async Task<List<MessageAttachment>> GetMessageAttachmentsAsync(Guid messageId)
        {
            try
            {
                return await _unitOfWork.AttachmentRepository.GetAttachmentsByMessageIdAsync(messageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachments for message: {messageId}");
                return new List<MessageAttachment>();
            }
        }

        public bool ValidateAttachment(AttachmentRequest attachment)
        {
            _logger.LogInformation($"Validating attachment: {attachment.FileName}, ContentType: {attachment.ContentType}, Size: {attachment.FileSizeBytes} bytes");

            // Check file size
            if (attachment.FileSizeBytes > AttachmentConstants.MaxFileSizeBytes)
            {
                _logger.LogWarning($"File too large: {attachment.FileName} ({attachment.FileSizeBytes} bytes), Max: {AttachmentConstants.MaxFileSizeBytes}");
                return false;
            }

            // Check content type
            var normalizedContentType = attachment.ContentType.ToLower();
            if (!AttachmentConstants.AllowedContentTypes.Contains(normalizedContentType))
            {
                _logger.LogWarning($"Content type not allowed: {attachment.ContentType} (normalized: {normalizedContentType})");
                _logger.LogInformation($"Allowed content types: {string.Join(", ", AttachmentConstants.AllowedContentTypes)}");
                return false;
            }

            // Check file extension
            var extension = Path.GetExtension(attachment.FileName).ToLower();
            if (AttachmentConstants.BlockedExtensions.Contains(extension))
            {
                _logger.LogWarning($"File extension blocked: {extension}");
                return false;
            }

            // Validate base64 content
            try
            {
                var content = Convert.FromBase64String(attachment.FileContentBase64);
                if (content.Length != attachment.FileSizeBytes)
                {
                    _logger.LogWarning($"File size mismatch: {attachment.FileName}, Expected: {attachment.FileSizeBytes}, Actual: {content.Length}");
                    return false;
                }
            }
            catch (FormatException ex)
            {
                _logger.LogWarning(ex, $"Invalid base64 content: {attachment.FileName}");
                return false;
            }

            _logger.LogInformation($"Attachment validation successful: {attachment.FileName}");
            return true;
        }

        public async Task<string> SaveAttachmentToStorageAsync(string fileName, byte[] content, string contentType)
        {
            // This method is now obsolete - we use Azure Blob Storage directly
            // Kept for interface compatibility but redirects to blob storage
            _logger.LogWarning("SaveAttachmentToStorageAsync is obsolete - use Azure Blob Storage directly");

            var blobFileName = await _blobStorageService.UploadAttachmentAsync(content, fileName, contentType);
            return _blobStorageService.GetAttachmentUrl(blobFileName);
        }

        private string CalculateFileHash(byte[] content)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(content);
            return Convert.ToBase64String(hash);
        }
    }
}
