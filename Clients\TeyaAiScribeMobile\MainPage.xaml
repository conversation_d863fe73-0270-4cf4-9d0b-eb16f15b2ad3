    <ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                 xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                 x:Class="TeyaAiScribeMobile.MainPage"
                 Shell.NavBarIsVisible="True"
                 Title="MainPage">
        <Shell.TitleView>
            <Grid Padding="10" ColumnSpacing="20">
            <!--<ImageButton Source="burger_icon.png" BackgroundColor="Transparent" Clicked="OnFlyoutButtonClicked" />-->
            <!--<ImageButton Source="burger_icon.png" BackgroundColor="Transparent" Clicked="OnFlyoutButtonClicked" HorizontalOptions="Start" />-->
            <ImageButton Source="burger_icon.png" 
                         BackgroundColor="Transparent" 
                         Clicked="OnFlyoutButtonClicked" 
                         InputTransparent="True" />
            <!-- Center Text with Cursive Font -->
                <Label Text="Unleash AI Recorder" 
                       FontSize="24" 
                       FontAttributes="Italic" 
                       HorizontalOptions="Center" 
                       TextColor="Black" 
                       InputTransparent="True" />
                <!-- Make center text non-interactive -->

                <!-- Right-Aligned App Logo -->
                <Image Source="teyalogo.jpg" 
                       WidthRequest="40" 
                       HeightRequest="40" 
                       HorizontalOptions="End" 
                       VerticalOptions="Center" 
                       InputTransparent="True" 
                        Margin="10,0,0,0"/>
                <!-- Make app logo non-interactive -->
            </Grid>
        </Shell.TitleView>

        <Grid>
            <!-- Main Content -->
            <VerticalStackLayout VerticalOptions="Center" HorizontalOptions="Center" Spacing="20">
                <!-- Microphone Icon and Capture Conversation -->

            <Frame BackgroundColor="#E0E0E0" 
       Padding="10" 
       CornerRadius="50" 
       HasShadow="True" 
       HorizontalOptions="Center">
                <Frame.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnFrameTapped" />
                </Frame.GestureRecognizers>
                <HorizontalStackLayout HorizontalOptions="Center" Spacing="10">
                    <Image Source="mic_icon.png" 
               WidthRequest="30" 
               HeightRequest="30" 
               VerticalOptions="Center" />
                    <Label Text="Capture conversation" 
               FontSize="20" 
               VerticalOptions="Center" 
               TextColor="Black" />
                </HorizontalStackLayout>
            </Frame>

            <HorizontalStackLayout HorizontalOptions="Center" Spacing="20" Margin="0,20,0,0">
                <Button Text="Pause" 
            BackgroundColor="#F44336" 
            TextColor="White" 
            CornerRadius="25" 
            WidthRequest="100" 
            HeightRequest="50" 
            Clicked="OnPauseClicked" />
                <Button Text="Resume" 
            BackgroundColor="#4CAF50" 
            TextColor="White" 
            CornerRadius="25" 
            WidthRequest="100" 
            HeightRequest="50" 
            Clicked="OnResumeClicked" />
                <Button Text="Stop" 
            BackgroundColor="#9E9E9E" 
            TextColor="White" 
            CornerRadius="25" 
            WidthRequest="100" 
            HeightRequest="50" 
            Clicked="OnStopClicked" />
            </HorizontalStackLayout>


            <!-- Separator Line -->
                <HorizontalStackLayout HorizontalOptions="Center" Spacing="10">
                    <BoxView HeightRequest="1" 
                             WidthRequest="100" 
                             Color="Gray" 
                             Margin="10" />
                    <Label Text="or" 
                           FontSize="18" 
                           TextColor="Black" 
                           HorizontalOptions="Center" />
                    <BoxView HeightRequest="1" 
                             WidthRequest="100" 
                             Color="Gray" 
                             Margin="10" />
                </HorizontalStackLayout>

                <!-- Upload Field -->
                <HorizontalStackLayout HorizontalOptions="Center" Spacing="10">
                    <Label Text="Upload" 
                           FontSize="18" 
                           TextColor="Blue" 
                           TextDecorations="Underline" 
                           HorizontalOptions="Center" 
                           VerticalOptions="Center">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding UploadCommand}" />
                        </Label.GestureRecognizers>
                    </Label>
                    <Label Text="a prerecorded visit" 
                           FontSize="18" 
                           TextColor="Black" 
                           HorizontalOptions="Center" />
                </HorizontalStackLayout>
            </VerticalStackLayout>

            <!-- Recorded Notes Section at the Bottom -->
            <VerticalStackLayout VerticalOptions="End" HorizontalOptions="FillAndExpand" Padding="10" Spacing="10">
                <!-- Separator Line -->
                <BoxView BackgroundColor="Gray" HeightRequest="1" HorizontalOptions="FillAndExpand" />

                <!-- Notes Icon and Text -->
            <HorizontalStackLayout HorizontalOptions="Start" Spacing="10" Padding="10">
                <!-- Fluent Icon for Notes -->
                <Label Text="&#xE70E;" 
           FontFamily="FluentIcons" 
           FontSize="25" 
           TextColor="Black" 
           VerticalOptions="Center" />
                <!-- Text Label for Recorded Notes -->
                <Label Text="Recorded Notes" 
           FontSize="18" 
           TextColor="Black" 
           VerticalOptions="Center" />
            </HorizontalStackLayout>

        </VerticalStackLayout>
        </Grid>
    </ContentPage>
