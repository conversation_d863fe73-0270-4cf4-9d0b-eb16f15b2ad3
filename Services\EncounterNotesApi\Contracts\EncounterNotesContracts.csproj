﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="Carbon.Redis" Version="2.8.2" />
    <PackageReference Include="Concentus" Version="2.2.2" />
    <PackageReference Include="Concentus.Oggfile" Version="1.0.6" />
    <PackageReference Include="FFmpegBlazor" Version="1.0.0.10" />
    <PackageReference Include="FFMpegCore" Version="5.1.0" />
    <PackageReference Include="Google_GenerativeAI" Version="1.0.2" />
    <PackageReference Include="Markdig" Version="0.40.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.10" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.10" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.5.0" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.24.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Text.Json" Version="9.0.2" />
    <PackageReference Include="Xabe.FFmpeg" Version="5.2.6" />
  </ItemGroup>

</Project>
