﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public interface IGenericRepository<T> where T : class
    {
        // Retrieve all entities
        Task<IEnumerable<T>> GetAllAsync();

        // Retrieve all entities (alternative method name)
        Task<IEnumerable<T>> GetAsync();

        // Retrieve all entities including soft-deleted ones
        Task<IEnumerable<T>> GetAsync(bool includeDeleted);

        // Retrieve an entity by its primary key
        Task<T> GetByIdAsync(Guid id);

        // Retrieve an entity by its primary key including soft-deleted ones
        Task<T> GetByIdAsync(Guid id, bool includeDeleted);

        // Add multiple new entities
        Task AddAsync(IEnumerable<T> entities);

        // Update an existing entity
        Task UpdateAsync(T entity);

        // Delete an existing entity
        Task DeleteByEntityAsync(T entity);

        // Delete an existing id
        Task DeleteByIdAsync(Guid id);

        Task UpdateRangeAsync(IEnumerable<T> entities);

    }
}
