﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResource;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp;

public class ChiefComplaintService : IChiefComplaintService
{
    private readonly HttpClient _httpClient;
    private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
    private readonly string _EncounterNotes;
    private readonly ITokenService _tokenService;
    private readonly ILogger<ChiefComplaintService> _logger;

    public ChiefComplaintService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService, ILogger<ChiefComplaintService> logger)
    {
        _httpClient = httpClient;
        _localizer = localizer;
        _logger = logger;
        _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
        _tokenService = tokenService;
        _logger = logger;
    }
    public async Task<List<ChiefComplaintDTO>> GetAllComplaintsAsync()
    {
        try
        {
            
            var token = _tokenService.AccessToken;

          
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            
            return await _httpClient.GetFromJsonAsync<List<ChiefComplaintDTO>>($"{_EncounterNotes}/api/ChiefComplaint");
        }
        catch (Exception ex)
        {
            
            throw new Exception("Error retrieving complaints: " + ex.Message);
        }
    }
   
    public async Task AddAsync(ChiefComplaintDTO complaintDto)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PostAsJsonAsync($"{_EncounterNotes}/api/ChiefComplaint", complaintDto);
            response.EnsureSuccessStatusCode();  
        }
        catch (Exception ex)
        {
            throw new Exception("Error adding complaint: " + ex.Message);
        }
    }

    
    public async Task UpdateComplaintAsync(Guid id, ChiefComplaintDTO complaintDto)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PutAsJsonAsync($"{_EncounterNotes}/api/ChiefComplaint/{id}", complaintDto);
            response.EnsureSuccessStatusCode();  
        }
        catch (Exception ex)
        {
            throw new Exception("Error updating complaint: " + ex.Message);
        }
    }

    public async Task DeleteComplaintAsync(Guid id)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.DeleteAsync($"{_EncounterNotes}/api/ChiefComplaint/{id}");
            response.EnsureSuccessStatusCode();  
        }
        catch (Exception ex)
        {
            throw new Exception("Error deleting complaint: " + ex.Message);
        }
    }
    
    public async Task<IEnumerable<ChiefComplaintDTO>> GetByPatientIdAsync(Guid patientId)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ChiefComplaintDTO>>($"{_EncounterNotes}/api/ChiefComplaint/patient/{patientId}");
            return response;
        }
        catch (Exception ex)
        {
            throw new Exception($"Error retrieving complaints for PatientId {patientId}: " + ex.Message);
        }
    }

    public Task AddAsync(List<ChiefComplaintDTO> complaintList)
    {
        throw new NotImplementedException();
    }

    public async Task UpdateComplaintListAsync(List<ChiefComplaintDTO> complaints)
    {
        try
        {
            if (complaints == null || !complaints.Any())
            {
                throw new ArgumentException("Complaint list is empty.", nameof(complaints));
            }

            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            
            var response = await _httpClient.PutAsJsonAsync($"{_EncounterNotes}/api/ChiefComplaint/bulk-update", complaints);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception("Bulk update failed: " + response.ReasonPhrase);
            }
        }
        catch (Exception ex)
        {
            throw new Exception("Error during bulk update of complaints: " + ex.Message);
        }
    }


    public async Task<List<ChiefComplaintDTO>> LoadComplaintsAsync(Guid patientId)
    {
        try
        {
            var result = await GetByPatientIdAsync(patientId);
            return result.Select(c => new ChiefComplaintDTO
            {
                Id = c.Id,
                PatientId = c.PatientId,
                Description = c.Description,
                DateOfComplaint = c.DateOfComplaint,
                OrganizationId = c.OrganizationId,
                PcpId = c.PcpId,
                IsDeleted = c.IsDeleted
            }).ToList();
        }
        catch (Exception ex)
        {
            throw new Exception("Error loading complaints: " + ex.Message);
        }
    }

    public async Task<List<ChiefComplaintDTO>> GetProcessedComplaintsAsync()
    {
        try
        {
            var chiefComplaints = await GetAllComplaintsAsync();

            return chiefComplaints
                .Where(c => !string.IsNullOrWhiteSpace(c.Description))
                .GroupBy(c => c.Description, StringComparer.OrdinalIgnoreCase)
                .Select(g => new ChiefComplaintDTO
                {
                    Description = g.First().Description,
                    Id = g.First().Id,
                    DateOfComplaint = g.First().DateOfComplaint,
                    PatientId = g.First().PatientId,
                    OrganizationId = g.First().OrganizationId,
                    PcpId = g.First().PcpId,
                    IsDeleted = g.First().IsDeleted
                })
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, _localizer["ErrorFetchingComplaints"]);
            
            return new List<ChiefComplaintDTO>();
        }
    }
}