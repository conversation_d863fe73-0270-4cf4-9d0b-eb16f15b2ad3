﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class VitalQueryHandler : IVitalQueryHandler<Vitals>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public VitalQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Vitals>> GetVitalsByIdAndIsActive(Guid id)
        {
            var vitals = await _unitOfWork.VitalRepository.GetAsync();

            return vitals.Where(Vitals => Vitals.PatientId == id && Vitals.isActive == true);
        }

        public async Task<IEnumerable<Vitals>> GetAllVitalsbyId(Guid id)
        {
            var vitals = await _unitOfWork.VitalRepository.GetAsync();

            return vitals.Where(Vitals => Vitals.PatientId == id);
        }
    }
}
