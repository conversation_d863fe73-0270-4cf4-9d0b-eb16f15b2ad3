﻿@page "/email"
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Components.Forms
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@inject NavigationManager NavigationManager
@using TeyaWebApp.Components.Layout
@using MudBlazor
@layout Admin
@rendermode InteractiveServer


<MudContainer MaxWidth="MaxWidth.False" Class="email-app">
    <!-- Left Sidebar -->
    <MudPaper Class="sidebar @(selectedMessage != null ? "collapsed" : "")" Elevation="1">
        <MudButton Class="compose-btn" @onclick="ToggleCompose" StartIcon="fas fa-edit" FullWidth="true">
            Compose
        </MudButton>



        <MudNavMenu Class="menu">
            @foreach (var item in menuItems)
            {
                @if (item.IsCategory)
                {
                    <MudText Class="category" Typo="Typo.subtitle2">@item.Name</MudText>
                }
                else
                {
                    <MudNavLink Class="@(item.IsActive ? "active" : "")" data-folder="@item.Folder" @onclick="() => SelectMenuItem(item)">
                        <MudIcon Class="menu-icon" Icon="@item.Icon" />
                        <MudText Class="menu-text">@item.Name</MudText>
                        @if (item.Count > 0)
                        {
                            <MudChip Class="count" Size="Size.Small" Color="Color.Primary">@item.Count</MudChip>
                        }
                    </MudNavLink>
                }
            }
        </MudNavMenu>
    </MudPaper>

    <!-- Main Content -->
    <MudPaper Class="main" Elevation="0">
        <!-- Toolbar -->
        <MudPaper Class="toolbar" Elevation="1">
            <MudTabs Class="tabs" Elevation="0" Rounded="true" PanelClass="pa-6">
                <MudTabPanel Class="tab @(selectedTab == "all" ? "active" : "")" Text="All mail" @onclick='() => SetTab("all")' />
                <MudTabPanel Class="tab @(selectedTab == "unread" ? "active" : "")" Text="Unread" @onclick='() => SetTab("unread")' />
            </MudTabs>
            <MudPaper Class="search-box" Elevation="0">
                <MudTextField Class="search-input"
                             Placeholder="Search mail"
                             @bind-Value="searchQuery"
                             Adornment="Adornment.Start"
                             AdornmentIcon="fas fa-search"
                             Immediate="true" />
            </MudPaper>
        </MudPaper>

        <!-- Compose Modal -->
        @if (showCompose)
        {
            <MudDialog Class="compose-container" @bind-IsOpen="showCompose">
                <DialogContent>
                    <MudPaper Class="compose-header" Elevation="0">
                        <MudText Typo="Typo.h6">New Message</MudText>
                        <MudSpacer />
                        <MudIconButton Class="btn-icon" @onclick="ToggleCompose" Icon="fas fa-times" />
                    </MudPaper>
                    <MudPaper Class="compose-body" Elevation="0">
                        <MudGrid>
                            <MudItem xs="12" Class="compose-field">
                                <MudTextField Label="To:"
                                            Class="compose-input"
                                            Placeholder="Recipients (separate multiple emails with commas)"
                                            @bind-Value="recipientEmails"
                                            FullWidth="true" />
                            </MudItem>

                            <MudItem xs="12" Class="compose-field">
                                <MudTextField Label="Cc:"
                                            Class="compose-input"
                                            Placeholder="Cc recipients (optional)"
                                            @bind-Value="ccEmails"
                                            FullWidth="true" />
                            </MudItem>

                            <MudItem xs="12" Class="compose-field">
                                <MudTextField Label="Subject:"
                                            Class="compose-input"
                                            Placeholder="Subject"
                                            @bind-Value="newMessage.Subject"
                                            FullWidth="true" />
                            </MudItem>

                            <MudItem xs="12" Class="compose-editor">
                                <MudTextField @bind-Value="newMessage.MessageContent"
                                            Placeholder="Compose your message..."
                                            Lines="10"
                                            FullWidth="true" />
                            </MudItem>
                        </MudGrid>

                        <!-- Attachment Section -->
                        @if (attachments.Any())
                        {
                            <MudPaper Class="attachments-section" Elevation="0">
                                <MudText Typo="Typo.h6">Attachments:</MudText>
                                @foreach (var attachment in attachments)
                                {
                                    <MudPaper Class="attachment-item" Elevation="1">
                                        <MudIcon Icon="fas fa-file" />
                                        <MudText>@attachment.FileName (@(attachment.FileSizeBytes / 1024)KB)</MudText>
                                        <MudIconButton Class="btn-remove" @onclick="() => RemoveAttachment(attachment)" Icon="fas fa-times" />
                                    </MudPaper>
                                }
                            </MudPaper>
                        }

                        <!-- File Upload -->
                        <MudPaper Class="file-upload-section" Elevation="0">
                            <InputFile OnChange="OnFileSelected" multiple accept="*/*" class="file-input" id="fileInput" />
                            <MudButton Class="file-upload-btn" HtmlTag="label" for="fileInput" StartIcon="fas fa-paperclip">
                                Add Attachment
                            </MudButton>
                        </MudPaper>
                    </MudPaper>
                </DialogContent>
                <DialogActions>
                    <MudPaper Class="compose-footer" Elevation="0" Style="width: 100%;">
                        <MudGrid Class="compose-actions" AlignItems="Center">
                            <MudItem Class="left-actions">
                                @if (attachments.Any())
                                {
                                    <MudChip Class="attachment-count" Icon="fas fa-paperclip" Size="Size.Small">
                                        @attachments.Count file(s)
                                    </MudChip>
                                }
                            </MudItem>
                            <MudSpacer />
                            <MudItem>
                                <MudButton Class="send-btn" @onclick="SendEmail" Disabled="@isSending"
                                         StartIcon="@(isSending ? "fas fa-spinner fa-spin" : "fas fa-paper-plane")"
                                         Color="Color.Primary" Variant="Variant.Filled">
                                    @(isSending ? "Sending..." : "Send")
                                </MudButton>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>
                </DialogActions>
            </MudDialog>
        }

        <!-- Reply functionality now uses the main compose modal -->


        <!-- Content Area -->
        <MudPaper Class="content" Elevation="0">
            <!-- Email List -->
            <MudPaper Class="email-list @(selectedMessage != null ? "collapsed" : "")" Elevation="0">
                @foreach (var message in FilteredMessages)
                {
                    <MudPaper Class="email-item @(message == selectedMessage ? "selected" : "")"
                             @onclick="() => SelectMessage(message)" Elevation="1">
                        <MudPaper Class="email-preview" Elevation="0">
                            <MudGrid Class="email-header-row" Justify="Justify.Center">
                                <MudItem Class="sender-info" Style="display: flex; align-items: center; flex: 1;">
                                    <MudPaper Class="sender-avatar" Elevation="0">
                                        <MudAvatar Class="avatar-circle">
                                            @if (currentFolder == "sent")
                                            {
                                                @GetInitials(message.ReceiverName)
                                            }
                                            else
                                            {
                                                @GetInitials(message.SenderName)
                                            }
                                        </MudAvatar>
                                    </MudPaper>
                                    <MudPaper Class="sender-details" Elevation="0" Style="margin-left: 12px; flex: 1;">
                                        @if (currentFolder == "sent")
                                        {
                                            <MudText Class="sender-name" Typo="Typo.body1">@message.ReceiverName</MudText>
                                            <MudText Class="sender-email" Typo="Typo.body2">@message.ReceiverEmailId</MudText>
                                        }
                                        else
                                        {
                                            <MudText Class="sender-name" Typo="Typo.body1">@message.SenderName</MudText>
                                            <MudText Class="sender-email" Typo="Typo.body2">@message.SenderEmailId</MudText>
                                        }
                                    </MudPaper>
                                    <MudText Class="email-time" Typo="Typo.caption">@GetRelativeTime(message.SentDateTime)</MudText>
                                </MudItem>
                                <MudItem Class="email-actions" Style="display: flex; gap: 8px;">
                                    @if (message.IsImportant)
                                    {
                                        <MudTooltip Text="Remove from Important">
                                            <MudIconButton Class="action-btn important active" @onclick:stopPropagation="true" @onclick="() => ToggleImportant(message)"
                                                         Icon="fas fa-star" Size="Size.Small" />
                                        </MudTooltip>
                                    }
                                    else
                                    {
                                        <MudIconButton Class="action-btn important" @onclick:stopPropagation="true" @onclick="() => ToggleImportant(message)"
                                                     Icon="far fa-star" Title="Mark as Important" Size="Size.Small" />
                                    }

                                    @if (currentFolder == "trash")
                                    {
                                        <MudIconButton Class="action-btn restore" @onclick:stopPropagation="true" @onclick="() => RestoreFromTrash(message)"
                                                     Icon="fas fa-undo" Title="Restore" Size="Size.Small" />
                                        <MudIconButton Class="action-btn delete-permanent" @onclick:stopPropagation="true" @onclick="() => PermanentlyDeleteMessage(message)"
                                                     Icon="fas fa-trash-alt" Title="Delete Permanently" Size="Size.Small" />
                                    }
                                    else
                                    {
                                        @if (message.IsArchived)
                                        {
                                            <MudIconButton Class="action-btn archive active" @onclick:stopPropagation="true" @onclick="() => UnarchiveMessage(message)"
                                                         Icon="fas fa-archive" Title="Unarchive" Size="Size.Small" />
                                        }
                                        else
                                        {
                                            <MudIconButton Class="action-btn archive" @onclick:stopPropagation="true" @onclick="() => ArchiveMessage(message)"
                                                         Icon="fas fa-archive" Title="Archive" Size="Size.Small" />
                                        }
                                        <MudIconButton Class="action-btn delete" @onclick:stopPropagation="true" @onclick="() => ShowDeleteConfirmation(message)"
                                                     Icon="fas fa-trash" Title="Delete" Size="Size.Small" />
                                    }
                                </MudItem>
                            </MudGrid>

                            <MudGrid Class="subject-line" AlignItems="Center">
                                <MudItem Style="flex: 1;">
                                    <MudText Class="subject" Typo="Typo.subtitle1">@message.Subject</MudText>
                                </MudItem>
                                <MudItem Class="email-indicators" Style="display: flex; gap: 4px; align-items: center;">
                                    @if (currentFolder == "sent")
                                    {
                                        <MudTooltip Text="@GetDeliveryStatusText(message.Status)">
                                            <MudIcon Class="delivery-status" Icon="@GetDeliveryStatusIcon(message.Status)" Size="Size.Small" />
                                        </MudTooltip>
                                    }
                                    @if (!message.IsRead && currentFolder != "sent")
                                    {
                                        <MudIcon Class="unread-dot" Icon="fas fa-circle" Size="Size.Small" Color="Color.Primary" />
                                    }
                                    @if (message.IsArchived)
                                    {
                                        <MudTooltip Text="Archived">
                                            <MudIcon Class="archived-indicator" Icon="fas fa-archive" Size="Size.Small" />
                                        </MudTooltip>
                                    }
                                </MudItem>
                            </MudGrid>
                            <MudText Class="preview-text" Typo="Typo.body2">@message.GetPreviewText()</MudText>
                            <MudGrid Class="email-meta" AlignItems="Center">
                                <MudItem Class="labels" Style="display: flex; gap: 4px;">
                                    @foreach (var label in message.Labels)
                                    {
                                        <MudChip Class="label" Size="Size.Small" Text="@label" />
                                    }
                                </MudItem>
                                <MudSpacer />
                                @if (message.HasAttachment)
                                {
                                    <MudItem>
                                        <MudIcon Class="attachment-icon" Icon="fas fa-paperclip" Size="Size.Small" />
                                    </MudItem>
                                }
                            </MudGrid>
                        </MudPaper>
                    </MudPaper>
                }
            </MudPaper>

            <!-- Email Detail View -->
            <MudPaper Class="email-detail @(selectedMessage != null ? "expanded" : "")" Elevation="1">
                @if (selectedMessage != null)
                {
                    <!-- Email Header with Subject and Actions -->
                    <MudPaper Class="email-detail-header" Elevation="0">
                        <MudGrid Class="subject-row" AlignItems="Center">
                            <MudItem Style="flex: 1;">
                                <MudText Class="email-subject" Typo="Typo.h4">@selectedMessage.Subject</MudText>
                            </MudItem>
                            <MudItem Class="header-actions" Style="display: flex; gap: 8px;">
                                @if (selectedMessage.IsImportant)
                                {
                                    <MudIconButton Class="action-btn important active" @onclick="() => ToggleImportant(selectedMessage)"
                                                 Icon="fas fa-star" Title="Remove from Important" />
                                }
                                else
                                {
                                    <MudIconButton Class="action-btn important" @onclick="() => ToggleImportant(selectedMessage)"
                                                 Icon="far fa-star" Title="Mark as Important" />
                                }

                                @if (currentFolder == "trash")
                                {
                                    <MudIconButton Class="action-btn restore" @onclick="() => RestoreFromTrash(selectedMessage)"
                                                 Icon="fas fa-undo" Title="Restore" />
                                    <MudIconButton Class="action-btn delete-permanent" @onclick="() => PermanentlyDeleteMessage(selectedMessage)"
                                                 Icon="fas fa-trash-alt" Title="Delete Permanently" />
                                }
                                else
                                {
                                    @if (selectedMessage.IsArchived)
                                    {
                                        <MudIconButton Class="action-btn archive active" @onclick="() => UnarchiveMessage(selectedMessage)"
                                                     Icon="fas fa-archive" Title="Unarchive" />
                                    }
                                    else
                                    {
                                        <MudIconButton Class="action-btn archive" @onclick="() => ArchiveMessage(selectedMessage)"
                                                     Icon="fas fa-archive" Title="Archive" />
                                    }
                                    <MudIconButton Class="action-btn delete" @onclick="() => ShowDeleteConfirmation(selectedMessage)"
                                                 Icon="fas fa-trash" Title="Delete" />
                                }
                                <MudIconButton Class="action-btn reply" @onclick="() => ShowReply()"
                                             Icon="fas fa-reply" Title="Reply" />
                                <MudIconButton Class="action-btn more" Icon="fas fa-ellipsis-v" Title="More actions" />
                            </MudItem>
                        </MudGrid>
                    </MudPaper>

                        <!-- Sender/Recipient Information -->
                        <div class="email-participants">
                            <div class="participant-row">
                                <div class="participant-avatar">
                                    <div class="avatar-circle">
                                        @GetInitials(selectedMessage.SenderName)
                                    </div>
                                </div>
                                <div class="participant-details">
                                    <div class="participant-info">
                                        <span class="participant-name">@selectedMessage.SenderName</span>
                                        <span class="participant-email">&lt;@selectedMessage.SenderEmailId&gt;</span>
                                    </div>
                                    <div class="email-recipients">
                                        <span class="recipient-label">to</span>
                                        <span class="recipient-list">
                                            @if (currentFolder == "sent")
                                            {
                                                @if (selectedMessage.ReceiverEmailId.Contains(","))
                                                {
                                                    @foreach (var recipient in selectedMessage.ReceiverEmailId.Split(',').Select(r => r.Trim()))
                                                    {
                                                        <span class="recipient-email">@recipient</span>
                                                        @if (recipient != selectedMessage.ReceiverEmailId.Split(',').Last().Trim())
                                                        {
                                                            <span class="recipient-separator">, </span>
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="recipient-email">@selectedMessage.ReceiverEmailId</span>
                                                }
                                            }
                                            else
                                            {
                                                <span class="recipient-email">me</span>
                                            }
                                        </span>
                                        @if (!string.IsNullOrEmpty(ccEmails))
                                        {
                                            <span class="cc-label">cc:</span>
                                            <span class="cc-list">@ccEmails</span>
                                        }
                                    </div>
                                </div>
                                <div class="email-timestamp">
                                    <span class="email-date" title="@selectedMessage.SentDateTime.ToString("dddd, MMMM dd, yyyy 'at' h:mm tt")">
                                        @GetRelativeTime(selectedMessage.SentDateTime)
                                    </span>
                                    <button class="btn-icon reply-btn" @onclick="() => ShowReply()" title="Reply">
                                        <i class="fas fa-reply"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Email Body -->
                        <div class="email-body">
                        @((MarkupString)selectedMessage.MessageContent)
                        </div>

                        <!-- Attachments Section -->
                        @if (selectedMessage != null && selectedMessage.HasAttachments)
                        {
                            <div class="email-attachments">
                                <h4><i class="fas fa-paperclip"></i> Attachments (@selectedMessage.AttachmentCount)</h4>
                                @if (selectedMessage.Attachments.Any())
                                {
                                    <div class="attachment-list">
                                        @foreach (var attachment in selectedMessage.Attachments)
                                        {
                                            <div class="attachment-item-display">
                                                <div class="attachment-info">
                                                    <i class="@attachment.GetFileIcon()"></i>
                                                    <div class="attachment-details">
                                                        <span class="attachment-name">@attachment.OriginalFileName</span>
                                                        <span class="attachment-size">@attachment.GetFileSizeFormatted()</span>
                                                    </div>
                                                </div>
                                                <button class="download-btn" @onclick="() => DownloadAttachmentAsync(attachment)">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </div>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <button class="load-attachments-btn" @onclick="() => LoadMessageAttachmentsAsync(selectedMessage)">
                                        <i class="fas fa-download"></i> Load Attachments
                                    </button>
                                }
                            </div>
                        }
                }
                else
                {
                        <div class="no-email-selected">
                            <i class="fas fa-envelope-open-text"></i>
                            <p>Select an email to read</p>
                        </div>
                }
            </div>
        </MudPaper>
    </MudPaper>
</MudContainer>

<!-- Loading Overlay -->
@if (isLoading)
{
    <div class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading messages...</span>
        </div>
    </div>
}

<!-- Delete Confirmation Dialog -->
@if (showDeleteConfirm && messageToDelete != null)
{
    <div class="modal-overlay">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Confirm Delete</h3>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to move this message to trash?</p>
                <div class="message-preview">
                    <strong>From:</strong> @messageToDelete.SenderName<br>
                    <strong>Subject:</strong> Message from @messageToDelete.SenderName<br>
                    <strong>Content:</strong> @messageToDelete.GetPreviewText()
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="CancelDelete">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-danger" @onclick="DeleteMessage">
                    <i class="fas fa-trash"></i> Move to Trash
                </button>
            </div>
        </div>
    </div>
}

<script>
    window.downloadFile = function (dataUrl, fileName) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
</script>
<style>

    /* Base Styles */
    :root {
        --primary-color: #1a73e8;
        --hover-bg: #f1f3f4;
        --active-bg: #e8f0fe;
        --border-color: #e0e0e0;
        --text-color: #202124;
        --secondary-text: #5f6368;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    .email-app {
        display: flex;
        height: 100vh;
        font-family: 'Google Sans', Roboto, sans-serif;
        background: #fff;
        color: var(--text-color);
    }

    /* Sidebar */
    .sidebar {
        width: 280px;
        background: #f6f8fc;
        border-right: 1px solid var(--border-color);
        display: flex;
        flex-direction: column;
    }

    .compose-btn {
        background: #c2e7ff;
        color: #001d35;
        padding: 12px 24px;
        border-radius: 24px;
        margin: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: 0.2s;
    }

        .compose-btn:hover {
            background: #b8dfff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

    .profile {
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .profile-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }

    .menu {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;
    }

        .menu li {
            display: flex;
            align-items: center;
            padding: 8px 24px;
            margin: 2px 0;
            border-radius: 0 24px 24px 0;
            cursor: pointer;
            transition: 0.2s;
        }

            .menu li.active {
                background: var(--active-bg);
                color: var(--primary-color);
                font-weight: 500;
            }

            .menu li:hover:not(.category) {
                background: var(--hover-bg);
            }

    .menu-icon {
        width: 24px;
        margin-right: 16px;
    }

    .count {
        margin-left: auto;
        background: var(--hover-bg);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
    }

    .category {
        color: var(--secondary-text);
        font-size: 0.75rem;
        text-transform: uppercase;
        padding: 16px 24px 8px;
        cursor: default;
    }

    /* Main Content */
    .main {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .toolbar {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        gap: 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .tabs {
        display: flex;
        gap: 4px;
    }

    .tab {
        padding: 8px 16px;
        border-radius: 16px;
        border: none;
        background: none;
        cursor: pointer;
    }

        .tab.active {
            background: var(--active-bg);
            color: var(--primary-color);
        }

    .search-box {
        flex: 1;
        max-width: 600px;
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: 8px 16px 8px 40px;
        border-radius: 24px;
        border: 1px solid var(--border-color);
        font-size: 0.9rem;
    }

    .search-box i {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--secondary-text);
    }

    /* Email List */
    .content {
        flex: 1;
        display: flex;
        overflow: hidden;
    }

    .email-list {
        width: 35%;
        border-right: 1px solid var(--border-color);
        overflow-y: auto;
    }

    .email-item {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        transition: 0.1s;
    }

        .email-item.selected {
            background: var(--active-bg);
        }

        .email-item:hover {
            background: var(--hover-bg);
        }

    .email-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }

    .time {
        font-size: 0.8rem;
        color: var(--secondary-text);
    }

    .subject-line {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;
    }

    .subject {
        font-weight: 500;
        flex: 1;
    }

    .email-indicators {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .unread-dot {
        width: 8px;
        height: 8px;
        background: var(--primary-color);
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    .delivery-status {
        font-size: 0.75rem;
        color: var(--secondary-text);
        font-weight: 500;
    }

    .delivery-status[title*="Delivered"], .delivery-status[title*="Read"] {
        color: #4caf50;
    }

    .delivery-status[title*="Failed"] {
        color: #f44336;
    }

    .preview-text {
        color: var(--secondary-text);
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .labels {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
    }

    .label {
        background: var(--active-bg);
        color: var(--primary-color);
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
    }

    /* Compose Modal */
    .compose-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: 1000;
    }

    .compose-modal {
        position: fixed;
        bottom: 0;
        right: 24px;
        width: 600px;
        background: white;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 0 16px rgba(0,0,0,0.2);
    }

    .compose-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .compose-body {
        padding: 16px;
    }

    .compose-field {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;
    }

    .compose-field label {
        min-width: 40px;
        font-weight: 500;
        color: var(--secondary-text);
        font-size: 0.9rem;
    }

    .compose-input {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 0.9rem;
        transition: border-color 0.2s;
    }

    .compose-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
    }

    .compose-textarea {
        width: 100%;
        height: 300px;
        padding: 8px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        resize: none;
    }

    /* Email Detail */
    .email-detail {
        flex: 1;
        padding: 0;
        overflow-y: auto;
        background: white;
    }

    /* Gmail-like Email Detail Header */
    .email-detail-header {
        padding: 24px 24px 16px 24px;
        border-bottom: 1px solid #f0f0f0;
    }

    .subject-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16px;
    }

    .email-subject {
        font-size: 1.375rem;
        font-weight: 400;
        color: var(--text-color);
        margin: 0;
        line-height: 1.3;
        flex: 1;
    }

    .header-actions {
        display: flex;
        gap: 4px;
        flex-shrink: 0;
    }

    /* Gmail-like Participants Section */
    .email-participants {
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;
    }

    .participant-row {
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }

    .participant-avatar {
        flex-shrink: 0;
    }

    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 0.875rem;
        text-transform: uppercase;
    }

    .participant-details {
        flex: 1;
        min-width: 0;
    }

    .participant-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
    }

    .participant-name {
        font-weight: 500;
        color: var(--text-color);
        font-size: 0.875rem;
    }

    .participant-email {
        color: var(--secondary-text);
        font-size: 0.875rem;
    }

    .email-recipients {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 0.875rem;
        color: var(--secondary-text);
        flex-wrap: wrap;
    }

    .recipient-label, .cc-label {
        color: var(--secondary-text);
        font-weight: 400;
    }

    .recipient-email, .cc-list {
        color: var(--text-color);
    }

    .recipient-separator {
        color: var(--secondary-text);
    }

    .email-timestamp {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
        flex-shrink: 0;
    }

    .email-date {
        font-size: 0.875rem;
        color: var(--secondary-text);
        white-space: nowrap;
    }

    .reply-btn {
        padding: 6px;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

    .reply-btn:hover {
        background: var(--hover-bg);
    }

    .email-body {
        padding: 24px;
        line-height: 1.6;
        white-space: pre-wrap;
        font-size: 0.875rem;
        color: var(--text-color);
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .email-list {
            width: 100%;
        }

        .email-detail {
            display: none;
        }
    }

    /* Compact Compose Window Styles */
    .compose-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 500px;
        max-width: 90vw;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        z-index: 1000;
        display: flex;
        flex-direction: column;
    }

    .compose-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        border-radius: 8px 8px 0 0;
    }

        .compose-header span {
            font-weight: 500;
            font-size: 0.9rem;
        }

    .compose-body {
        padding: 16px;
        flex-grow: 1;
    }

    .compose-to, .compose-subject {
        width: 100%;
        border: none;
        padding: 8px 0;
        margin-bottom: 8px;
        border-bottom: 1px solid #e0e0e0;
        font-size: 0.9rem;
    }

        .compose-to:focus, .compose-subject:focus {
            outline: none;
            border-bottom: 2px solid #1a73e8;
        }

    .compose-editor {
        margin-top: 12px;
        height: 200px;
    }

        .compose-editor textarea {
            width: 100%;
            height: 100%;
            border: none;
            resize: none;
            font-family: inherit;
            font-size: 0.9rem;
            padding: 8px;
        }

            .compose-editor textarea:focus {
                outline: none;
            }

    .compose-footer {
        padding: 12px 16px;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: flex-end;
        background: #f8f9fa;
        border-radius: 0 0 8px 8px;
    }

    .send-btn {
        background: #1a73e8;
        color: white;
        padding: 8px 24px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
    }

    .btn-icon {
        background: none;
        border: none;
        padding: 4px;
        cursor: pointer;
        color: #5f6368;
    }

    /* Responsive Design */
    @@media (max-width: 600px) {
        .compose-container {
            width: 100%;
            bottom: 0;
            right: 0;
            border-radius: 8px 8px 0 0;
        }
    }

    .reply-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 500px;
        max-height: 70vh;
        z-index: 1000;
    }

    .reply-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .left-controls {
        display: flex;
        gap: 8px;
    }

    /* Adjust existing compose-container style if needed */
    .compose-container {
        /* existing styles */
        max-height: 80vh;
        display: flex;
        flex-direction: column;
    }

    .compose-body {
        overflow-y: auto;
        flex-grow: 1;
    }

    /* Attachment Styles */
    .attachments-section {
        margin-top: 12px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
    }

    .attachments-section h4 {
        margin: 0 0 8px 0;
        font-size: 0.9rem;
        color: var(--secondary-text);
    }

    .attachment-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        background: white;
        border-radius: 4px;
        margin-bottom: 4px;
        border: 1px solid #e0e0e0;
    }

    .attachment-item i {
        color: var(--primary-color);
    }

    .attachment-item span {
        flex: 1;
        font-size: 0.85rem;
    }

    .btn-remove {
        background: none;
        border: none;
        color: #dc3545;
        cursor: pointer;
        padding: 2px 4px;
        border-radius: 2px;
    }

    .btn-remove:hover {
        background: #f8d7da;
    }

    .file-upload-section {
        margin-top: 12px;
        position: relative;
    }

    .file-input {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }

    .file-upload-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        color: var(--secondary-text);
        transition: 0.2s;
    }

    .file-upload-btn:hover {
        background: #e9ecef;
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    .compose-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .left-actions {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .attachment-count {
        font-size: 0.85rem;
        color: var(--secondary-text);
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .send-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Loading indicator */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }

    .loading-spinner {
        background: white;
        padding: 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 12px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    }

    /* Email Attachments Display */
    .email-attachments {
        margin-top: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }

    .email-attachments h4 {
        margin: 0 0 12px 0;
        font-size: 0.9rem;
        color: var(--secondary-text);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .attachment-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .attachment-item-display {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e0e0e0;
        transition: 0.2s;
    }

    .attachment-item-display:hover {
        border-color: var(--primary-color);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .attachment-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .attachment-info i {
        font-size: 1.2rem;
        color: var(--primary-color);
        width: 20px;
        text-align: center;
    }

    .attachment-details {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .attachment-name {
        font-weight: 500;
        font-size: 0.9rem;
        color: var(--text-color);
    }

    .attachment-size {
        font-size: 0.8rem;
        color: var(--secondary-text);
    }

    .download-btn {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.85rem;
        transition: 0.2s;
    }

    .download-btn:hover {
        background: #1557b0;
        transform: translateY(-1px);
    }

    .load-attachments-btn {
        background: #f1f3f4;
        color: var(--secondary-text);
        border: 1px solid #e0e0e0;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.85rem;
        transition: 0.2s;
    }

    .load-attachments-btn:hover {
        background: #e8f0fe;
        color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* Enhanced Email List Styles */
    .email-header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .sender-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .sender-name {
        font-weight: 600;
        color: var(--text-color);
        font-size: 0.9rem;
    }

    .email-time {
        font-size: 0.8rem;
        color: var(--secondary-text);
        margin-left: auto;
    }

    .email-actions {
        display: flex;
        gap: 4px;
        opacity: 0;
        transition: opacity 0.2s;
    }

    .email-item:hover .email-actions {
        opacity: 1;
    }

    .action-btn {
        background: none;
        border: none;
        padding: 6px;
        border-radius: 4px;
        cursor: pointer;
        color: var(--secondary-text);
        transition: all 0.2s;
        font-size: 0.85rem;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .action-btn:hover {
        background: var(--hover-bg);
        color: var(--text-color);
    }

    .action-btn.important.active {
        color: #ffa000;
    }

    .action-btn.important:hover {
        color: #ffa000;
    }

    .action-btn.archive:hover {
        color: #4caf50;
    }

    .action-btn.delete:hover {
        color: #f44336;
    }

    .action-btn.restore:hover {
        color: #2196f3;
    }

    .action-btn.delete-permanent:hover {
        color: #d32f2f;
        background: #ffebee;
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }

    .modal-dialog {
        background: white;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow: hidden;
    }

    .modal-header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--border-color);
        background: #f8f9fa;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.1rem;
        color: var(--text-color);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .modal-header i {
        color: #ff9800;
    }

    .modal-body {
        padding: 24px;
    }

    .modal-body p {
        margin: 0 0 16px 0;
        color: var(--text-color);
    }

    .message-preview {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 6px;
        border-left: 4px solid var(--primary-color);
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .modal-footer {
        padding: 16px 24px;
        border-top: 1px solid var(--border-color);
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        background: #f8f9fa;
    }

    .btn {
        padding: 10px 20px;
        border-radius: 6px;
        border: none !important;
        outline: none !important;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        transition: all 0.2s;
        min-width: 100px;
        text-align: center;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .btn:focus {
        outline: none !important;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    }

    .btn-secondary {
        background: #6c757d !important;
        color: white !important;
        border: none !important;
    }

    .btn-secondary:hover {
        background: #5a6268 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .btn-secondary:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0,0,0,0.2);
    }

    .btn-danger {
        background: #dc3545 !important;
        color: white !important;
        border: none !important;
    }

    .btn-danger:hover {
        background: #c82333 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .btn-danger:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0,0,0,0.2);
    }

    /* Professional Enhancements */
    .email-item {
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
    }

    .email-item:hover {
        background: var(--hover-bg);
        border-left-color: var(--primary-color);
        transform: translateX(2px);
    }

    .email-item.selected {
        background: var(--active-bg);
        border-left-color: var(--primary-color);
        box-shadow: inset 0 0 0 1px rgba(26, 115, 232, 0.2);
    }

    .unread-dot {
        width: 8px;
        height: 8px;
        background: var(--primary-color);
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @@keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* Menu Item Enhancements */
    .menu li {
        position: relative;
        overflow: hidden;
    }

    .menu li::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(26, 115, 232, 0.1), transparent);
        transition: width 0.3s ease;
    }

    .menu li:hover::before {
        width: 100%;
    }

    .count {
        background: var(--primary-color);
        color: white;
        font-weight: 600;
        min-width: 20px;
        text-align: center;
    }

    /* Improved Attachment Display */
    .attachment-icon {
        color: var(--primary-color);
        font-size: 0.9rem;
    }

    /* Enhanced Compose Button */
    .compose-btn {
        position: relative;
        overflow: hidden;
    }

    .compose-btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease;
    }

    .compose-btn:hover::before {
        width: 200px;
        height: 200px;
    }

</style>

<script>
    window.downloadFile = function (dataUrl, fileName) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
    </script>

