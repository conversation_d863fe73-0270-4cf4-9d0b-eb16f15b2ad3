﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;



namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public interface IObHistoryCommandHandler<T> where T : class
    {
        Task AddAsync(IEnumerable<T> obhistory);
        Task UpdateAsync(T complaint);
        Task UpdateObHistoryListAsync(IEnumerable<ObHistory> obhistory);
        Task DeleteByIdAsync(Guid ObId);
        Task DeleteByEntity(T obhistory);
    }
}
