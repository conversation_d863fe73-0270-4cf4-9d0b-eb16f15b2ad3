﻿using System;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class PlanTypeCommandHandler : IPlanTypeCommandHandler<PlanType>
    {
        private readonly IUnitOfWork _unitOfWork;

        public PlanTypeCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddPlanTypeAsync(PlanType plan)
        {
            if (plan == null)
                throw new ArgumentNullException(nameof(plan));

            await _unitOfWork.PlanTypeRepository.AddAsync(plan);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdatePlanTypeAsync(PlanType plan)
        {
            if (plan == null)
                throw new ArgumentNullException(nameof(plan));

            await _unitOfWork.PlanTypeRepository.UpdateAsync(plan);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeletePlanTypeAsync(Guid id)
        {
            await _unitOfWork.PlanTypeRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
