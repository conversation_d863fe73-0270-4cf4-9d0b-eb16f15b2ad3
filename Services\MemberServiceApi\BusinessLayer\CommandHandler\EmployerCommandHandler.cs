﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class EmployerCommandHandler : IEmployerCommandHandler<Employer>
    {
        private readonly IUnitOfWork _unitOfWork;

        public EmployerCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddEmployerAsync(List<Employer> Employers)
        {
            await _unitOfWork.EmployerRepository.AddAsync(Employers);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateEmployerAsync(Employer Employer)
        {
            await _unitOfWork.EmployerRepository.UpdateAsync(Employer);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteEmployerAsync(Guid id)
        {
            await _unitOfWork.EmployerRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
