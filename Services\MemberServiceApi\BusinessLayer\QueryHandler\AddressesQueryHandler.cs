﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class AddressesQueryHandler : IAddressesQueryHandler<Address>
    {
        private readonly IUnitOfWork _unitOfWork;

        public AddressesQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Address> GetAddressByIdAsync(Guid id)
        {
            var address = await _unitOfWork.AddressesRepository.GetByIdAsync(id);
            return address;
        }



        public async Task<List<Address>> GetAllAddressesAsync()
        {
            var addresses = await _unitOfWork.AddressesRepository.GetAllAsync();
            return addresses.ToList();
        }
    }
}
