# Base Image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

# Build Stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy project files first for caching
COPY ["Services/MemberServiceApi/MemberServiceApi.csproj", "Services/MemberServiceApi/"]
COPY ["Services/MemberServiceApi/BusinessLayer/MemberServiceBusinessLayer.csproj", "Services/MemberServiceApi/BusinessLayer/"]
COPY ["Services/MemberServiceApi/Contracts/Contracts.csproj", "Services/MemberServiceApi/Contracts/"]
COPY ["Services/MemberServiceApi/DataAccessLayer/MemberServiceDataAccessLayer.csproj", "Services/MemberServiceApi/DataAccessLayer/"]

# Restore dependencies
RUN dotnet restore "./Services/MemberServiceApi/MemberServiceApi.csproj"

# Copy full source code
COPY . .

# Debugging: Check if files exist before build
RUN ls -la /src/Services/MemberServiceApi/
RUN cat /src/Services/MemberServiceApi/MemberServiceApi.csproj

# Build the project
WORKDIR "/src/Services/MemberServiceApi"
RUN dotnet build "./MemberServiceApi.csproj" -c ${BUILD_CONFIGURATION:-Release} -o /app/build

# Publish Stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./MemberServiceApi.csproj" -c ${BUILD_CONFIGURATION:-Release} -o /app/publish /p:UseAppHost=false

# Final Image
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MemberServiceApi.dll"]
