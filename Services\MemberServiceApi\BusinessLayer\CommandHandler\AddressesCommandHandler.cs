﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class AddressesCommandHandler : IAddressesCommandHandler<Address>
    {
        private readonly IUnitOfWork _unitOfWork;

        public AddressesCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddAddressAsync(List<Address> addresses)
        {
            await _unitOfWork.AddressesRepository.AddAsync(addresses);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAddressAsync(Address address)
        {
            await _unitOfWork.AddressesRepository.UpdateAsync(address);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteAddressAsync(Guid id)
        {
            await _unitOfWork.AddressesRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
