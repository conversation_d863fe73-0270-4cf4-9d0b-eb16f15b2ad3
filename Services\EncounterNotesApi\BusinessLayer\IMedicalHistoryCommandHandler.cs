﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IMedicalHistoryCommandHandler<TText>
    {
        Task DeleteMedicalHistoryByEntity(MedicalHistory medicalHistory);
        Task DeleteMedicalHistoryById(Guid id);
        Task AddMedicalHistory(List<TText> texts);
        Task UpdateMedicalHistory(MedicalHistory medicalHistory);
        Task UpdateMedicalHistoryList(List<MedicalHistory> medicalHistories);
    }
}