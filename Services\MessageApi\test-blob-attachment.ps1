# Test script for Azure Blob Storage attachment functionality
Write-Host "🧪 Testing MessageAPI with Azure Blob Storage Attachments" -ForegroundColor Green

# API endpoint
$apiUrl = "http://localhost:5000/api/Message/send"

# Create test file content
$testContent = "Hello World! This is a test attachment for Azure Blob Storage.`n`nFeatures tested:`n- Upload to blob storage`n- Metadata storage in database`n- Download from blob storage`n`nTimestamp: $(Get-Date)"
$base64Content = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($testContent))
$fileSizeBytes = [System.Text.Encoding]::UTF8.GetBytes($testContent).Length

Write-Host "📄 Test file created:" -ForegroundColor Yellow
Write-Host "  - File name: test-blob-attachment.txt"
Write-Host "  - Content size: $fileSizeBytes bytes"
Write-Host "  - Base64 length: $($base64Content.Length) characters"

# Create the JSON request with attachment
$requestBody = @{
    senderName = "Azure Test User"
    senderEmailId = "<EMAIL>"
    receiverName = "Blob Storage Tester"
    receiverEmailId = "<EMAIL>"
    subject = "Azure Blob Storage Attachment Test"
    messageContent = "This message contains an attachment that should be stored in Azure Blob Storage instead of the database."
    attachments = @(
        @{
            fileName = "test-blob-attachment.txt"
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSizeBytes
        }
    )
} | ConvertTo-Json -Depth 3

Write-Host "`n📤 Sending message with attachment..." -ForegroundColor Yellow
Write-Host "  - API URL: $apiUrl"
Write-Host "  - Sender: Azure Test User (<EMAIL>)"
Write-Host "  - Receiver: Blob Storage Tester (<EMAIL>)"
Write-Host "  - Attachment: test-blob-attachment.txt ($fileSizeBytes bytes)"

try {
    # Send the request
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $requestBody -ContentType "application/json" -ErrorAction Stop
    
    Write-Host "`n✅ SUCCESS! Message sent successfully" -ForegroundColor Green
    Write-Host "📋 Response details:" -ForegroundColor Cyan
    Write-Host "  - Primary Message ID: $($response.PrimaryMessageId)"
    Write-Host "  - Status: $($response.Status)"
    Write-Host "  - Recipient Count: $($response.RecipientCount)"
    Write-Host "  - Attachment Count: $($response.AttachmentCount)"
    
    if ($response.MessageIds -and $response.MessageIds.Count -gt 0) {
        Write-Host "  - All Message IDs: $($response.MessageIds -join ', ')"
    }
    
    # Test getting the message attachments
    $messageId = $response.PrimaryMessageId
    if ($messageId) {
        Write-Host "`n🔍 Testing attachment retrieval..." -ForegroundColor Yellow
        
        try {
            $attachmentsUrl = "http://localhost:5000/api/Message/$messageId/attachments"
            $attachments = Invoke-RestMethod -Uri $attachmentsUrl -Method GET -ErrorAction Stop
            
            Write-Host "✅ Attachments retrieved successfully:" -ForegroundColor Green
            foreach ($attachment in $attachments) {
                Write-Host "📎 Attachment Details:" -ForegroundColor Cyan
                Write-Host "  - ID: $($attachment.AttachmentId)"
                Write-Host "  - Original Name: $($attachment.OriginalFileName)"
                Write-Host "  - Blob Name: $($attachment.BlobFileName)"
                Write-Host "  - Blob URL: $($attachment.BlobStorageUrl)"
                Write-Host "  - Container: $($attachment.BlobContainerName)"
                Write-Host "  - Size: $($attachment.FileSizeBytes) bytes"
                Write-Host "  - Content Type: $($attachment.ContentType)"
                Write-Host "  - Uploaded: $($attachment.UploadedDateTime)"
                
                # Test downloading the attachment
                Write-Host "`n📥 Testing attachment download..." -ForegroundColor Yellow
                try {
                    $downloadUrl = "http://localhost:5000/api/Message/attachment/$($attachment.AttachmentId)/download"
                    $downloadResponse = Invoke-WebRequest -Uri $downloadUrl -Method GET -ErrorAction Stop
                    
                    Write-Host "✅ Attachment downloaded successfully:" -ForegroundColor Green
                    Write-Host "  - Content Length: $($downloadResponse.Content.Length) characters"
                    Write-Host "  - Content Type: $($downloadResponse.Headers.'Content-Type')"
                    Write-Host "  - File Name: $($downloadResponse.Headers.'Content-Disposition')"
                    
                    # Verify content matches
                    if ($downloadResponse.Content -eq $testContent) {
                        Write-Host "✅ Content verification: PASSED" -ForegroundColor Green
                    } else {
                        Write-Host "❌ Content verification: FAILED" -ForegroundColor Red
                        Write-Host "Expected: $testContent"
                        Write-Host "Actual: $($downloadResponse.Content)"
                    }
                    
                } catch {
                    Write-Host "❌ Attachment download failed: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
            
        } catch {
            Write-Host "❌ Failed to retrieve attachments: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "`n❌ FAILED! Error sending message:" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Response: $errorContent" -ForegroundColor Red
    }
}

Write-Host "`n🏁 Test completed!" -ForegroundColor Green
