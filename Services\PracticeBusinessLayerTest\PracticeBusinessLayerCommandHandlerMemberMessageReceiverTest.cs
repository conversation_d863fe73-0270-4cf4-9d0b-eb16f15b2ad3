﻿
using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeBusinessLayer.CommandHandler;
using PracticeBusinessLayer.PracticeBusinessLayerResources;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;

namespace PracticeBusinessLayerTest
{
    public class PracticeBusinessLayerCommandHandlerMemberMessageReceiverTest
    {
        private readonly Mock<ServiceBusClient> _mockServiceBusClient;
        private readonly Mock<ILogger<MemberMessageReceiver>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<IServiceScopeFactory> _mockScopeFactory;
        private readonly Mock<IStringLocalizer<PracticeBusinessLayerStrings>> _mockLocalizer;
        private readonly Mock<ServiceBusProcessor> _mockProcessor;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly MemberMessageReceiver _memberMessageReceiver;

        public PracticeBusinessLayerCommandHandlerMemberMessageReceiverTest()
        {
            _mockServiceBusClient = new Mock<ServiceBusClient>();
            _mockLogger = new Mock<ILogger<MemberMessageReceiver>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockScopeFactory = new Mock<IServiceScopeFactory>();
            _mockLocalizer = new Mock<IStringLocalizer<PracticeBusinessLayerStrings>>();
            _mockProcessor = new Mock<ServiceBusProcessor>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();

            var mockServiceProvider = new Mock<IServiceProvider>();
            var mockScope = new Mock<IServiceScope>();

            mockServiceProvider
                .Setup(x => x.GetService(typeof(IUnitOfWork)))
                .Returns(_mockUnitOfWork.Object);
            mockScope.Setup(x => x.ServiceProvider).Returns(mockServiceProvider.Object);
            _mockScopeFactory.Setup(x => x.CreateScope()).Returns(mockScope.Object);

            _mockServiceBusClient
                .Setup(client => client.CreateProcessor(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(_mockProcessor.Object);

            _mockProcessor
                .SetupGet(p => p.IsProcessing)
                .Returns(false);

            _memberMessageReceiver = new MemberMessageReceiver(
                _mockServiceBusClient.Object,
                _mockLogger.Object,
                _mockConfiguration.Object,
                _mockScopeFactory.Object,
                _mockLocalizer.Object
            );
        }
        [Fact]
        public async Task StartReceivingMessages_ShouldStartProcessor_WhenNotProcessing()
        {
            // Arrange
            _mockProcessor
                .Setup(p => p.StartProcessingAsync(It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            await _memberMessageReceiver.StartReceivingMessages();

            // Assert
            _mockProcessor.Verify(p => p.StartProcessingAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task StopReceivingMessages_ShouldStopProcessor_WhenProcessing()
        {
            // Arrange
            _mockProcessor
                .SetupGet(p => p.IsProcessing)
                .Returns(true);

            _mockProcessor
                .Setup(p => p.StopProcessingAsync(It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Manually initialize the processor here for testing
            _memberMessageReceiver.GetType().GetField("_processor", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(_memberMessageReceiver, _mockProcessor.Object);

            // Act
            await _memberMessageReceiver.StopReceivingMessages();

            // Assert
            _mockProcessor.Verify(p => p.StopProcessingAsync(It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task StopReceivingMessages_ShouldNotStopProcessor_WhenNotProcessing()
        {
            // Arrange
            _mockProcessor
                .SetupGet(p => p.IsProcessing)
                .Returns(false);

            // Act
            await _memberMessageReceiver.StopReceivingMessages();

            // Assert
            _mockProcessor.Verify(p => p.StopProcessingAsync(It.IsAny<CancellationToken>()), Times.Never);
        }

       
        [Fact]
        public async Task ProcessMemberDataAsync_ShouldCallAddMethod_WhenActionIsAdd()
        {
            // Arrange
            var memberData = new ProviderPatient { Id = Guid.NewGuid(), PCPName = "Name", PCPId = Guid.NewGuid(), PatientName = "P01" };

            _mockUnitOfWork.Setup(uow => uow.ProviderPatientRepository.AddAsync(It.Is<List<ProviderPatient>>(p => p.Count == 1 && p[0].Id == memberData.Id)))
                           .Returns(Task.CompletedTask);


            _mockUnitOfWork.Setup(uow => uow.SaveAsync()).Returns(Task.FromResult(1));

            // Act
            await _memberMessageReceiver.ProcessMemberDataAsync(memberData, _mockUnitOfWork.Object, "Add");

            // Assert
            _mockUnitOfWork.Verify(uow => uow.ProviderPatientRepository.AddAsync(It.Is<List<ProviderPatient>>(p => p.Count == 1 && p[0].Id == memberData.Id)), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task ProcessMemberDataAsync_ShouldCallUpdateMethod_WhenActionIsUpdate()
        {
            // Arrange
            var memberData = new ProviderPatient { Id = Guid.NewGuid(), PCPName = "N001",PatientName="P001",PCPId=Guid.NewGuid() };


            _mockUnitOfWork.Setup(uow => uow.ProviderPatientRepository.UpdateAsync(It.Is<ProviderPatient>(p => p.Id == memberData.Id)))
                           .Returns(Task.CompletedTask);


            _mockUnitOfWork.Setup(uow => uow.SaveAsync()).Returns(Task.FromResult(1));

            // Act
            await _memberMessageReceiver.ProcessMemberDataAsync(memberData, _mockUnitOfWork.Object, "Update");

            // Assert
            _mockUnitOfWork.Verify(uow => uow.ProviderPatientRepository.UpdateAsync(It.Is<ProviderPatient>(p => p.Id == memberData.Id)), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task ProcessMemberDataAsync_ShouldCallDeleteByIdMethod_WhenActionIsDeleteById()
        {
            // Arrange
            var memberData = new ProviderPatient { Id = Guid.NewGuid(), PCPName = "N02",PCPId=Guid.NewGuid(),PatientName="P001" };


            _mockUnitOfWork.Setup(uow => uow.ProviderPatientRepository.DeleteByIdAsync(It.Is<Guid>(id => id == memberData.Id)))
                           .Returns(Task.CompletedTask);

            _mockUnitOfWork.Setup(uow => uow.SaveAsync()).Returns(Task.FromResult(1));

            // Act
            await _memberMessageReceiver.ProcessMemberDataAsync(memberData, _mockUnitOfWork.Object, "DeleteById");

            // Assert
            _mockUnitOfWork.Verify(uow => uow.ProviderPatientRepository.DeleteByIdAsync(memberData.Id), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task ProcessMemberDataAsync_ShouldCallDeleteByEntityMethod_WhenActionIsDeleteByEntity()
        {
            // Arrange
            var memberData = new ProviderPatient { Id = Guid.NewGuid(), PCPName = "N01" };

            _mockUnitOfWork.Setup(uow => uow.ProviderPatientRepository.DeleteByEntityAsync(It.Is<ProviderPatient>(p => p.Id == memberData.Id)))
                           .Returns(Task.CompletedTask);


            _mockUnitOfWork.Setup(uow => uow.SaveAsync()).Returns(Task.FromResult(1));

            // Act
            await _memberMessageReceiver.ProcessMemberDataAsync(memberData, _mockUnitOfWork.Object, "DeleteByEntity");

            // Assert
            _mockUnitOfWork.Verify(uow => uow.ProviderPatientRepository.DeleteByEntityAsync(It.Is<ProviderPatient>(p => p.Id == memberData.Id)), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveAsync(), Times.Once);
        }
        

    }
}
