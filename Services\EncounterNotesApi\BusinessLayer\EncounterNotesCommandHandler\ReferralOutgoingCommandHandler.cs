﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class ReferralOutgoingCommandHandler : IReferralOutgoingCommandHandler<PatientReferralOutgoing>
    {
        private readonly IUnitOfWork _unitOfWork;
        public ReferralOutgoingCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddReferralOutgoing(List<PatientReferralOutgoing> PatientReferralOutgoing)
        {
            await _unitOfWork.ReferralOutgoingRepository.AddAsync(PatientReferralOutgoing);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateReferralOutgoing(PatientReferralOutgoing ReferralOutgoing)
        {
            await _unitOfWork.ReferralOutgoingRepository.UpdateAsync(ReferralOutgoing);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateReferralOutgoingList(List<PatientReferralOutgoing> PatientReferralOutgoing)
        {
            await _unitOfWork.ReferralOutgoingRepository.UpdateRangeAsync(PatientReferralOutgoing);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteReferralOutgoingById(Guid id)
        {
            await _unitOfWork.ReferralOutgoingRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteReferralOutgoingByEntity(PatientReferralOutgoing ReferralOutgoing)
        {
            await _unitOfWork.ReferralOutgoingRepository.DeleteByEntityAsync(ReferralOutgoing);
            await _unitOfWork.SaveAsync();
        }
    }
}
