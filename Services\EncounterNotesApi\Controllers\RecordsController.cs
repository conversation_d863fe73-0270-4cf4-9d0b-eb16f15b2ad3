﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class RecordsController : ControllerBase
    {
        private readonly IRecordCommandHandler<Record> _recordDataHandler;
        private readonly IRecordQueryHandler<Record> _recordQueryHandler;
        private readonly ILogger<RecordsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer; 


        public RecordsController(
            IRecordCommandHandler<Record> dataHandler,
            IRecordQueryHandler<Record> queryHandler,
            ILogger<RecordsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _recordDataHandler = dataHandler;
            _recordQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer; 
        }
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Record>>> Get()
        {
            ActionResult result;
            try
            {
                var records = await _recordQueryHandler.GetRecord();
                result = Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("{id:guid}")]
        public async Task<ActionResult<Record>> GetById(Guid id)
        {
            ActionResult result;
            try
            {
                var record = await _recordQueryHandler.GetRecordById(id);
                result = record != null ? Ok(record) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("ByPCP/{pcpId:guid}")]
        public async Task<ActionResult<IEnumerable<Record>>> GetByPCPId(Guid pcpId)
        {
            try
            {
                var records = await _recordQueryHandler.GetRecordByPCPId(pcpId);
                return Ok(records ?? new List<Record>()); // Return an empty list instead of null
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
        }

        [HttpGet("PatientId/{patientId:guid}")]
        public async Task<ActionResult<IEnumerable<Record>>> GetByPatientId(Guid patientId)
        {
            ActionResult result;
            try
            {
                var records = await _recordQueryHandler.GetRecordByPatientId(patientId);
                result = Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById([FromRoute]Guid id,[FromBody] Record record)
        {
            IActionResult result;
            if (record == null || record.Id != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _recordDataHandler.UpdateRecord(record);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        [HttpDelete("{id:guid}")]
        public async Task<IActionResult> DeleteById(Guid id)
        {
            IActionResult result;
            try
            {
                await _recordDataHandler.DeleteRecordById(id);
                result = Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
            }
            return result;
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] Record record)
        {
            IActionResult result;
            if (record == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _recordDataHandler.DeleteRecordByEntity(record);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                }
            }
            return result;
        }

        [HttpPost]
        [Route("EncounterNote")]
        public async Task<IActionResult> EncounterNote([FromBody] List<RecordDTO> encounterNotesDto)
        {
            IActionResult result;
            if (encounterNotesDto == null || !encounterNotesDto.Any())
            {
                result = BadRequest(_localizer["NoLicense"]);
            }
            else
            {
                try
                {
                    var encounterNotes = encounterNotesDto.Select(dto => new Record
                    {
                        Id = dto.Id,
                        PatientId=dto.PatientId,
                        PatientName = dto.PatientName,
                        DateTime = dto.DateTime,
                        Notes = dto.Notes,
                        OrganizationId = dto.OrganizationId,
                        PCPId = dto.PCPId,
                        Transcription = dto.Transcription,
                        WordTimings = null,
                        isEditable=dto.isEditable
                    }).ToList();

                    await _recordDataHandler.AddRecord(encounterNotes);
                    var firstRecordId = encounterNotes.FirstOrDefault()?.Id;
                    result = Ok(new { Message = _localizer["SuccessfulEncounterNote"], FirstRecordId = firstRecordId });
                }
                //catch (SqlException ex)
                //{
                //    _logger.LogError(ex, _localizer["DatabaseError"]);
                //    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DatabaseError"]);
                //}
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["PostLogError"]);
                }
            }
            return result;
        }

    }
}

