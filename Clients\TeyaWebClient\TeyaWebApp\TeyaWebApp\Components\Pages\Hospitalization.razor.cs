﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using static TeyaWebApp.Components.Pages.Notes;
using static MudBlazor.Icons.Custom;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Net.Http;
using Syncfusion.Blazor.DropDowns;

namespace TeyaWebApp.Components.Pages
{
    public partial class Hospitalization : ComponentBase
    {
        private MudDialog _addMemberDialog;
        private List<string> ToolbarItems = new List<string> { "Add" };
        public SfGrid<HospitalizationRecord> hospitalizationRecordGrid { get; set; }
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        public HospitalizationRecord newVal = new();
        private List<HospitalizationRecord> records { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        private Guid PatientID { get; set; }
        private Guid? organizationId { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ILogger<Config> Logger { get; set; }
        private bool add = false;
        private List<HospitalizationRecord> DeleteList = new();
        private List<HospitalizationRecord> AddList = new();

        /// <summary>
        /// get list of Hospitalization Record from database
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientID = _PatientService.PatientData.Id;
                organizationId = _PatientService.PatientData.OrganizationID;
                records = await HospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(PatientID);
                ViewHandler();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        /// <summary>
        /// To store deleted rows locally in SFgrid 
        /// </summary>
        private void ActionCompletedHandler(ActionEventArgs<HospitalizationRecord> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedRecord = args.Data as HospitalizationRecord;
                    var existingItem = AddList.FirstOrDefault(v => v.RecordID == deletedRecord.RecordID);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedRecord.IsActive = false;
                        deletedRecord.UpdatedBy = Guid.Parse(User.id);
                        deletedRecord.UpdatedDate = DateTime.Now;
                        args.Data.UpdatedBy = PatientID;
                        DeleteList.Add(deletedRecord);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.PatientId = PatientID;
                args.Data.OrganizationID = organizationId;
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = PatientID;
                args.Data.RecordID = new Guid();
                args.Data.IsActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedVitals = args.Data;
                        if (addedVitals != null)
                        {
                            AddList.Add(addedVitals);
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// To Handle click Outside the SF Grid
        /// </summary>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Add HospitalizationRecord to database
        /// </summary>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await HospitalizationRecordService.CreateHospitalizationRecordAsync(AddList);
            }
            await HospitalizationRecordService.UpdateHospitalizationRecordList(DeleteList);
            await HospitalizationRecordService.UpdateHospitalizationRecordList(records);
            AddList.Clear();
            DeleteList.Clear();
            records = await HospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(PatientID);
            ViewHandler();
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Open dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _addMemberDialog.ShowAsync();
        }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add", TooltipText = "Add Record" },
        };

        /// <summary>
        /// Update to RichTextEditor
        /// </summary>
        private void ViewHandler()
        {
            string str = "";

            foreach (var member in records)
            {
                str += $"<li><b>{member.Date:dd-MM-yyyy}:</b> {member.Reason}</li>";
            }

            str += "</ul>";

            editorContent = str;
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _addMemberDialog.CloseAsync();
        }

        /// <summary>
        /// Undo Changes When click on cancel
        /// </summary>
        private async Task CancelChanges()
        {
            DeleteList.Clear();
            AddList.Clear();
            records = await HospitalizationRecordService.GetHospitalizationRecordByIdAsyncAndIsActive(PatientID);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        private async void CancelAction()
        {
            CloseAddTaskDialog();
            ViewHandler();
            await InvokeAsync(StateHasChanged);
            StateHasChanged();
        }

    }
}