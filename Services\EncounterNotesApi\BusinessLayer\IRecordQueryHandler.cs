﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IRecordQueryHandler<TText>
    {
        Task<IEnumerable<TText>> GetRecord();
        Task<TText> GetRecordById(Guid id);
        Task<IEnumerable<Record>> GetRecordByPCPId(Guid pcpId);
        Task<IEnumerable<Record>> GetRecordByPatientId(Guid patientId);
    }
}
