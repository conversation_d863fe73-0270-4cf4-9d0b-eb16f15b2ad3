﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class RolesCommandHandler : IRolesCommandHandler<Role>
    {
        private readonly IUnitOfWork _unitOfWork;

        public RolesCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddRoleAsync(List<Role> roles)
        {
            await _unitOfWork.RolesRepository.AddAsync(roles);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateRoleAsync(Role role)
        {
            await _unitOfWork.RolesRepository.UpdateAsync(role);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteRoleAsync(Guid id)
        {
            await _unitOfWork.RolesRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
