using Contracts;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer.CommandHandler;  
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class OrganizationsController : ControllerBase
    {
        private readonly IOrganizationsQueryHandler<Organization> _organizationsQueryHandler;
        private readonly IOrganizationsCommandHandler<Organization> _organizationsCommandHandler;
        private readonly ILogger<OrganizationsController> _logger;
        private readonly IStringLocalizer<OrganizationsController> _localizer;

        public OrganizationsController(
            IOrganizationsQueryHandler<Organization> organizationsQueryHandler,
            IOrganizationsCommandHandler<Organization> organizationsCommandHandler,
            ILogger<OrganizationsController> logger,
            IStringLocalizer<OrganizationsController> localizer)
        {
            _organizationsQueryHandler = organizationsQueryHandler ?? throw new ArgumentNullException(nameof(organizationsQueryHandler));
            _organizationsCommandHandler = organizationsCommandHandler ?? throw new ArgumentNullException(nameof(organizationsCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        /// <summary>
        /// Gets an organization by its ID.
        /// </summary>
        /// <param name="id">The ID of the organization.</param>
        /// <returns>The organization with the specified ID.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetOrganizationById(Guid id)
        {
            _logger.LogInformation(_localizer["Fetching Organization with ID: {0}"], id);
            var organization = await _organizationsQueryHandler.GetOrganizationByIdAsync(id);
            if (organization == null)
            {
                _logger.LogWarning(_localizer["Organization with ID: {0} not found"], id);
                return NotFound(_localizer["OrganizationNotFound"]);
            }
            _logger.LogInformation(_localizer["Organization with ID: {0} fetched successfully"], id);
            return Ok(organization);
        }

        /// <summary>
        /// Adds a new organization.
        /// </summary>
        /// <param name="organization">The organization to add.</param>
        /// <returns>The added organization.</returns>
        [HttpPost]
        public async Task<IActionResult> AddOrganization([FromBody] Organization organization)
        {
            if (organization == null)
            {
                _logger.LogWarning(_localizer["Invalid Organization data received"]);
                return BadRequest(_localizer["OrganizationInvalid"]);
            }

            try
            {
                _logger.LogInformation(_localizer["Adding new Organization"]);
                await _organizationsCommandHandler.AddOrganizationAsync(new List<Organization> { organization });
                _logger.LogInformation(_localizer["Organization added successfully with ID: {0}"], organization.OrganizationId);
                return CreatedAtAction(nameof(GetOrganizationById), new { id = organization.OrganizationId }, organization);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingOrganization"]);
                return StatusCode(500, _localizer["InternalServerError"]);
            }
        }

        /// <summary>
        /// Updates an existing organization.
        /// </summary>
        /// <param name="id">The ID of the organization to update.</param>
        /// <param name="organization">The updated organization data.</param>
        /// <returns>No content if the update is successful.</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOrganization(Guid id, [FromBody] Organization organization)
        {
            if (organization == null || id != organization.OrganizationId)
            {
                _logger.LogWarning(_localizer["Invalid data for updating Organization with ID: {0}"], id);
                return BadRequest(_localizer["OrganizationInvalid"]);
            }

            try
            {
                _logger.LogInformation(_localizer["Updating Organization with ID: {0}"], id);
                await _organizationsCommandHandler.UpdateOrganizationAsync(organization);
                _logger.LogInformation(_localizer["Organization with ID: {0} updated successfully"], id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["Organization with ID: {0} not found for update"], id);
                return NotFound(_localizer["OrganizationNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingOrganization"]);
                return StatusCode(500, _localizer["InternalServerError"]);
            }
        }

        /// <summary>
        /// Deletes an organization by its ID.
        /// </summary>
        /// <param name="id">The ID of the organization to delete.</param>
        /// <returns>No content if the deletion is successful.</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteOrganization(Guid id)
        {
            try
            {
                _logger.LogInformation(_localizer["Deleting Organization with ID: {0}"], id);
                await _organizationsCommandHandler.DeleteOrganizationAsync(id);
                _logger.LogInformation(_localizer["Organization with ID: {0} deleted successfully"], id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["Organization with ID: {0} not found for deletion"], id);
                return NotFound(_localizer["OrganizationNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingOrganization"]);
                return StatusCode(500, _localizer["InternalServerError"]);
            }
        }

        /// <summary>
        /// Searches for organizations by name.
        /// </summary>
        /// <param name="name">The name of the organization to search for.</param>
        /// <returns>A list of organizations that match the search criteria.</returns>
        [HttpGet("search")]
        public async Task<IActionResult> GetOrganizationByName([FromQuery] string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                _logger.LogWarning(_localizer["Organization Name is required for search"]);
                return BadRequest(_localizer["OrganizationNameRequired"]);
            }

            try
            {
                _logger.LogInformation(_localizer["Searching for organization with name: {0}"], name);
                var organizations = await _organizationsQueryHandler.GetOrganizationsByNameAsync(name);

                if (organizations == null || organizations.Count == 0)
                {
                    _logger.LogWarning(_localizer["No organization found with name: {0}"], name);
                    return NotFound(_localizer["OrganizationNotFound"]);
                }

                _logger.LogInformation(_localizer["organization with name: {0} fetched successfully"], name);
                return Ok(organizations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorSearchingRoles"]);
                return StatusCode(500, _localizer["InternalServerError"]);
            }
        }

        /// <summary>
        /// Gets all organizations.
        /// </summary>
        /// <returns>A list of all organizations.</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllOrganizations()
        {
            try
            {
                _logger.LogInformation(_localizer["Fetching all organizations"]);
                var organizations = await _organizationsQueryHandler.GetAllOrganizationsAsync();

                if (organizations == null || organizations.Count == 0)
                {
                    _logger.LogWarning(_localizer["No organizations found"]);
                    return NotFound(_localizer["OrganizationNotFound"]);
                }

                _logger.LogInformation(_localizer["Organizations fetched successfully"]);
                return Ok(organizations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingOrganizations"]);
                return StatusCode(500, _localizer["InternalServerError"]);
            }
        }
    }
}
