﻿using EncounterNotesBusinessLayer.EncounterNotesBusinessResource;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using DotNetEnv;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using FFMpegCore;
using System.Net.Http;
using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using Moq.Protected;
using NUnit.Framework.Legacy;

namespace EncounterNotesBusinessLayer.Tests
{
    [TestFixture]
    public class SpeechDataHandlerTests
    {
        private Mock<HttpMessageHandler> _httpMessageHandlerMock;
        private HttpClient _httpClient;
        private Mock<ILogger<SpeechDataHandler>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesBusiness>> _localizerMock;
        private RecordDTO _recordDTO;
        private Mock<IBlobStorageService> _blobStorageMock;
        private Mock<IKernelService> _kernelServiceMock;
        private SpeechDataHandler _handler;

        [SetUp]
        public void SetUp()
        {
            _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
            _loggerMock = new Mock<ILogger<SpeechDataHandler>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesBusiness>>();
            _recordDTO = new RecordDTO();
            _blobStorageMock = new Mock<IBlobStorageService>();
            _kernelServiceMock = new Mock<IKernelService>();

            // Pass the mocked services into the handler constructor
            //_handler = new SpeechDataHandler(_httpClient, _loggerMock.Object, _localizerMock.Object, _recordDTO, _blobStorageMock.Object, _kernelServiceMock.Object);

            Environment.SetEnvironmentVariable("OPENAI_MODEL_NAME", "test_model");
            Environment.SetEnvironmentVariable("OPENAI_ENDPOINT", "https://test.endpoint");
            Environment.SetEnvironmentVariable("OPENAI_API_KEY", "test_api_key");
            Environment.SetEnvironmentVariable("RECORDS_API_ENDPOINT", "https://test.records.endpoint");
            Environment.SetEnvironmentVariable("AZURE_BLOB_CONNECTION_STRING", "DefaultEndpointsProtocol=https;AccountName=testaccount;AccountKey=testkey;EndpointSuffix=core.windows.net");
            Environment.SetEnvironmentVariable("AZURE_BLOB_CONTAINER_NAME", "test_container_name");

            // Mock localizer values
            _localizerMock.Setup(l => l["pcm_s16le"]).Returns(new LocalizedString("pcm_s16le", "pcm_s16le"));
            _localizerMock.Setup(l => l["16000"]).Returns(new LocalizedString("16000", "16000"));
            _localizerMock.Setup(l => l["wav"]).Returns(new LocalizedString("wav", "wav"));
            _localizerMock.Setup(l => l["audio/wav"]).Returns(new LocalizedString("audio/wav", "audio/wav"));

            Env.Load();
            GlobalFFOptions.Configure(options => options.BinaryFolder = @"C:\ffmpeg");
        }

        [Test]
        public async Task ProcessSpeech_ShouldUpdateRecordDTO_WhenSpeechIsProcessed()
        {
            // Arrange
            var speechRequest = new SpeechRequest
            {
                Id = Guid.NewGuid(),
                Speeches = new List<Speech>
                {
                    new Speech
                    {
                        Result = "Patient John Doe has a history of diabetes.",
                        Timestamps = new List<WordTime>
                        {
                            new WordTime { Word = "Patient", StartTime = 0, EndTime = 1 },
                            new WordTime { Word = "John", StartTime = 1, EndTime = 2 },
                            new WordTime { Word = "Doe", StartTime = 2, EndTime = 3 },
                            new WordTime { Word = "has", StartTime = 3, EndTime = 4 },
                            new WordTime { Word = "a", StartTime = 4, EndTime = 5 },
                            new WordTime { Word = "history", StartTime = 5, EndTime = 6 },
                            new WordTime { Word = "of", StartTime = 6, EndTime = 7 },
                            new WordTime { Word = "diabetes", StartTime = 7, EndTime = 8 }
                        }
                    }
                }
            };

            _kernelServiceMock.Setup(k => k.ExtractFromPrompt(It.IsAny<string>(), It.IsAny<string>()))
                              .ReturnsAsync("Extracted Information");

            // Act
            //await _handler.ProcessSpeech(speechRequest);

            // Assert
            ClassicAssert.AreEqual("Patient John Doe has a history of diabetes.", _recordDTO.Transcription);
            ClassicAssert.AreEqual(8, _recordDTO.WordTimings.Count);
            ClassicAssert.AreEqual("Extracted Information", _recordDTO.PatientName);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.Summary);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.CurrentMedication);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.MedicalHistory);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.SurgicalHistory);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.HospitalizationHistory);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.FamilyHistory);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.SocialHistory);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.Vitals);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.Assessment);
            //ClassicAssert.AreEqual("Extracted Information", _recordDTO.TreatmentPlan);
        }

        [Test]
        public async Task ExtractFromPrompt_ShouldReturnExtractedInformation_WhenPromptIsInvoked()
        {
            // Arrange
            var text = "Patient John Doe has a history of diabetes.";
            var prompt = "Extract patient name: ";
            _kernelServiceMock.Setup(k => k.ExtractFromPrompt(It.IsAny<string>(), It.IsAny<string>()))
                              .ReturnsAsync("John Doe");

            // Act
            var result = await _handler.ExtractFromPrompt(text, prompt);

            // Assert
            ClassicAssert.AreEqual("John Doe", result);
        }

        [Test]
        public async Task Handle_ShouldReturnFirstRecordId_WhenSpeechesAreProcessed()
        {
            // Arrange
            var speechRequest = new SpeechRequest
            {
                Id = Guid.NewGuid(),
                Speeches = new List<Speech>
                {
                    new Speech { Result = "Patient John Doe has a history of diabetes." }
                },
                accessToken = "test_access_token"
            };

            _kernelServiceMock.Setup(k => k.ExtractFromPrompt(It.IsAny<string>(), It.IsAny<string>()))
                              .ReturnsAsync("Extracted Information");

            var recordId = Guid.NewGuid();
            _httpMessageHandlerMock.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent($"{{ \"firstRecordId\": \"{recordId}\" }}")
                });

            // Act
            var result = await _handler.Handle(speechRequest);

            // Assert
            ClassicAssert.IsNotNull(result);
            ClassicAssert.AreEqual(recordId, result);
        }

        [Test]
        public async Task UploadRecord_ShouldReturnRecordId_WhenUploadIsSuccessful()
        {
            // Arrange
            var accessToken = "test_access_token";
            var recordId = Guid.NewGuid();
            var responseMessage = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent($"{{ \"firstRecordId\": \"{recordId}\" }}")
            };

            _httpMessageHandlerMock.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage);

            // Act
            var result = await _handler.UploadRecord(accessToken);

            // Assert
            ClassicAssert.AreEqual(recordId, result);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient.Dispose();
        }
    }
}