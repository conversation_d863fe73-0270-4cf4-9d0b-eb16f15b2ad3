﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
 
namespace EncounterNotesContracts
{
    public class HistoryOfPresentIllness : IContract
    {
        public Guid Id { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public string? Location { get; set; }
        public string? Duration { get; set; }
        public string? Severity { get; set; }
        public string? Symptoms { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
    }
}

