﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IUserLicenseService
    {
        Task<IEnumerable<UserLicense>> GetAllUserLicensesAsync();
        Task<UserLicense> GetUserLicenseByIdAsync(Guid id);
        Task<UserLicense> GetUserLicenseByOrganizationIdAsync(Guid id);
        Task AddUserLicenseAsync(UserLicense license);
        Task UpdateUserLicenseAsync(UserLicense license);
        Task DeleteUserLicenseByIdAsync(Guid id);
        Task IncrementActiveUsersAsync(Guid organizationId);
        Task ResetActiveUsersAsync(Guid organizationId);
        Task SetLicenseStatusAsync(Guid organizationId, bool status);
        Task<bool> CheckLicenseExpiryAsync(Guid organizationId);
    }
}
