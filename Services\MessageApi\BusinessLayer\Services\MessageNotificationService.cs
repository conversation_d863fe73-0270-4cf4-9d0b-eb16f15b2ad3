using MessageContracts;
using Microsoft.AspNetCore.SignalR;
using MessageApi.Hubs;

namespace MessageBusinessLayer.Services
{
    public class MessageNotificationService : IMessageNotificationService
    {
        private readonly IHubContext<MessageHub> _hubContext;
        private readonly ILogger<MessageNotificationService> _logger;

        public MessageNotificationService(IHubContext<MessageHub> hubContext, ILogger<MessageNotificationService> logger)
        {
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task NotifyNewMessageAsync(string receiverEmail, Message message)
        {
            try
            {
                _logger.LogInformation($"Sending real-time notification for new message to {receiverEmail}");
                
                await _hubContext.Clients.Group($"user_{receiverEmail}")
                    .SendAsync("NewMessage", new
                    {
                        MessageId = message.MessageId,
                        SenderName = message.SenderName,
                        SenderEmail = message.SenderEmailId,
                        Subject = message.Subject,
                        MessageContent = message.MessageContent,
                        SentDateTime = message.SentDateTime,
                        HasAttachments = message.HasAttachments,
                        AttachmentCount = message.AttachmentCount
                    });
                    
                _logger.LogInformation($"Real-time notification sent successfully to {receiverEmail}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send real-time notification to {receiverEmail}");
            }
        }

        public async Task NotifyMessageReadAsync(string senderEmail, Guid messageId)
        {
            try
            {
                _logger.LogInformation($"Sending read notification to {senderEmail} for message {messageId}");
                
                await _hubContext.Clients.Group($"user_{senderEmail}")
                    .SendAsync("MessageRead", new
                    {
                        MessageId = messageId,
                        ReadDateTime = DateTime.UtcNow
                    });
                    
                _logger.LogInformation($"Read notification sent successfully to {senderEmail}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send read notification to {senderEmail}");
            }
        }

        public async Task NotifyMessageArchivedAsync(string userEmail, Guid messageId)
        {
            try
            {
                await _hubContext.Clients.Group($"user_{userEmail}")
                    .SendAsync("MessageArchived", new
                    {
                        MessageId = messageId,
                        ArchivedDateTime = DateTime.UtcNow
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send archive notification to {userEmail}");
            }
        }

        public async Task NotifyMessageDeletedAsync(string userEmail, Guid messageId)
        {
            try
            {
                await _hubContext.Clients.Group($"user_{userEmail}")
                    .SendAsync("MessageDeleted", new
                    {
                        MessageId = messageId,
                        DeletedDateTime = DateTime.UtcNow
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send delete notification to {userEmail}");
            }
        }

        public async Task NotifyMessageImportantAsync(string userEmail, Guid messageId, bool isImportant)
        {
            try
            {
                await _hubContext.Clients.Group($"user_{userEmail}")
                    .SendAsync("MessageImportantChanged", new
                    {
                        MessageId = messageId,
                        IsImportant = isImportant,
                        UpdatedDateTime = DateTime.UtcNow
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send important notification to {userEmail}");
            }
        }
    }
}
