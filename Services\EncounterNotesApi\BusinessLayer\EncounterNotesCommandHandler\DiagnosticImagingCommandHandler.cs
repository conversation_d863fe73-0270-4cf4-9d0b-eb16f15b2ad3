﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class DiagnosticImagingCommandHandler : IDiagnosticImagingCommandHandler<DiagnosticImage>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DiagnosticImagingCommandHandler> _logger;

        public DiagnosticImagingCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<DiagnosticImagingCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Add new Diagnostic Imaging
        /// </summary>
        public async Task AddDiagnosticImaging(List<DiagnosticImage> diagnosticImagingList)
        {
            await _unitOfWork.DiagnosticImagingRepository.AddAsync(diagnosticImagingList);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Update an existing Diagnostic Imaging
        /// </summary>
        public async Task UpdateDiagnosticImaging(DiagnosticImage diagnosticImaging)
        {
            await _unitOfWork.DiagnosticImagingRepository.UpdateAsync(diagnosticImaging);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Delete an existing Diagnostic Imaging By Id
        /// </summary>
        public async Task DeleteDiagnosticImagingById(Guid id)
        {
            await _unitOfWork.DiagnosticImagingRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        ///  Update an existing List of Diagnostic Imaging
        /// </summary>
        public async Task<bool> UpdateDiagnosticImagingList(List<DiagnosticImage> diagnosticImagingList)
        {
            var existingRecords = await _unitOfWork.DiagnosticImagingRepository.GetAsync();

            foreach (var update in diagnosticImagingList)
            {
                var recordToUpdate = existingRecords.FirstOrDefault(r => r.RecordID == update.RecordID);

                if (recordToUpdate != null)
                {
                    recordToUpdate.DiCompany = update.DiCompany;
                    recordToUpdate.Type = update.Type;
                    recordToUpdate.Lookup = update.Lookup;
                    recordToUpdate.OrderName = update.OrderName;
                    recordToUpdate.StartsWith = update.StartsWith;
                    recordToUpdate.IsActive = update.IsActive;

                    await _unitOfWork.DiagnosticImagingRepository.UpdateAsync(recordToUpdate);
                }
            }

            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
