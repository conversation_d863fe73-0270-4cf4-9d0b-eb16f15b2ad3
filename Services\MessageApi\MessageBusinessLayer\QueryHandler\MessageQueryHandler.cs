using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageBusinessLayer.QueryHandler
{
    public class MessageQueryHandler : IMessageQueryHandler<Message>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MessageQueryHandler> _logger;

        public MessageQueryHandler(
            IConfiguration configuration,
            IUnitOfWork unitOfWork,
            ILogger<MessageQueryHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Message>> GetMessagesByEmail(string email)
        {
            try
            {
                var messages = await _unitOfWork.MessageRepository.GetMessagesWithAttachmentsByEmailAsync(email);

                // Update attachment flags for each message
                foreach (var message in messages)
                {
                    message.HasAttachments = message.Attachments?.Any() ?? false;
                    message.AttachmentCount = message.Attachments?.Count ?? 0;
                }

                return messages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting messages for email: {email}");
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetConversation(string senderEmail, string receiverEmail)
        {
            try
            {
                var messages = await _unitOfWork.MessageRepository.GetAsync();
                return messages.Where(m =>
                    (m.SenderEmailId.Equals(senderEmail, StringComparison.OrdinalIgnoreCase) &&
                     m.ReceiverEmailId.Equals(receiverEmail, StringComparison.OrdinalIgnoreCase)) ||
                    (m.SenderEmailId.Equals(receiverEmail, StringComparison.OrdinalIgnoreCase) &&
                     m.ReceiverEmailId.Equals(senderEmail, StringComparison.OrdinalIgnoreCase)))
                    .OrderBy(m => m.SentDateTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting conversation between {senderEmail} and {receiverEmail}");
                throw;
            }
        }

        public async Task<Message> GetMessageById(Guid messageId)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository.GetMessageWithAttachmentsAsync(messageId);
                if (message != null)
                {
                    // Update attachment flags based on loaded attachments
                    message.HasAttachments = message.Attachments?.Any() ?? false;
                    message.AttachmentCount = message.Attachments?.Count ?? 0;
                }
                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting message by Id: {messageId}");
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetUnreadMessages(string email)
        {
            try
            {
                var messages = await _unitOfWork.MessageRepository.GetAsync();
                return messages.Where(m => m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase) &&
                                          m.Status != 3) // 3 = Read
                              .OrderByDescending(m => m.SentDateTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting unread messages for email: {email}");
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetSentMessages(string email)
        {
            try
            {
                var messages = await _unitOfWork.MessageRepository.GetAsync();
                return messages.Where(m => m.SenderEmailId.Equals(email, StringComparison.OrdinalIgnoreCase))
                              .OrderByDescending(m => m.SentDateTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting sent messages for email: {email}");
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetReceivedMessages(string email)
        {
            try
            {
                var messages = await _unitOfWork.MessageRepository.GetAsync();
                return messages.Where(m => m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase))
                              .OrderByDescending(m => m.SentDateTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting received messages for email: {email}");
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetDeletedMessagesByEmail(string email)
        {
            try
            {
                // Use IgnoreQueryFilters to get soft-deleted messages
                var messages = await _unitOfWork.MessageRepository.GetAsync(includeDeleted: true);
                return messages.Where(m => (m.SenderEmailId.Equals(email, StringComparison.OrdinalIgnoreCase) ||
                                          m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase)) &&
                                          m.IsDeleted)
                              .OrderByDescending(m => m.DeletedDateTime ?? m.SentDateTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting deleted messages for email: {email}");
                throw;
            }
        }
    }
}
