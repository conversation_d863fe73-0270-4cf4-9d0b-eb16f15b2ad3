﻿using PracticeDataAccessLayer.Context;
using System;
using System.Threading.Tasks;

namespace PracticeDataAccessLayer.Implementation
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly PracticeDatabaseContext _context;
        public IPracticeRepository PracticeRepository { get; }
        public IProviderPatientRepository ProviderPatientRepository { get; }

        public UnitOfWork(PracticeDatabaseContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            PracticeRepository = new PracticeRepository(_context);
            ProviderPatientRepository = new ProviderPatientRepository(_context);
        }

        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
