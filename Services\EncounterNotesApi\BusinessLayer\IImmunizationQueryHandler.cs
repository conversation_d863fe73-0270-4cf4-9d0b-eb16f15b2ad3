﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IImmunizationQueryHandler<TText>
    {
        Task<IEnumerable<Immunization>> GetImmunizationByIdAndIsActive(Guid id);
        Task<IEnumerable<Immunization>> GetAllImmunizationsById(Guid id);
    }
}