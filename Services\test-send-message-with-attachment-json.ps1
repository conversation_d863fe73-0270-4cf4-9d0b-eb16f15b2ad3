# Test sending message with attachment using JSON format (Base64 encoded)
$uri = "http://localhost:5000/api/Message/send"

Write-Host "Sending message with attachment using JSON format..."
Write-Host "Attachment file: test-attachment.txt"

# Read file and convert to Base64
$fileBytes = [System.IO.File]::ReadAllBytes("test-attachment.txt")
$base64Content = [System.Convert]::ToBase64String($fileBytes)
$fileSizeBytes = $fileBytes.Length

# Create the JSON request with attachment
$requestBody = @{
    senderName = "<PERSON> Johnson"
    senderEmailId = "<EMAIL>"
    receiverName = "<PERSON>"
    receiverEmailId = "<EMAIL>"
    messageContent = "Test message with attachment for user management debugging - JSON format"
    attachments = @(
        @{
            fileName = "test-attachment.txt"
            contentType = "text/plain"
            fileContentBase64 = $base64Content
            fileSizeBytes = $fileSizeBytes
        }
    )
} | ConvertTo-Json -Depth 3

Write-Host "Request body preview:"
Write-Host "- Sender: <PERSON> (<EMAIL>)"
Write-Host "- Receiver: <PERSON> (<EMAIL>)"
Write-Host "- Attachment: test-attachment.txt ($fileSizeBytes bytes)"
Write-Host "- Base64 content length: $($base64Content.Length) characters"

try {
    $response = Invoke-RestMethod -Uri $uri -Method POST -ContentType "application/json" -Body $requestBody
    Write-Host ""
    Write-Host "✅ SUCCESS! Response:"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host ""
    Write-Host "❌ ERROR occurred:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
