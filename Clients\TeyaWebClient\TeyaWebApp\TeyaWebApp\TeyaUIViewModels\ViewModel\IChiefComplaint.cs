﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaWebApp;

namespace TeyaUIViewModels.ViewModel
{
    public interface IChiefComplaintService
    {
        Task<List<ChiefComplaintDTO>> GetAllComplaintsAsync();
        Task AddAsync(ChiefComplaintDTO complaintDto);
        Task UpdateComplaintAsync(Guid id, ChiefComplaintDTO complaintDto);
        Task DeleteComplaintAsync(Guid id);

        Task UpdateComplaintListAsync(List<ChiefComplaintDTO> complaints);
        Task<IEnumerable<ChiefComplaintDTO>> GetByPatientIdAsync(Guid patientId);
        Task AddAsync(List<ChiefComplaintDTO> complaintList);
        Task<List<ChiefComplaintDTO>> LoadComplaintsAsync(Guid patientId);
        Task<List<ChiefComplaintDTO>> GetProcessedComplaintsAsync();


    }
}
