﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using System.Data.SqlClient;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ProcedureController : ControllerBase
    {
        private readonly IProcedureCommandHandler<Procedure> _procedureCommandHandler;
        private readonly IProcedureQueryHandler<Procedure> _procedureQueryHandler;
        private readonly ILogger<ProcedureController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public ProcedureController(
            IProcedureCommandHandler<Procedure> procedureCommandHandler,
            IProcedureQueryHandler<Procedure> procedureQueryHandler,
            ILogger<ProcedureController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _procedureCommandHandler = procedureCommandHandler;
            _procedureQueryHandler = procedureQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get all assessments by Patient ID
        /// </summary>
        [HttpGet("{patientId:guid}")]
        public async Task<ActionResult<IEnumerable<Procedure>>> GetByPatientId(Guid patientId)
        {
            try
            {
                var assessments = await _procedureQueryHandler.GetProcedureByPatientId(patientId);
                return assessments != null ? Ok(assessments) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetProcedureError"]);
                return StatusCode(500, _localizer["GetProcedureError"]);
            }
        }

        /// <summary>
        /// Add new assessments
        /// </summary>
        [HttpPost("AddProcedure")]
        public async Task<IActionResult> AddProcedure([FromBody] List<Procedure> procedure)
        {
            if (procedure == null || procedure.Count == 0)
            {
                return BadRequest(_localizer["NoProcedureProvided"]);
            }

            try
            {
                await _procedureCommandHandler.AddProcedure(procedure);
                return Ok(_localizer["ProcedureAdded"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["AddProcedureError"]);
                return StatusCode(500, _localizer["AddProcedureError"]);
            }
        }

        /// <summary>
        /// Update single assessment
        /// </summary>
        [HttpPut("{Id:guid}")]
        public async Task<IActionResult> UpdateProcedure(Guid Id, [FromBody] Procedure procedure)
        {
            if (procedure == null || procedure.Id != Id)
            {
                return BadRequest(_localizer["InvalidProcedure"]);
            }

            try
            {
                await _procedureCommandHandler.UpdateProcedure(procedure);
                return Ok(_localizer["ProcedureUpdated"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateProcedureError"]);
                return StatusCode(500, _localizer["UpdateProcedureError"]);
            }
        }

        /// <summary>
        /// Update multiple assessments
        /// </summary>
        [HttpPut("UpdateProcedureList")]
        public async Task<IActionResult> UpdateAssessmentsList([FromBody] List<Procedure> procedure)
        {
            try
            {
                await _procedureCommandHandler.UpdateProcedureListAsync(procedure);
                return Ok(_localizer["ProcedureUpdated"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateProcedureListError"]);
                return StatusCode(500, _localizer["UpdateProcedureListError"]);
            }
        }


    }
}