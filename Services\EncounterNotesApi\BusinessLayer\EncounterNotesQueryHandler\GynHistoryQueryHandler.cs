﻿using System;
using System.Collections.Generic;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;



namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class GynHistoryQueryHandler : IGynHistoryQueryHandler<GynHistory>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        public GynHistoryQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public Task<IEnumerable<GynHistory>> GetAllAsync()
        {
            var getAll = _unitOfWork.GynHistoryRepository.GetAllAsync();
            return getAll;
        }

        public Task<GynHistory> GetByIdAsync(Guid id)
        {
            var getById = _unitOfWork.GynHistoryRepository.GetByIdAsync(id);
            return getById;
        }

        public Task<IEnumerable<GynHistory>> GetByPatientIdAsync(Guid patientId)
        {
            var getByPatientId = _unitOfWork.GynHistoryRepository.GetByPatientIdAsync(patientId);
            return getByPatientId;
        }


    }
}
