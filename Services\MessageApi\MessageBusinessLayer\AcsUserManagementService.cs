using Azure.Communication.Identity;
using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public class AcsUserManagementService : IAcsUserManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AcsUserManagementService> _logger;
        private readonly CommunicationIdentityClient? _identityClient;
        private readonly string? _connectionString;

        public AcsUserManagementService(
            IUnitOfWork unitOfWork,
            IConfiguration configuration,
            ILogger<AcsUserManagementService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;

            _connectionString = Environment.GetEnvironmentVariable("AzureCommunicationServices__ConnectionString")
                ?? configuration.GetConnectionString("AzureCommunicationServices");

            if (!string.IsNullOrEmpty(_connectionString))
            {
                try
                {
                    _identityClient = new CommunicationIdentityClient(_connectionString);
                    _logger.LogInformation("ACS Identity client initialized for user management");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to initialize ACS Identity client for user management");
                }
            }
            else
            {
                _logger.LogWarning("ACS connection string not found. User management will use simulation mode.");
            }
        }

        public async Task<string> GetOrCreateAcsUserAsync(string email, string? name = null)
        {
            try
            {
                _logger.LogInformation($"[DEBUG] GetOrCreateAcsUserAsync called for email: {email}");

                // First, get or create user in our database
                var user = await GetOrCreateUserAsync(email, name);
                _logger.LogInformation($"[DEBUG] User retrieved/created: UserId={user.UserId}, Email={user.EmailId}, AcsUserId={user.AcsUserId}");

                // If user already has ACS ID, return it (REUSE EXISTING IDENTITY)
                if (!string.IsNullOrEmpty(user.AcsUserId))
                {
                    _logger.LogInformation($"✅ REUSING existing ACS user ID for {email}: {user.AcsUserId}");
                    await UpdateUserLastActiveAsync(email);
                    return user.AcsUserId;
                }

                // Create new ACS user identity only if needed
                if (_identityClient != null)
                {
                    _logger.LogInformation($"[DEBUG] Creating NEW ACS user via real ACS client for {email}");
                    var userResponse = await _identityClient.CreateUserAsync();
                    var acsUserId = userResponse.Value.Id;

                    // Store ACS user ID in database
                    await _unitOfWork.UserRepository.UpdateAcsUserIdAsync(user.UserId, acsUserId);
                    await _unitOfWork.SaveAsync();

                    _logger.LogInformation($"✅ Created NEW ACS user for {email}: {acsUserId}");
                    return acsUserId;
                }
                else
                {
                    _logger.LogInformation($"[DEBUG] Creating simulated ACS user for {email}");
                    // Fallback: Use deterministic ID for simulation
                    var emailHash = email.GetHashCode().ToString("X");
                    var simulatedUserId = $"8:acs:sim_{emailHash}";

                    // Store simulated ID in database
                    await _unitOfWork.UserRepository.UpdateAcsUserIdAsync(user.UserId, simulatedUserId);
                    await _unitOfWork.SaveAsync();

                    _logger.LogInformation($"✅ [SIMULATION] Created deterministic ACS user ID for {email}: {simulatedUserId}");
                    return simulatedUserId;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error getting/creating ACS user for {email}");
                throw;
            }
        }

        public async Task<User> GetOrCreateUserAsync(string email, string? name = null)
        {
            try
            {
                _logger.LogInformation($"[DEBUG] Calling UserRepository.GetOrCreateUserAsync for {email}");
                var user = await _unitOfWork.UserRepository.GetOrCreateUserAsync(email, name);
                _logger.LogInformation($"[DEBUG] UserRepository returned: UserId={user.UserId}, Email={user.EmailId}");
                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error getting/creating user for {email}");
                throw;
            }
        }

        public async Task<string?> GetAcsUserIdAsync(string email)
        {
            try
            {
                var user = await _unitOfWork.UserRepository.GetByEmailAsync(email);
                return user?.AcsUserId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting ACS user ID for {email}");
                return null;
            }
        }

        public async Task<bool> IsUserExistsAsync(string email)
        {
            try
            {
                var user = await _unitOfWork.UserRepository.GetByEmailAsync(email);
                return user != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking if user exists for {email}");
                return false;
            }
        }

        public async Task UpdateUserLastActiveAsync(string email)
        {
            try
            {
                var user = await _unitOfWork.UserRepository.GetByEmailAsync(email);
                if (user != null)
                {
                    user.LastActiveDateTime = DateTime.UtcNow;
                    user.UpdatedDate = DateTime.UtcNow;
                    await _unitOfWork.UserRepository.UpdateAsync(user);
                    await _unitOfWork.SaveAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating last active time for {email}");
            }
        }
    }
}
