using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IUserThemeRepository
    {
        Task AddAsync(UserTheme userTheme);
        Task UpdateAsync(UserTheme userTheme);
        Task DeleteByIdAsync(Guid id);
        Task<UserTheme> GetByIdAsync(Guid id);
        Task<List<UserTheme>> GetAllAsync();
    }
}
