﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class PlanLabs
    {
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] public ILabTestsService _labTestsService { get; set; }
        [Inject] private PatientService _PatientService { get; set; }

        [Inject] private SharedNotesService SharedNotesService { get; set; }
        [Inject] private IAssessmentsService assessmentsService { get; set; }
        private MudDialog _planLabs;
        private DateTime? _CreatedDate = DateTime.Now;
        private DateTime? _UpdatedDate = DateTime.Now;

        private List<LabTests> planlabs { get; set; } = new List<LabTests>();
        private SfRichTextEditor RichTextEditor;
        public SfGrid<LabTests> PlanLabsGrid { get; set; }
       

        public Guid PatientId { get; set; }
        private string editorContent;
        public List<LabTests> deleteList = new List<LabTests>();
        public List<LabTests> AddList = new List<LabTests>();
        public List<AssessmentsData> Localdata { get; set; } = new List<AssessmentsData>();
        private List<string> AssessmentDiagnosis = new List<string>();
        //private string? _Description = "Enter Additional Reviews";
        /// <summary>
        /// Initializes the component asynchronously by setting the Patient ID,
        /// fetching the active review of system records, and formatting the editor content.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = _PatientService.PatientData.Id;
            Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientId))
                .GroupBy(a=>a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
            AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();
            SharedNotesService.OnChange += UpdateAssessments;
            planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
            editorContent = string.Join("<br>", planlabs.Select(m =>
                $"Created Date: {(m.CreatedDate.HasValue ? m.CreatedDate.Value.ToShortDateString() : Localizer["NoDate"])}, " +
                $"Lab Test 1: {(string.IsNullOrWhiteSpace(m.LabTest1) ? Localizer["NoValue"] : m.LabTest1)}, " +
                $"Lab Test 2: {(string.IsNullOrWhiteSpace(m.LabTest2) ? Localizer["NoValue"] : m.LabTest2)}, " +
                $"Test Organization: {(string.IsNullOrWhiteSpace(m.TestOrganization) ? Localizer["NoValue"] : m.TestOrganization)}"
            ));
        }
        private void UpdateAssessments()
        {
            OnInitializedAsync();
            StateHasChanged();  
        }
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
        };

        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests Labs) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", Labs.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    Labs.AssessmentData = value;
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        Labs.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(Labs.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");
            builder.CloseComponent();
        };
        private List<string> OrganizationOptions => new List<string>
        {
                Localizer["Quest Inc"].Value,
                Localizer["Lab Corp"].Value
        };
        private RenderFragment<object> OrganizationEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests labTest) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", OrganizationOptions);
            builder.AddAttribute(2, "Value", labTest.TestOrganization);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    labTest.TestOrganization = value;
                }));
            builder.AddAttribute(4, "Placeholder", "Select Organization");
            builder.CloseComponent();
        };

       
        /// <summary>
        /// Opens the new dialog box .
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task OpenNewDialogBox()
        {
            await _planLabs.ShowAsync();
        }

        /// <summary>
        /// Closes the new dialog box after resetting input fields.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _planLabs.CloseAsync();
        }

        /// <summary>
        /// Adds a new history record to the ReviewOfSystem list and refreshes the grid.
        /// </summary>
        private async void AddNewHistory()
        {
            var newHistory = new LabTests
            {
                LabTestsId = Guid.NewGuid(),
                PatientId = PatientId,
                PcpId = PatientId,
                OrganizationId = PatientId,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                IsActive = true,
            };

            AddList.Add(newHistory);
            planlabs.Add(newHistory);
            await PlanLabsGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Resets the input fields to their default values.
        /// </summary>
        private void ResetInputFields()
        {
            _CreatedDate = null;
        }

        /// <summary>
        /// Handles the action completion event for the LabTests grid.
        /// If an item is deleted, it is either removed from the add list or marked as inactive and added to the delete list.
        /// </summary>
        /// <param name="args">Event arguments containing the action details.</param>
        public void ActionCompletedHandler(ActionEventArgs<LabTests> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleteLabTest = args.Data as LabTests;
                var existingItem = AddList.FirstOrDefault(m => m.LabTestsId == deleteLabTest.LabTestsId);
                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
        }

        /// <summary>
        /// Handles the beginning of an action for the LabTests grid.
        /// If the action is a save operation, it updates the UpdatedDate.
        /// </summary>
        /// <param name="args">Event arguments containing the action details.</param>
        public void ActionBeginHandler(ActionEventArgs<LabTests> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Cancels the data operation, clears lists, reloads data, and resets input fields.
        /// </summary>
        private async Task CancelData()
        {
            deleteList.Clear();
            AddList.Clear();
            planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Saves the data by adding new items and updating existing ones.
        /// Updates the editor content with formatted review system data.
        /// </summary>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _labTestsService.AddLabTestsAsync(AddList);
            }
            await _labTestsService.UpdateLabTestsListAsync(planlabs);
            await _labTestsService.UpdateLabTestsListAsync(deleteList);
            deleteList.Clear();
            AddList.Clear();
            planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
            editorContent = string.Join("<br>", planlabs.Select(m =>
                $"Created Date: {(m.CreatedDate.HasValue ? m.CreatedDate.Value.ToShortDateString() : Localizer["NoDate"])}, " +
                $"Lab Test 1: {(string.IsNullOrWhiteSpace(m.LabTest1) ? Localizer["NoValue"] : m.LabTest1)}, " +
                $"Lab Test 2: {(string.IsNullOrWhiteSpace(m.LabTest2) ? Localizer["NoValue"] : m.LabTest2)}, " +
                $"Test Organization: {(string.IsNullOrWhiteSpace(m.TestOrganization) ? Localizer["NoValue"] : m.TestOrganization)}"
            ));
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

    }
}