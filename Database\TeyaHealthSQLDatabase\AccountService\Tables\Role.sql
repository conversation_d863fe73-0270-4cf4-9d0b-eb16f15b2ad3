﻿CREATE TABLE [AccountService].[Role] (
    [RoleId]         UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [RoleName]       NVARCHAR (100)   NOT NULL,
    [CreatedDate]    DATETIME         NULL,
    [UpdatedDate]    DATETIM<PERSON>         NULL,
    [UpdatedBy]      UNIQUE<PERSON>ENTIFIER NULL,
    [OrganizationId] UNIQUEIDENTIFIER NULL,
    [IsActive]       BIT              NULL,
    CONSTRAINT [PK_Role] PRIMARY KEY CLUSTERED ([RoleId] ASC),
    CONSTRAINT [FK_Role_Organization] FOREIGN KEY ([OrganizationId]) REFERENCES [AccountService].[Organization] ([OrganizationId]) ON DELETE CASCADE
);

