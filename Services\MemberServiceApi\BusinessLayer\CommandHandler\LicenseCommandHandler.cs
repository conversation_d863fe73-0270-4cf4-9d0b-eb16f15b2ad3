﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class LicenseCommandHandler : ILicenseCommandHandler<ProductLicense>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public LicenseCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task AddLicense(List<ProductLicense> licenses)
        {
            await _unitOfWork.ProductLicenseRepository.AddAsync(licenses);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateLicense(ProductLicense license)
        {
            await _unitOfWork.ProductLicenseRepository.UpdateAsync(license);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteLicenseById(Guid id)
        {
            await _unitOfWork.ProductLicenseRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteLicenseByEntity(ProductLicense license)
        {
            await _unitOfWork.ProductLicenseRepository.DeleteByEntityAsync(license);
            await _unitOfWork.SaveAsync();
        }

        public async Task<bool> UpdateLicenseAccessAsync(List<ProductLicense> licenseAccessUpdates)
        {
            var existingRecords = await _unitOfWork.ProductLicenseRepository.GetAllAsync();

            foreach (var update in licenseAccessUpdates)
            {
                var recordToUpdate = existingRecords.FirstOrDefault(r => r.Id == update.Id);

                if (recordToUpdate != null)
                {
                    recordToUpdate.IsLicenseActivated = update.IsLicenseActivated;

                    await _unitOfWork.ProductLicenseRepository.UpdateAsync(recordToUpdate);
                }
                else
                {
                    return false;
                }
            }

            await _unitOfWork.SaveAsync();

            return true;
        }
    }
}
