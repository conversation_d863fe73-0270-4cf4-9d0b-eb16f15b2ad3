using MessageContracts;
using System;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public interface IUserRepository : IGenericRepository<User>
    {
        Task<User?> GetByEmailAsync(string email);
        Task<User?> GetByAcsUserIdAsync(string acsUserId);
        Task<User> GetOrCreateUserAsync(string email, string? name = null);
        Task UpdateAcsUserIdAsync(Guid userId, string acsUserId);
        Task UpdateAcsTokenAsync(Guid userId, string accessToken, DateTime expiryTime);
    }
}
