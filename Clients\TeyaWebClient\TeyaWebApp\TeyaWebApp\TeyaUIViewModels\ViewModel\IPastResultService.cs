﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPastResultService
    {
        Task<List<PastResult>> GetResultsByIdAsync(Guid id);
        Task AddResultAsync(List<PastResult> previous);
        Task DeletePastResultAsync(PastResult pastres);
        Task UpdateResultAsync(PastResult past);
        Task<List<PastResult>> GetResultByIdAsyncAndIsActive(Guid id);
        Task UpdatePastResultListAsync(List<PastResult> Pastres);
    }
}
