﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Graph.Models;

namespace TeyaWebApp.Components.Pages
{
    public partial class PastResults : ComponentBase
    {
        [Inject] public IPastResultService PastResultService { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }

        [Inject] private ISnackbar Snackbar { get; set; }
        private List<PastResult> pastresult { get; set; }
        private List<PastResult> AddList = new();
        private List<PastResult> DeleteList = new();
        public SfGrid<PastResult> PastResultsGrid { get; set; }
        public SfRichTextEditor RichTextEditor { get; set; }
        private MudDialog _pastResults;
        private Guid PatientId { get; set; }
        private string editorContent;
        private bool add = false;

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
        };

        /// <summary>
        /// Initializes the component asynchronously and loads patient past results data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientService.PatientData.Id;
            await LoadDataAsync();
        }

        /// <summary>
        /// Loads past results data asynchronously for the given patient.
        /// </summary>
        private async Task LoadDataAsync()
        {
            pastresult = await PastResultService.GetResultByIdAsyncAndIsActive(PatientId);
            editorContent = string.Join("<p>", pastresult.Select(display =>
                $"<b>Created Date:</b> {(display.CreatedDate.ToString("MM-dd-yyyy") ?? "N/A")}&emsp;" +
                $"<b>Order Name:</b> {display.OrderName}&emsp;" +
                $"<b>Order Date:</b> {(display.OrderDate?.ToString("MM-dd-yyyy") ?? "N/A")}&emsp;" +
                $"<b>Ordered By:</b> {display.OrderBy}&emsp;" +
                $"<b>Result Date:</b> {(display.ResultDate?.ToString("MM-dd-yyyy") ?? "N/A")}"
            ));
            await InvokeAsync(StateHasChanged);
        }




        /// <summary>
        /// Opens the add task dialog for adding new past result records.
        /// </summary>
        private async Task OpenNewDialogBox()
        {
            await _pastResults.ShowAsync();
        }

        /// <summary>
        /// Handles the completion of actions such as Add, Save, and Delete in the past result grid.
        /// </summary>
        /// <param name="args">Action event arguments containing the details of the operation.</param>
        private void ActionCompletedHandler(ActionEventArgs<PastResult> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedresults = args.Data as PastResult;
                    var existingItem = AddList.FirstOrDefault(s => s.ResultId == deletedresults.ResultId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedresults.UpdatedBy = Guid.Parse(User.id);
                        deletedresults.UpdatedDate = DateTime.Now;
                        deletedresults.IsActive = false;
                        DeleteList.Add(deletedresults);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.ResultId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = PatientService.PatientData.OrganizationID;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.IsActive = true;
                add = true;
            }
        }

        public void ActionBeginHandler(ActionEventArgs<PastResult> args)
        {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (add)
                    {
                        if (args.Data != null)
                        {
                            var addedhistories = args.Data;
                            if (addedhistories != null)
                            {
                                AddList.Add(addedhistories);
                            }
                        }
                        add = false;
                    }
                if (args.Data.CreatedDate.Date > DateTime.Now.Date)
                {
                    Snackbar.Add(@Localizer["Date cannot be in the future"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (args.Data.ResultDate.HasValue && args.Data.OrderDate.HasValue && args.Data.OrderDate > args.Data.ResultDate)
                    {
                        Snackbar.Add(@Localizer["Order Date cannot be after Result Date"], Severity.Warning);
                        args.Cancel = true;
                        return;
                    }
                    if (!string.IsNullOrEmpty(args.Data.OrderBy) && !IsAlphabeticWithSpaces(args.Data.OrderBy))
                    {
                        Snackbar.Add(@Localizer["Order By Location field should only contain alphabets and spaces"], Severity.Warning);
                        args.Cancel = true;
                        return;
                    }
                    DateTime today = DateTime.Today;
                    if (args.Data.OrderDate < today)
                    {
                        Snackbar.Add("Order Date cannot be in the past.", Severity.Warning);
                        args.Cancel = true;
                        return;
                    }
                if (!string.IsNullOrEmpty(args.Data.ViewResults) && !IsAlphabeticWithSpaces(args.Data. ViewResults))
                {
                    Snackbar.Add(@Localizer["ViewResults Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (!string.IsNullOrEmpty(args.Data.OrderType) && !IsAlphabeticWithSpaces(args.Data.OrderType))
                {
                    Snackbar.Add(@Localizer["OrderType Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (!string.IsNullOrEmpty(args.Data.OrderName) && !IsAlphabeticWithSpaces(args.Data.OrderName))
                {
                    Snackbar.Add(@Localizer["OrderName Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                }
            }

        /// <summary>
        /// Saves the changes made to the past results records by adding new entries and updating existing ones.
        /// </summary>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await PastResultService.AddResultAsync(AddList);
            }
            await PastResultService.UpdatePastResultListAsync(DeleteList);
            await PastResultService.UpdatePastResultListAsync(pastresult);
            await LoadDataAsync();
            AddList.Clear();
            DeleteList.Clear();
            await _pastResults.CloseAsync();
        }

        /// <summary>
        /// Cancels changes and reloads the original past results data.
        /// </summary>
        private async Task CancelData()
        {
            await LoadDataAsync();
            await _pastResults.CloseAsync();
        }

        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s/,.\\\(\)-]+$");
        }
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
    }
}