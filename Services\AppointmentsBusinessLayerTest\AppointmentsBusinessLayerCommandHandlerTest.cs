﻿using Moq;
using AppointmentContracts;
using AppointmentDataAccessLayer.AppointmentImplementation;
using BusinessLayer.CommandHandler;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Xunit;

namespace AppointmentsBusinessLayerTest
{
    public class AppointmentsBusinessLayerCommandHandlerTest
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<ILogger<AppointmentCommandHandler>> _mockLogger;
        private readonly Mock<IMigration> _mockMigration;
        private readonly AppointmentCommandHandler _handler;

        public AppointmentsBusinessLayerCommandHandlerTest()
        {
            
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLogger = new Mock<ILogger<AppointmentCommandHandler>>();
            _mockMigration = new Mock<IMigration>();

            _handler = new AppointmentCommandHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockMigration.Object
            );
        }

        [Fact]
        public async Task AddAppointment_ShouldCallAddAsync()
        {
            // Arrange
            var appointments = new List<Appointment>
{
            new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddHours(1),
                AppointmentDate = DateTime.Today,
                CreatedDate = DateTime.Now.AddDays(-5),
                UpdatedDate = DateTime.Now,
                Subscription = true
            },
            new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now.AddHours(2),
                EndTime = DateTime.Now.AddHours(3),
                AppointmentDate = DateTime.Today.AddDays(1),
                CreatedDate = DateTime.Now.AddDays(-3),
                UpdatedDate = DateTime.Now.AddDays(-1),
                Subscription = false
            },
            new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now.AddHours(4),
                EndTime = DateTime.Now.AddHours(5),
                AppointmentDate = DateTime.Today.AddDays(2),
                CreatedDate = DateTime.Now.AddDays(-2),
                UpdatedDate = DateTime.Now,
                Subscription = true
            },
            new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now.AddHours(6),
                EndTime = DateTime.Now.AddHours(7),
                AppointmentDate = DateTime.Today.AddDays(3),
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now,
                Subscription = false
            }
        };

            var shardId = Guid.NewGuid();

           
            _mockUnitOfWork.Setup(uow => uow.AppointmentRepository.AddAsync(appointments, shardId))
                .Returns(Task.CompletedTask);

            // Act
            await _handler.AddAppointment(appointments, shardId);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.AppointmentRepository.AddAsync(appointments, shardId), Times.Once);
        }

        [Fact]
        public async Task UpdateAppointment_ShouldCallUpdateAsync()
        {
            // Arrange
            var appointment = new Appointment
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ProviderId = Guid.NewGuid(),
                FacilityId = Guid.NewGuid(),
                OrganisationId = Guid.NewGuid(),
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddHours(2),
                AppointmentDate = DateTime.Now,
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now,
                Subscription = false
            };
            var shardId = Guid.NewGuid();

            
            _mockUnitOfWork.Setup(uow => uow.AppointmentRepository.UpdateAsync(appointment, shardId))
                .Returns(Task.CompletedTask);

            // Act
            await _handler.UpdateAppointment(appointment, shardId);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.AppointmentRepository.UpdateAsync(appointment, shardId), Times.Once);
        }

        [Fact]
        public async Task DeleteAppointmentById_ShouldCallDeleteByIdAsync()
        {
            // Arrange
            var subscription = true;
            var shardId = Guid.NewGuid();
            var OrgID = Guid.NewGuid();

            _mockUnitOfWork.Setup(uow => uow.AppointmentRepository.DeleteByIdAsync(subscription, shardId,OrgID))
                .Returns(Task.CompletedTask);

            // Act
            await _handler.DeleteAppointmentById(subscription, shardId,OrgID);

            // Assert
            _mockUnitOfWork.Verify(uow => uow.AppointmentRepository.DeleteByIdAsync(subscription, shardId,OrgID), Times.Once);
        }

        [Fact]
        public void AddShard_ShouldCallCreateAndMigrateDataToNewShard_WhenSubscriptionIsZero()
        {
            // Arrange
            var shardRequest = new ShardRequest
            {
                Key = Guid.NewGuid(),
                Subscription = 0,
                ServerName = "serverName",
                DatabaseName = "databaseName"
            };

            // Act
            _handler.AddShard(shardRequest);

            // Assert
            _mockMigration.Verify(migration => migration.CreateAndMigrateDataToNewShard(
                shardRequest.ServerName,
                shardRequest.DatabaseName,
                It.IsAny<byte[]>()), Times.Once);
        }

        [Fact]
        public void AddShard_ShouldCallCreateShardForSingleTenant_WhenSubscriptionIsOne()
        {
            // Arrange
            var shardRequest = new ShardRequest
            {
                Key = Guid.NewGuid(),
                Subscription = 1,
                ServerName = "serverName",
                DatabaseName = "databaseName"
            };

            // Act
            _handler.AddShard(shardRequest);

            // Assert
            _mockMigration.Verify(migration => migration.CreateShardForSingleTenant(
                shardRequest.ServerName,
                shardRequest.DatabaseName,
                It.IsAny<byte[]>()), Times.Once);
        }
    }
}
