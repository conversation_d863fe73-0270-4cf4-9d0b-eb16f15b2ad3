using MessageContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IMessageCommandHandler<T>
    {
        Task<Guid> SendMessage(T message, List<AttachmentRequest>? attachments = null);
        Task MarkMessageAsRead(Guid messageId);
        Task DeleteMessage(Guid messageId);
        Task<bool> SoftDeleteMessage(Guid messageId, string deletedBy, string? reason = null);
        Task<bool> RestoreMessage(Guid messageId, string restoredBy, string? reason = null);
        Task<bool> ArchiveMessage(Guid messageId, string archivedBy);
        Task<bool> UnarchiveMessage(Guid messageId, string unarchivedBy);
        Task<bool> ToggleImportant(Guid messageId, bool isImportant, string updatedBy);
    }
}
