﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class InsuranceRepository : GenericRepository<Insurance>, IInsuranceRepository
    {
        private readonly AccountDatabaseContext _context;

        public InsuranceRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }

        public async Task<Insurance> GetByIdAsync(Guid id)
        {
            return await _context.Insurance
                .FirstOrDefaultAsync(ins => ins.InsuranceId == id);
        }
        public async Task<List<Insurance>> GetAllAsync()
        {
            return await _context.Insurance.ToListAsync();
        }
    }
}
