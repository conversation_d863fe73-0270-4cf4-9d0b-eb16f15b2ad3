﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class FamilyMemberRepository : GenericRepository<FamilyMember>, IFamilyMemberRepository
    {
        private readonly RecordDatabaseContext _context;

        public FamilyMemberRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<List<FamilyMember>> GetFamilyMembersById(Guid recordId)
        {
            return await _context.FamilyMembers
                .Where(wt => wt.PatientId == recordId)
                .ToListAsync();
        }

    }
}
