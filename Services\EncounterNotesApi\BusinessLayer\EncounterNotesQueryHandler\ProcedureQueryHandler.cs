﻿using System;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ProcedureQueryHandler : IProcedureQueryHandler<Procedure>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        public ProcedureQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }
        public async Task<IEnumerable<Procedure>> GetProcedureByPatientId(Guid patientId)
        {
            var getByPatientId = await _unitOfWork.ProcedureRepository.GetAsync();
            return getByPatientId.Where(Assess => Assess.PatientId == patientId && Assess.IsDeleted == !false);
        }
    }
}