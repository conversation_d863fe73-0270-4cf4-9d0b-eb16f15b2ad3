﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface ISurgicalHistoryCommandHandler<TText>
    {
        Task DeleteSurgeryByEntity(SurgicalHistory surgicalhistory);
        Task DeleteSurgeryById(Guid id);
        Task AddSurgery(List<TText> texts);
        Task UpdateSurgery(SurgicalHistory surgicalhistory);
        Task UpdateSurgeryList(List<SurgicalHistory> Surgeries);
    }
}