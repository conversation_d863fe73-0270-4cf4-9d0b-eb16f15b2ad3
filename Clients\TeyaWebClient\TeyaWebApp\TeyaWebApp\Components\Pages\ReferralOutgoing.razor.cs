﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;


namespace TeyaWebApp.Components.Pages
{
    public partial class ReferralOutgoing : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientID { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] IReferralOutgoingService _ReferralOutgoingService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        private string editorContent;
        private MudDialog _ReferralOutgoingdialog;
        private SfRichTextEditor RichTextEditor;
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };
        private List<string> ToolbarItems = new List<string> { "Add" };
        public string temperature { get; set; }
        public string weight { get; set; }
        public string height { get; set; }
        public string pulse { get; set; }
        public string blood_Pressure { get; set; }
        public SfGrid<PatientReferralOutgoing> ReferralOutgoingGrid { get; set; }
        private List<PatientReferralOutgoing> ReferralOutgoings { get; set; }
        private List<PatientReferralOutgoing> AddList = new();
        private List<PatientReferralOutgoing> DeleteList = new();

        /// <summary>
        /// Open Edit Dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _ReferralOutgoingdialog.ShowAsync();
        }

        /// <summary>
        /// Close Edit Dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _ReferralOutgoingdialog.CloseAsync();
        }
        bool add = false;

        /// <summary>
        /// Retrieve ReferralOutgoing and set rich text editor
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            ReferralOutgoings = await _ReferralOutgoingService.GetReferralOutgoingsByIdAsyncAndIsActive(PatientID);
            editorContent = string.Join("<p>",
            ReferralOutgoings.OrderByDescending(v => v.CreatedDate)
            .Select(display => $"<b>{display.CreatedDate.ToString("MM-dd-yyyy")}</b>&emsp;{display.ReferralReason}"));

        }

        /// <summary>
        /// Event Handler for add,edit,delete
        /// </summary>
        /// <param name="args"></param>
        private void ActionCompletedHandler(ActionEventArgs<PatientReferralOutgoing> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedReferralOutgoing = args.Data as PatientReferralOutgoing;
                    var existingItem = AddList.FirstOrDefault(v => v.PlanReferralId == deletedReferralOutgoing.PlanReferralId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedReferralOutgoing.isActive = false;
                        deletedReferralOutgoing.UpdatedBy = Guid.Parse(User.id);
                        deletedReferralOutgoing.UpdatedDate = DateTime.Now;
                        DeleteList.Add(deletedReferralOutgoing);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.PlanReferralId = Guid.NewGuid();
                args.Data.PatientId = PatientID;
                args.Data.OrganizationId = _PatientService.PatientData.OrganizationID;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.isActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedReferralOutgoing = args.Data;
                        if (addedReferralOutgoing != null)
                        {
                            AddList.Add(addedReferralOutgoing); 
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Handle BackdropClick
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Save changes to database
        /// </summary>
        /// <returns></returns>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await _ReferralOutgoingService.AddReferralOutgoingAsync(AddList);
            }
            await _ReferralOutgoingService.UpdateReferralOutgoingsListAsync(DeleteList);
            await _ReferralOutgoingService.UpdateReferralOutgoingsListAsync(ReferralOutgoings);
            AddList.Clear();
            DeleteList.Clear();
            editorContent = string.Join("<p>",
            ReferralOutgoings.OrderByDescending(v => v.CreatedDate)
            .Select(display => $"<b>{display.CreatedDate.ToString("MM-dd-yyyy")}</b>&emsp;{display.ReferralReason}"));
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Undo changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelChanges()
        {

            DeleteList.Clear();
            AddList.Clear();
            ReferralOutgoings = await _ReferralOutgoingService.GetReferralOutgoingsByIdAsyncAndIsActive(PatientID);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }
    }
}