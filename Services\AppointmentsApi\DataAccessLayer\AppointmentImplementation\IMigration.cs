﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AppointmentDataAccessLayer.AppointmentImplementation
{
    public interface IMigration
    {
        void CreateAndMigrateDataToNewShard(string serverName, string databaseName, byte[] key);
        void CreateShardForSingleTenant(string serverName, string databaseName, byte[] newShardKey);
    }
}
