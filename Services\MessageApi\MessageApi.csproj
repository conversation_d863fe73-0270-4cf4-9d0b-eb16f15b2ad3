﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="BusinessLayer - Copy\**" />
    <Compile Remove="BusinessLayer\**" />
    <Compile Remove="Contracts\**" />
    <Compile Remove="MessageBusinessLayer\**" />
    <Compile Remove="MessageDataAccessLayer\**" />
    <Content Remove="BusinessLayer - Copy\**" />
    <Content Remove="BusinessLayer\**" />
    <Content Remove="Contracts\**" />
    <Content Remove="MessageBusinessLayer\**" />
    <Content Remove="MessageDataAccessLayer\**" />
    <EmbeddedResource Remove="BusinessLayer - Copy\**" />
    <EmbeddedResource Remove="BusinessLayer\**" />
    <EmbeddedResource Remove="Contracts\**" />
    <EmbeddedResource Remove="MessageBusinessLayer\**" />
    <EmbeddedResource Remove="MessageDataAccessLayer\**" />
    <None Remove="BusinessLayer - Copy\**" />
    <None Remove="BusinessLayer\**" />
    <None Remove="Contracts\**" />
    <None Remove="MessageBusinessLayer\**" />
    <None Remove="MessageDataAccessLayer\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.EntityFramework.SqlServer" Version="6.5.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.4" />
    <PackageReference Include="Azure.Communication.Chat" Version="1.3.0" />
    <PackageReference Include="Azure.Communication.Identity" Version="1.3.1" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="Contracts\MessageContracts.csproj" />
    <ProjectReference Include="MessageBusinessLayer\MessageBusinessLayer.csproj" />
    <ProjectReference Include="MessageDataAccessLayer\MessageDataAccessLayer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\" />
  </ItemGroup>

</Project>
