using MessageContracts;
using MessageBusinessLayer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageApi.Controllers
{
    // [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")] // Temporarily disabled for testing
    // [Authorize] // Temporarily disabled for testing user management
    [Route("api/[controller]")]
    [ApiController]
    public class MessageController : ControllerBase
    {
        private readonly IMessageCommandHandler<Message> _messageCommandHandler;
        private readonly IMessageQueryHandler<Message> _messageQueryHandler;
        private readonly IAttachmentService _attachmentService;
        private readonly ILogger<MessageController> _logger;
        private readonly IStringLocalizer<MessageController> _localizer;

        public MessageController(
            IMessageCommandHandler<Message> messageCommandHandler,
            IMessageQueryHandler<Message> messageQueryHandler,
            IAttachmentService attachmentService,
            ILogger<MessageController> logger,
            IStringLocalizer<MessageController> localizer)
        {
            _messageCommandHandler = messageCommandHandler;
            _messageQueryHandler = messageQueryHandler;
            _attachmentService = attachmentService;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpPost("send")]
        public async Task<ActionResult<Guid>> SendMessage([FromBody] SendMessageRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("SendMessage validation failed:");
                    foreach (var error in ModelState)
                    {
                        _logger.LogWarning($"Field: {error.Key}, Errors: {string.Join(", ", error.Value.Errors.Select(e => e.ErrorMessage))}");
                    }
                    return BadRequest(ModelState);
                }

                var sentMessageIds = new List<Guid>();
                var allRecipients = new List<string>();

                // Collect all recipients
                if (request.ToEmails?.Any() == true)
                {
                    allRecipients.AddRange(request.ToEmails);
                }
                else if (!string.IsNullOrEmpty(request.ReceiverEmailId))
                {
                    // Fallback to single recipient for backward compatibility
                    allRecipients.Add(request.ReceiverEmailId);
                }

                if (request.CcEmails?.Any() == true)
                {
                    allRecipients.AddRange(request.CcEmails);
                }

                if (request.BccEmails?.Any() == true)
                {
                    allRecipients.AddRange(request.BccEmails);
                }

                if (!allRecipients.Any())
                {
                    return BadRequest("At least one recipient is required");
                }

                // Send message to each recipient
                foreach (var recipientEmail in allRecipients.Distinct())
                {
                    var message = new Message
                    {
                        SenderName = request.SenderName,
                        SenderEmailId = request.SenderEmailId,
                        ReceiverName = recipientEmail, // Use email as name for now
                        ReceiverEmailId = recipientEmail,
                        Subject = request.Subject,
                        MessageContent = request.MessageContent
                    };

                    var messageId = await _messageCommandHandler.SendMessage(message, request.Attachments);
                    sentMessageIds.Add(messageId);
                }

                var response = new
                {
                    MessageIds = sentMessageIds,
                    PrimaryMessageId = sentMessageIds.FirstOrDefault(),
                    Status = $"Message sent successfully to {allRecipients.Count} recipient(s)",
                    RecipientCount = allRecipients.Count,
                    AttachmentCount = request.Attachments?.Count ?? 0
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message");
                return StatusCode(500, "Internal server error while sending message");
            }
        }

        [HttpGet("email/{email}")]
        public async Task<ActionResult<IEnumerable<Message>>> GetMessagesByEmail(string email)
        {
            try
            {
                var messages = await _messageQueryHandler.GetMessagesByEmail(email);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting messages for email {email}");
                return StatusCode(500, "Internal server error while retrieving messages");
            }
        }

        [HttpGet("conversation/{senderEmail}/{receiverEmail}")]
        public async Task<ActionResult<IEnumerable<Message>>> GetConversation(string senderEmail, string receiverEmail)
        {
            try
            {
                var messages = await _messageQueryHandler.GetConversation(senderEmail, receiverEmail);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting conversation between {senderEmail} and {receiverEmail}");
                return StatusCode(500, "Internal server error while retrieving conversation");
            }
        }

        [HttpGet("{messageId}")]
        public async Task<ActionResult<Message>> GetMessageById(Guid messageId)
        {
            try
            {
                var message = await _messageQueryHandler.GetMessageById(messageId);
                if (message == null)
                {
                    return NotFound($"Message with ID {messageId} not found");
                }
                return Ok(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting message {messageId}");
                return StatusCode(500, "Internal server error while retrieving message");
            }
        }

        [HttpGet("unread/{email}")]
        public async Task<ActionResult<IEnumerable<Message>>> GetUnreadMessages(string email)
        {
            try
            {
                var messages = await _messageQueryHandler.GetUnreadMessages(email);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting unread messages for email {email}");
                return StatusCode(500, "Internal server error while retrieving unread messages");
            }
        }

        [HttpGet("sent/{email}")]
        public async Task<ActionResult<IEnumerable<Message>>> GetSentMessages(string email)
        {
            try
            {
                var messages = await _messageQueryHandler.GetSentMessages(email);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting sent messages for email {email}");
                return StatusCode(500, "Internal server error while retrieving sent messages");
            }
        }

        [HttpGet("received/{email}")]
        public async Task<ActionResult<IEnumerable<Message>>> GetReceivedMessages(string email)
        {
            try
            {
                var messages = await _messageQueryHandler.GetReceivedMessages(email);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting received messages for email {email}");
                return StatusCode(500, "Internal server error while retrieving received messages");
            }
        }

        [HttpPut("{messageId}/read")]
        public async Task<ActionResult> MarkMessageAsRead(Guid messageId)
        {
            try
            {
                await _messageCommandHandler.MarkMessageAsRead(messageId);
                return Ok(new { Status = "Message marked as read" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error marking message {messageId} as read");
                return StatusCode(500, "Internal server error while updating message status");
            }
        }

        [HttpDelete("{messageId}")]
        public async Task<ActionResult> DeleteMessage(Guid messageId)
        {
            try
            {
                await _messageCommandHandler.DeleteMessage(messageId);
                return Ok(new { Status = "Message permanently deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error permanently deleting message {messageId}");
                return StatusCode(500, "Internal server error while deleting message");
            }
        }

        [HttpPost("{messageId}/soft-delete")]
        public async Task<ActionResult> SoftDeleteMessage(Guid messageId, [FromBody] SoftDeleteMessageRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (messageId != request.MessageId)
                {
                    return BadRequest("Message ID in URL does not match request body");
                }

                var result = await _messageCommandHandler.SoftDeleteMessage(messageId, request.DeletedBy, request.Reason);
                if (result)
                {
                    return Ok(new { Status = "Message moved to trash successfully" });
                }
                else
                {
                    return NotFound(new { Status = "Message not found" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error soft deleting message {messageId}");
                return StatusCode(500, "Internal server error while moving message to trash");
            }
        }

        [HttpPost("{messageId}/restore")]
        public async Task<ActionResult> RestoreMessage(Guid messageId, [FromBody] RestoreMessageRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (messageId != request.MessageId)
                {
                    return BadRequest("Message ID in URL does not match request body");
                }

                var result = await _messageCommandHandler.RestoreMessage(messageId, request.RestoredBy, request.Reason);
                if (result)
                {
                    return Ok(new { Status = "Message restored successfully" });
                }
                else
                {
                    return NotFound(new { Status = "Message not found in trash" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error restoring message {messageId}");
                return StatusCode(500, "Internal server error while restoring message");
            }
        }

        [HttpGet("deleted/{email}")]
        public async Task<ActionResult<List<Message>>> GetDeletedMessages(string email)
        {
            try
            {
                var messages = await _messageQueryHandler.GetDeletedMessagesByEmail(email);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting deleted messages for email {email}");
                return StatusCode(500, "Internal server error while retrieving deleted messages");
            }
        }

        [HttpPost("{messageId}/archive")]
        public async Task<ActionResult> ArchiveMessage(Guid messageId, [FromBody] ArchiveMessageRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (messageId != request.MessageId)
                {
                    return BadRequest("Message ID in URL does not match request body");
                }

                var result = await _messageCommandHandler.ArchiveMessage(messageId, request.ArchivedBy);
                if (result)
                {
                    return Ok(new { Status = "Message archived successfully" });
                }
                else
                {
                    return NotFound(new { Status = "Message not found" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error archiving message {messageId}");
                return StatusCode(500, "Internal server error while archiving message");
            }
        }

        [HttpPost("{messageId}/unarchive")]
        public async Task<ActionResult> UnarchiveMessage(Guid messageId, [FromBody] UnarchiveMessageRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (messageId != request.MessageId)
                {
                    return BadRequest("Message ID in URL does not match request body");
                }

                var result = await _messageCommandHandler.UnarchiveMessage(messageId, request.UnarchivedBy);
                if (result)
                {
                    return Ok(new { Status = "Message unarchived successfully" });
                }
                else
                {
                    return NotFound(new { Status = "Message not found" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error unarchiving message {messageId}");
                return StatusCode(500, "Internal server error while unarchiving message");
            }
        }

        // Attachment endpoints
        [HttpGet("{messageId}/attachments")]
        public async Task<ActionResult<IEnumerable<MessageAttachment>>> GetMessageAttachments(Guid messageId)
        {
            try
            {
                var attachments = await _attachmentService.GetMessageAttachmentsAsync(messageId);
                return Ok(attachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachments for message {messageId}");
                return StatusCode(500, "Internal server error while retrieving attachments");
            }
        }

        [HttpGet("attachment/{attachmentId}")]
        public async Task<ActionResult<MessageAttachment>> GetAttachment(Guid attachmentId)
        {
            try
            {
                var attachment = await _attachmentService.GetAttachmentAsync(attachmentId);
                if (attachment == null)
                {
                    return NotFound($"Attachment with ID {attachmentId} not found");
                }
                return Ok(attachment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachment {attachmentId}");
                return StatusCode(500, "Internal server error while retrieving attachment");
            }
        }

        [HttpGet("attachment/{attachmentId}/info")]
        public async Task<ActionResult> GetAttachmentInfo(Guid attachmentId)
        {
            try
            {
                var attachment = await _attachmentService.GetAttachmentAsync(attachmentId);
                if (attachment == null)
                {
                    return NotFound($"Attachment with ID {attachmentId} not found");
                }

                return Ok(new
                {
                    AttachmentId = attachment.AttachmentId,
                    MessageId = attachment.MessageId,
                    FileName = attachment.FileName,
                    OriginalFileName = attachment.OriginalFileName,
                    ContentType = attachment.ContentType,
                    FileSizeBytes = attachment.FileSizeBytes,
                    BlobFileName = attachment.BlobFileName,
                    BlobStorageUrl = attachment.BlobStorageUrl,
                    BlobContainerName = attachment.BlobContainerName,
                    FileHash = attachment.FileHash,
                    UploadedDateTime = attachment.UploadedDateTime,
                    IsScanned = attachment.IsScanned,
                    IsSafe = attachment.IsSafe
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting attachment info {attachmentId}");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("attachment/{attachmentId}/download")]
        public async Task<ActionResult> DownloadAttachment(Guid attachmentId)
        {
            try
            {
                _logger.LogInformation($"Attempting to download attachment: {attachmentId}");

                var attachment = await _attachmentService.GetAttachmentAsync(attachmentId);
                if (attachment == null)
                {
                    _logger.LogWarning($"Attachment not found in database: {attachmentId}");
                    return NotFound($"Attachment with ID {attachmentId} not found");
                }

                _logger.LogInformation($"Found attachment: {attachment.OriginalFileName}, BlobFileName: {attachment.BlobFileName}");

                var content = await _attachmentService.GetAttachmentContentAsync(attachmentId);

                _logger.LogInformation($"Successfully retrieved content for attachment: {attachmentId}, Size: {content.Length} bytes");

                return File(content, attachment.ContentType, attachment.OriginalFileName);
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError(ex, $"File not found for attachment {attachmentId}");
                return NotFound($"Attachment file not found: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error downloading attachment {attachmentId}");
                return StatusCode(500, $"Internal server error while downloading attachment: {ex.Message}");
            }
        }

        [HttpDelete("attachment/{attachmentId}")]
        public async Task<ActionResult> DeleteAttachment(Guid attachmentId)
        {
            try
            {
                var result = await _attachmentService.DeleteAttachmentAsync(attachmentId);
                if (!result)
                {
                    return NotFound($"Attachment with ID {attachmentId} not found");
                }
                return Ok(new { Status = "Attachment deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting attachment {attachmentId}");
                return StatusCode(500, "Internal server error while deleting attachment");
            }
        }

        [HttpPost("{messageId}/toggle-important")]
        public async Task<ActionResult> ToggleImportant(Guid messageId, [FromBody] ToggleImportantRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (messageId != request.MessageId)
                {
                    return BadRequest("Message ID in URL does not match request body");
                }

                var result = await _messageCommandHandler.ToggleImportant(messageId, request.IsImportant, request.UpdatedBy);
                if (result)
                {
                    var status = request.IsImportant ? "marked as important" : "unmarked as important";
                    return Ok(new { Status = $"Message {status} successfully" });
                }
                else
                {
                    return NotFound(new { Status = "Message not found" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error toggling important status for message {messageId}");
                return StatusCode(500, "Internal server error while updating important status");
            }
        }


    }
}
