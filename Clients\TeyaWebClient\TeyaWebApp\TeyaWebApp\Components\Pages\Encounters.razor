﻿@page "/Encounters"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject IProgressNotesService ProgressNotesService
@inject HttpClient Http


<GenericCard>
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4">
        @if (records == null)
        {
            <MudAlert Severity="Severity.Info">@Localizer["NoRecordsFound"]</MudAlert>
        }
        else
        {
            <MudGrid>
                <MudItem xs="12" Class="mx-auto">
                    @foreach (var record in records)
                    {
                        <MudCard Elevation="4" Class="mb-6 my-card">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">@record.PatientName</MudText>
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">Record Date: @record.DateTime.ToString("g")</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent Class="p-4">
                                <div class="d-flex">
                                    <div class="flex-fill me-2">
                                        <!-- First Column -->
                                        <div class="first-column">
                                            @foreach (var notes in JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes))
                                            {
                                                <MudText Typo="Typo.h6" Style="margin-top: 16px;">@notes.Key</MudText>
                                                @foreach (var note in notes.Value)
                                                {
                                                    <MudText Typo="Typo.body1" Style="margin-top: 16px; margin-bottom: 16px; font-weight:bold">@note.Key</MudText>
                                                    <SfRichTextEditor Value="@ConvertToHtml(notes.Value[note.Key])" ReadOnly="@IsReadOnly">
                                                        <RichTextEditorToolbarSettings Items="@Tools">
                                                        </RichTextEditorToolbarSettings>
                                                    </SfRichTextEditor>
                                                }
                                            }
                                        </div>
                                    </div>

                                    <div class="second-column ms-auto">
                                        <audio controls>
                                            <source src="@GetAudioUrl(record.Id)" type="@Localizer["audio/mp3"]" />
                                            Your browser does not support the audio tag.
                                        </audio>

                                        <MudPaper Class="mt-3 pa-4">
                                            <MudText Typo="Typo.h6">@Localizer["Transcription"]</MudText>
                                            <div class="conversation-container">
                                                @if (!string.IsNullOrEmpty(record.Transcription))
                                                {
                                                    var lines = record.Transcription.Split('\n');
                                                    string currentSpeaker = "";
                                                    Dictionary<string, string> speakerColors = new Dictionary<string, string>();
                                                    Dictionary<string, int> speakerPositions = new Dictionary<string, int>();
                                                    int speakerCount = 0;

                                                    @foreach (var line in lines)
                                                    {
                                                        if (string.IsNullOrWhiteSpace(line))
                                                        {
                                                            continue;
                                                        }

                                                        // Check if line starts with speaker identifier
                                                        var parts = line.Split(new[] { ":" }, 2, StringSplitOptions.None);
                                                        if (parts.Length == 2)
                                                        {
                                                            var speaker = parts[0].Trim();
                                                            var message = parts[1].Trim();
                                                            if (speaker.Equals("Unknown", StringComparison.OrdinalIgnoreCase))
                                                            {
                                                                continue;
                                                            }
                                                            currentSpeaker = speaker;

                                                            // Assign color and position if this is a new speaker
                                                            if (!speakerColors.ContainsKey(speaker))
                                                            {
                                                                speakerColors[speaker] = GetSpeakerColor(speakerCount);
                                                                speakerPositions[speaker] = speakerCount % 2; // 0 = left, 1 = right
                                                                speakerCount++;
                                                            }

                                                            var alignment = speakerPositions[speaker] == 0 ? "left-align" : "right-align";
                                                            string speakerColor = speakerColors[speaker];
                                                            string bubbleColor = GetBubbleColor(speakerColor);

                                                            <div class="conversation-message @alignment">
                                                                <div class="speaker-name-container" style=@($"color: {speakerColor}")>
                                                                    <MudText Typo="Typo.subtitle2" Class="speaker-name">@speaker</MudText>
                                                                </div>
                                                                <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                                                    <MudText Style="text-align: left;">@message</MudText>
                                                                </MudPaper>
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            // If no speaker identified, use the current speaker's alignment
                                                            if (!string.IsNullOrEmpty(currentSpeaker) && speakerPositions.ContainsKey(currentSpeaker))
                                                            {
                                                                var alignment = speakerPositions[currentSpeaker] == 0 ? "left-align" : "right-align";
                                                                string bubbleColor = GetBubbleColor(speakerColors[currentSpeaker]);

                                                                <div class="conversation-message @alignment">
                                                                    <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                                                        <MudText Style="text-align: left;">@line</MudText>
                                                                    </MudPaper>
                                                                </div>
                                                            }
                                                            else
                                                            {
                                                                // Fallback for lines without a speaker context
                                                                <div class="conversation-message left-align">
                                                                    <MudPaper Elevation="0" Class="message-bubble" Style="background-color: #f5f5f5">
                                                                        <MudText Style="text-align: left;">@line</MudText>
                                                                    </MudPaper>
                                                                </div>
                                                            }
                                                        }
                                                    }
                                                }
                                            </div>
                                        </MudPaper>
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    }
                </MudItem>
            </MudGrid>
        }
    </MudContainer>
</GenericCard>

<style>
    .my-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background-color: rgba(235, 225, 216, 1);
        width: 100%;
    }

        .my-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

    .first-column {
        display: flex;
        flex-direction: column;
    }

    .second-column {
        width: 400px;
        min-width: 300px;
    }

    .mud-card-content {
        padding: 16px;
    }

    .conversation-container {
        display: flex;
        flex-direction: column;
        gap: 6px;
        max-height: 500px;
        overflow-y: auto;
        padding: 10px;
    }

    .conversation-message {
        display: flex;
        flex-direction: column;
        max-width: 80%;
    }

    .left-align {
        align-self: flex-start;
    }

    .right-align {
        align-self: flex-end;
    }

        .right-align .speaker-name-container {
            align-self: flex-end;
        }

        .right-align .message-bubble {
            margin-left: auto;
        }

    .speaker-name {
        margin-bottom: 4px;
        font-weight: bold;
    }

    .message-bubble {
        padding: 8px;
        border-radius: 12px;
        word-break: break-word;
    }

    /* Extra styling for better readability */
    .left-align .message-bubble {
        border-top-left-radius: 2px;
    }

    .right-align .message-bubble {
        border-top-right-radius: 2px;
    }
</style>
