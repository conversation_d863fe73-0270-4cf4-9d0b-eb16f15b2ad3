# PowerShell script to run all unit tests for TeyaHealth messaging system

Write-Host "========================================" -ForegroundColor Green
Write-Host "TeyaHealth Messaging System Test Runner" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Function to run tests for a specific project
function Run-Tests {
    param(
        [string]$ProjectPath,
        [string]$ProjectName
    )
    
    Write-Host "`n--- Running tests for $ProjectName ---" -ForegroundColor Yellow
    
    if (Test-Path $ProjectPath) {
        try {
            Set-Location $ProjectPath
            Write-Host "Building test project..." -ForegroundColor Cyan
            dotnet build --configuration Release
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Running tests..." -ForegroundColor Cyan
                dotnet test --configuration Release --logger "console;verbosity=detailed" --collect:"XPlat Code Coverage"
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ Tests completed successfully for $ProjectName" -ForegroundColor Green
                } else {
                    Write-Host "❌ Tests failed for $ProjectName" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ Build failed for $ProjectName" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ Error running tests for $ProjectName`: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  Test project not found: $ProjectPath" -ForegroundColor Yellow
    }
}

# Store original location
$OriginalLocation = Get-Location

try {
    # Run Message API Backend Tests
    Run-Tests -ProjectPath "Services\MessageApi\MessageApi.Tests" -ProjectName "Message API Backend"
    
    # Run Frontend Tests
    Run-Tests -ProjectPath "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp.Tests" -ProjectName "TeyaWebApp Frontend"
    
    # Run EncounterNotes API Tests (for reference)
    Run-Tests -ProjectPath "Services\EncounterNotesApi\EncounterNotesService.Tests" -ProjectName "EncounterNotes API (Reference)"
    
    Write-Host "`n========================================" -ForegroundColor Green
    Write-Host "Test execution completed!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    # Generate test report summary
    Write-Host "`n--- Test Summary ---" -ForegroundColor Magenta
    Write-Host "1. Message API Backend Tests: Check console output above" -ForegroundColor White
    Write-Host "2. TeyaWebApp Frontend Tests: Check console output above" -ForegroundColor White
    Write-Host "3. Code coverage reports generated in TestResults folders" -ForegroundColor White
    
    Write-Host "`n--- Next Steps ---" -ForegroundColor Magenta
    Write-Host "1. Review test results above" -ForegroundColor White
    Write-Host "2. Fix any failing tests" -ForegroundColor White
    Write-Host "3. Check code coverage reports" -ForegroundColor White
    Write-Host "4. Add more test cases if needed" -ForegroundColor White
}
finally {
    # Return to original location
    Set-Location $OriginalLocation
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
