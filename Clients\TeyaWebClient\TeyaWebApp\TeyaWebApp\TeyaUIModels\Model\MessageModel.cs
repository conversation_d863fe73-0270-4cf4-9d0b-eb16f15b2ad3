using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TeyaUIModels.Model
{
    public class MessageModel : IModel
    {
        [Key]
        public Guid MessageId { get; set; }

        // Sender Information
        [Required]
        [StringLength(100)]
        public string SenderName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string SenderEmailId { get; set; } = string.Empty;

        // Receiver Information
        [Required]
        [StringLength(100)]
        public string ReceiverName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string ReceiverEmailId { get; set; } = string.Empty;

        // Message Content
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string MessageContent { get; set; } = string.Empty;

        // Timestamps
        public DateTime SentDateTime { get; set; } = DateTime.UtcNow;
        public DateTime? DeliveredDateTime { get; set; }
        public DateTime? ReadDateTime { get; set; }
        public DateTime? CreatedAt { get; set; } // Let database set this with GETUTCDATE()
        public DateTime? UpdatedAt { get; set; } // Let database set this with GETUTCDATE()

        // Status
        public MessageStatus Status { get; set; } = MessageStatus.Sent;

        // Azure Communication Services specific
        public string? AzureMessageId { get; set; }
        public string? ConversationId { get; set; }

        // Attachments
        public bool HasAttachments { get; set; } = false;
        public int AttachmentCount { get; set; } = 0;
        public virtual ICollection<AttachmentModel> Attachments { get; set; } = new List<AttachmentModel>();

        // Soft Delete Support
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedDateTime { get; set; }
        public string? DeletedBy { get; set; }

        // Archive Support
        public bool IsArchived { get; set; } = false;
        public DateTime? ArchivedDateTime { get; set; }
        public string? ArchivedBy { get; set; }

        // Important Support
        public bool IsImportant { get; set; } = false;

        // UI Helper Properties
        public bool IsRead => ReadDateTime.HasValue;

        public string GetPreviewText() =>
            MessageContent?.Length > 100 ?
            MessageContent[..100].Replace("<br>", " ") + "..." :
            MessageContent?.Replace("<br>", " ") ?? "";

        public string GetRelativeTime()
        {
            var span = DateTime.Now - SentDateTime;
            return span.TotalDays switch
            {
                > 365 => $"{Math.Floor(span.TotalDays / 365)}y",
                > 30 => $"{Math.Floor(span.TotalDays / 30)}mo",
                > 7 => $"{Math.Floor(span.TotalDays / 7)}w",
                > 1 => $"{Math.Floor(span.TotalDays)}d",
                _ => SentDateTime.ToString("h:mm tt")
            };
        }

        // EmailModel compatibility properties for UI
        public string Sender => SenderName;
        public string Body => MessageContent;
        public DateTime Date => SentDateTime;
        public List<string> Labels => new() { "message" };
        public bool HasAttachment => HasAttachments;
    }
}
