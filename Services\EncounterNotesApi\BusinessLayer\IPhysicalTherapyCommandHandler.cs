﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IPhysicalTherapyCommandHandler<TText>
    {
        Task DeletePhysicalTherapyByEntity(PhysicalTherapyData _PhysicalTherapy);
        Task DeletePhysicalTherapyById(Guid id);
        Task AddPhysicalTherapy(List<TText> texts);
        Task UpdatePhysicalTherapy(PhysicalTherapyData _PhysicalTherapy);
        Task UpdatePhysicalTherapyList(List<PhysicalTherapyData> _PhysicalTherapy);
    }
}
