using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;

namespace EncounterNotesDataAccessLayer.EncounterNotesContext
{
    public class RecordDatabaseContext : DbContext
    {
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        public RecordDatabaseContext(DbContextOptions<RecordDatabaseContext> options,
                                      IStringLocalizer<EncounterDataAccessLayer> localizer,
                                      ILogger<RecordDatabaseContext> logger)
            : base(options)
        {
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger;
        }
        public DbSet<WordTiming> WordTimings { get; set; }
        public DbSet<DiagnosticImagingAssessment> DiagnosticImagingAssessment { get; set; }
        public DbSet<Record> Records { get; set; }
        public DbSet<HospitalizationRecord> HospitalizationRecords { get; set; }
        public DbSet<FamilyMember> FamilyMembers { get; set; }
        public DbSet<Relations> Relations { get; set; }
        public DbSet<Templates> Templates { get; set; }
        public DbSet<PredefinedTemplates> PredefinedTemplates { get; set; }
        public DbSet<CurrentMedication> CurrentMedications { get; set; }
        public DbSet<ICD> ICD { get; set; }
        public DbSet<TherapeuticInterventionsList> TherapeuticInterventionsList { get; set; }
        public DbSet<Vaccines> Vaccines { get; set; }
        public DbSet<SoapNotesComponents> SoapNotesComponents { get; set; }
        public DbSet<PatientSocialHistory> SocialHistories { get; set; }
        public DbSet<SurgicalHistory> SurgicalHistories { get; set; }

        public DbSet<DiagnosticImage> DiagnosticImaging { get; set; }
        public DbSet<DiagnosticImagingDTO> DiagnosticImagingDTO { get; set; }
        public DbSet<Immunization> Immunizations { get; set; }
        public DbSet<PhysicalExamination> PhysicalExaminations { get; set; }
        public DbSet<PastResults> Past { get; set; }
        public DbSet<MedicalHistory> MedicalHistories { get; set; }
        public DbSet<HistoryOfPresentIllness> HistoryOfPresentIllnesses { get; set; }
        public DbSet<Symptoms>Symptoms { get; set; }
        public DbSet<Allergy> Allergies { get; set; }
        public DbSet<PhysicalTherapyData> _PhysicalTherapy { get; set; }
        public DbSet<AssessmentsData> _Assessments { get; set; }
        public DbSet<TherapeuticInterventionsData> _TherapeuticInterventions { get; set; }
        public DbSet<ReviewOfSystem> ReviewOfSystems { get; set; }
        public DbSet<Vitals> Vitals { get; set; }
        public DbSet<PatientReferralOutgoing> PatientReferralOutgoing { get; set; }
        public DbSet<ChiefComplaint> ChiefComplaint { get; set; }
        public DbSet<Procedure> Procedures { get; set; }
        public DbSet<CPT> CPTS { get; set; }
        public DbSet<ObHistory> ObHistory { get; set; }
        public DbSet<GynHistory> GynHistory { get; set; }
        public DbSet<Examination>Examination { get; set; }
        public DbSet<PrescriptionMedication> PrescriptionMedications { get; set; }
        public DbSet<LabTests> LabTests { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            try
            {

                modelBuilder.Entity<Record>().ToTable(_localizer["ProgressNotes"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Record>().Property(p => p.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);
                modelBuilder.Entity<WordTiming>().ToTable(_localizer["WordTimings"], _localizer["EncounterNotesService"]).HasKey(w => w.Id);
                modelBuilder.Entity<Record>()
                .HasMany(record => record.WordTimings)
                .WithOne(word => word.Record)
                .HasForeignKey(words => words.RecordId)
                .OnDelete(DeleteBehavior.Cascade);

                modelBuilder.Entity<HospitalizationRecord>().ToTable(_localizer["HospitalizationRecord"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<HospitalizationRecord>().HasKey(fm => fm.RecordID);
                modelBuilder.Entity<HospitalizationRecord>().Property(p => p.RecordID).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<DiagnosticImagingAssessment>().ToTable(_localizer["DiagnosticImagingAssessment"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<DiagnosticImagingAssessment>().HasKey(fm => fm.ID);
                modelBuilder.Entity<DiagnosticImagingAssessment>().Property(p => p.ID).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<DiagnosticImage>().ToTable(_localizer["DiagnosticImaging"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<DiagnosticImage>().HasKey(fm => fm.RecordID);
                modelBuilder.Entity<DiagnosticImage>().Property(p => p.RecordID).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<PredefinedTemplates>().ToTable(_localizer["ProgressNotesPredefinedTemplates"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<PredefinedTemplates>().Property(p => p.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<DiagnosticImagingDTO>().ToTable(_localizer["DiagnosticImagingDTO"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<DiagnosticImagingDTO>().HasKey(fm => fm.RecordID);
                modelBuilder.Entity<DiagnosticImagingDTO>().Property(p => p.RecordID).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<Templates>().ToTable(_localizer["ProgressNotesTemplates"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Templates>().Property(p => p.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<CurrentMedication>().ToTable(_localizer["PatientCurrentMedication"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<CurrentMedication>().HasKey(c => new { c.MedicineId, c.PatientId });

                modelBuilder.Entity<PrescriptionMedication>().ToTable(_localizer["PatientPrescriptionMedication"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<PrescriptionMedication>().HasKey(c => new { c.MedicineId, c.PatientId });

                modelBuilder.Entity<SoapNotesComponents>().ToTable(_localizer["SoapNotesComponents"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<SoapNotesComponents>().Property(p => p.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);
                modelBuilder.Entity<PatientSocialHistory>().ToTable(_localizer["PatientSocialHistory"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<PatientSocialHistory>().HasKey(c => new { c.SocialHistoryId, c.PatientId });

                modelBuilder.Entity<Vaccines>().ToTable(_localizer["Vaccines"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Vaccines>().Property(p => p.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<ICD>().ToTable(_localizer["ICD"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<ICD>().Property(p => p.Code).HasDefaultValueSql(_localizer["NewIdFunction"]);
                modelBuilder.Entity<ICD>(entity =>
                {
                    entity.HasNoKey();
                });


                modelBuilder.Entity<TherapeuticInterventionsList>().ToTable(_localizer["TherapeuticInterventionsList"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<TherapeuticInterventionsList>().Property(p => p.ID).HasDefaultValueSql(_localizer["NewIdFunction"]);
                modelBuilder.Entity<TherapeuticInterventionsList>(entity =>
                {
                    entity.HasNoKey();
                });

                modelBuilder.Entity<Immunization>().ToTable(_localizer["PatientImmunization"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Immunization>().HasKey(s => new { s.ImmunizationId, s.PatientId });

                modelBuilder.Entity<SurgicalHistory>().ToTable(_localizer["PatientSurgicalHistory"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<SurgicalHistory>().HasKey(s => new { s.SurgeryId, s.PatientId }); // Configure composite key

                modelBuilder.Entity<PhysicalExamination>().ToTable(_localizer["PatientPhysicalExamination"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<PhysicalExamination>().HasKey(p => new { p.ExaminationId, p.PatientId });
                modelBuilder.Entity<PastResults>().ToTable(_localizer["PatientPastResults"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<PastResults>().HasKey(s => new { s.ResultId, s.PatientId });


                modelBuilder.Entity<FamilyMember>().ToTable(_localizer["FamilyMember"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<FamilyMember>().HasKey(fm => fm.RecordID);
                modelBuilder.Entity<FamilyMember>().Property(p => p.RecordID).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<Relations>().ToTable(_localizer["Relations"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Relations>().HasKey(fm => fm.RelationId);
                modelBuilder.Entity<Relations>().Property(p => p.RelationId).HasDefaultValueSql(_localizer["NewIdFunction"]);

                modelBuilder.Entity<HistoryOfPresentIllness>().ToTable(_localizer["HistoryOfPresentIllness"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<HistoryOfPresentIllness>().HasKey(hpi=> new { hpi.Id, hpi.PatientId });
                modelBuilder.Entity<Symptoms>().ToTable("Symptoms", _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Symptoms>().HasKey(fm => fm.SymptomId);

                modelBuilder.Entity<Allergy>().ToTable(_localizer["Allergy"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Allergy>().HasKey(c => new { c.MedicineId, c.PatientId });

                modelBuilder.Entity<ChiefComplaint>().ToTable(_localizer["ChiefComplaint"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<ChiefComplaint>().HasKey(cc => cc.Id);
                modelBuilder.Entity<ChiefComplaint>().Property(cc => cc.PatientId).IsRequired();
                modelBuilder.Entity<ChiefComplaint>().Property(cc => cc.OrganizationId).IsRequired();
            
                modelBuilder.Entity<MedicalHistory>().ToTable(_localizer["PatientMedicalHistory"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<MedicalHistory>().HasKey(s => new { s.MedicalHistoryID, s.PatientId }); // Configure composite key

                modelBuilder.Entity<AssessmentsData>().ToTable(_localizer["Assessments"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<AssessmentsData>().HasKey(s => new { s.AssessmentsID, s.PatientId });

                modelBuilder.Entity<TherapeuticInterventionsData>().ToTable(_localizer["PatientTherapeuticInterventions"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<TherapeuticInterventionsData>().HasKey(s => new { s.TherapeuticInterventionsID, s.PatientId });

                modelBuilder.Entity<PhysicalTherapyData>().ToTable(_localizer["PhysicalTherapy"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<PhysicalTherapyData>().HasKey(s => new { s.PhysicalTherapyID, s.PatientId });

                modelBuilder.Entity<ReviewOfSystem>().ToTable(_localizer["ReviewOfSystem"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<ReviewOfSystem>().HasKey(s => new { s.ReviewOfSystemId, s.PatientId }); // Configure composite key
                
                modelBuilder.Entity<Vitals>().ToTable(_localizer["PatientVitals"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Vitals>().HasKey(c => new { c.VitalId, c.PatientId });

                modelBuilder.Entity<PatientReferralOutgoing>().ToTable(_localizer["PatientReferralOutgoing"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<PatientReferralOutgoing>().HasKey(c => new { c.PlanReferralId, c.PatientId });


                modelBuilder.Entity<Procedure>().ToTable(_localizer["Procedure"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Procedure>().HasKey(s => new { s.Id, s.PatientId }); // Configure composite key

                modelBuilder.Entity<CPT>().ToTable(_localizer["CPT"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<CPT>().Property(p => p.CPTCode).HasDefaultValueSql(_localizer["NewIdFunction"]);
                modelBuilder.Entity<CPT>(entity =>
                {
                    entity.HasNoKey();
                });

                modelBuilder.Entity<ObHistory>().ToTable(_localizer["ObHistory"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<ObHistory>().HasKey(s => new { s.obId, s.PatientId });

                modelBuilder.Entity<GynHistory>().ToTable(_localizer["GynHistory"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<GynHistory>().HasKey(s => new { s.gynId, s.PatientId });

                modelBuilder.Entity<Examination>().ToTable(_localizer["Examination"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<Examination>().HasKey(e => e.ExaminationId);
                modelBuilder.Entity<LabTests>().ToTable(_localizer["LabTests"], _localizer["EncounterNotesService"]);
                modelBuilder.Entity<LabTests>().HasKey(s => new { s.PatientId, s.LabTestsId }); // Configure composite key
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DatabaseError"]);
            }
        }
    }
}
