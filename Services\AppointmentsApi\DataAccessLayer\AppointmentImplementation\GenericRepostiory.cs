﻿using AppointmentContracts;
using AppointmentDataAccessLayer.AppointmentContext;
using AppointmentDataAccessLayer.DataAccessLayerResources;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DotNetEnv;
using System.IO.Hashing;
using Azure.Core;

namespace AppointmentDataAccessLayer.AppointmentImplementation
{
    /// <summary>
    /// Generic repository for handling database operations using sharding.
    /// </summary>
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
        private readonly ShardMapManagerService _shardMapManagerService;
        private readonly IStringLocalizer<DataAccessLayerStrings> _localizer;
        private readonly ILogger<AppointmentDatabaseContext> _logger;
        const int two = 2, one = 1, zero = 0;

        /// <summary>
        /// Initializes a new instance of the <see cref="GenericRepository{T}"/> class.
        /// </summary>
        public GenericRepository(ShardMapManagerService shardMapManagerService,
                                 IStringLocalizer<DataAccessLayerStrings> localizer,
                                 ILogger<AppointmentDatabaseContext> logger
                                 )
        {
            Env.Load();
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Retrieves the appropriate database context based on the user ID and subscription status.
        /// </summary>
        public AppointmentDatabaseContext GetNextKey(bool Subscription, Guid userId)
        {
            List<byte> tempguidBytes = userId.ToByteArray().ToList();
            byte[] nextKey;
            if (Subscription == false)
            {
                tempguidBytes.Add(two);
                byte[] guidBytes = tempguidBytes.ToArray();
                var allKeys = _shardMapManagerService.GetAllKeys();
                nextKey = allKeys.Where(key => key != null && key.Length > zero && key[key.Length - one] == two && CompareByteArrays(key, guidBytes) > zero)
                  .OrderBy(key => key, Comparer<byte[]>.Create(CompareByteArrays))
                    .FirstOrDefault();
            }
            else
            {
                tempguidBytes.Add(one);
                nextKey = tempguidBytes.ToArray();
            }
            var context = GetDbContext(nextKey);
            if (context != null)
            {
                return context;
            }
            return null;
        }

        /// <summary>
        /// Gets the database context for the specified shard key.
        /// </summary>
        protected virtual AppointmentDatabaseContext GetDbContext(byte[] nextKey)
        {
            var shard = _shardMapManagerService.GetShardForKey(nextKey);
            if (shard == null)
            {
                return null;
            }
            var connectionString = $"{_localizer["Server"]}={shard.Location.Server};{_localizer["Initial Catalog"]}={shard.Location.Database};{Environment.GetEnvironmentVariable("ConnectionString")}";

            var optionsBuilder = new DbContextOptionsBuilder<AppointmentDatabaseContext>();
            optionsBuilder.UseSqlServer(connectionString);
            var context = new AppointmentDatabaseContext(optionsBuilder.Options, _localizer, _logger);
            return context;
        }

        /// <summary>
        /// Compares two byte arrays.
        /// </summary>
        static int CompareByteArrays(byte[] array1, byte[] array2)
        {
            for (int i = zero; i < Math.Min(array1.Length, array2.Length); i++)
            {
                if (array1[i] < array2[i]) return -one;
                if (array1[i] > array2[i]) return one;
            }
            return array1.Length.CompareTo(array2.Length);
        }

        /// <summary>
        /// Adds a collection of appointments asynchronously.
        /// </summary>
        public async Task AddAsync(IEnumerable<Appointment> entities, Guid OrgId)
        {
            using var context = GetNextKey(entities.First().Subscription, OrgId);
            if (context == null) return;
            await context.Set<Appointment>().AddRangeAsync(entities);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Retrieves all records asynchronously.
        /// </summary>
        public async Task<IEnumerable<T>> GetAllAsync()
        {
            var allEntities = new List<T>();
            var allKeys = _shardMapManagerService.GetAllKeys();
            foreach (var key in allKeys)
            {
                using (var context = GetDbContext(key))
                {
                    var entities = await context.Set<T>().ToListAsync();
                    allEntities.AddRange(entities);
                }
            }
            return allEntities;
        }

        /// <summary>
        /// Retrieves records by a specific date asynchronously.
        /// </summary>
        public async Task<IEnumerable<T>> GetByDateAsync(DateTime date, Guid OrgID, bool Subscription)
        {
            var allEntities = new List<T>();
            using var context = GetNextKey(Subscription, OrgID); //change this
            if (context == null) return null;
            var entities = await context.Set<T>()
               .Where(e => EF.Property<DateTime>(e, _localizer["AppointmentDate"]).Date == date.Date)
               .ToListAsync();
            allEntities.AddRange(entities);


            return allEntities;
        }

        /// <summary>
        /// Retrieves a record by its ID asynchronously.
        /// </summary>
        public async Task<T> GetByIdAsync(bool Subscription, Guid Id, Guid OrgID)
        {
            using var context = GetNextKey(Subscription, OrgID);
            if (context == null) return null;
            return await context.Set<T>().FindAsync(Id);
        }

        /// <summary>
        /// Updates an appointment asynchronously.
        /// </summary>
        public async Task UpdateAsync(Appointment entity, Guid Id)
        {
            using var context = GetNextKey(entity.Subscription, entity.OrganisationId);
            if (context == null) return;
            context.Set<Appointment>().Update(entity);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Deletes a record by ID asynchronously.
        /// </summary>
        public async Task DeleteByIdAsync(bool Subscription, Guid Id, Guid OrgID)
        {
            using var context = GetNextKey(Subscription, OrgID);
            if (context == null) return;
            var entity = await context.Set<T>().FindAsync(Id);
            if (entity == null) return;
            context.Set<T>().Remove(entity);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Retrieves records by user ID asynchronously.
        /// </summary>
        public async Task<IEnumerable<T>> GetByUserIdAsync(Guid userId, Guid OrgID, bool Subscription)
        {
            var allEntities = new List<T>();
            using var context = GetNextKey(Subscription, OrgID); //change this
            if (context == null) return null;
            var entities = await context.Set<T>()
                .Where(e => EF.Property<Guid>(e, _localizer["UserId"]) == userId)
                .ToListAsync();

            allEntities.AddRange(entities);
            return allEntities;
        }

    }
}