﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class PastResultsController : ControllerBase
    {
        private readonly IPastResultsCommandHandler<PastResults> _pastDataHandler;
        private readonly IPastResultsQueryHandler<PastResults> _pastQueryHandler;
        private readonly ILogger<PastResultsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public PastResultsController(
            IPastResultsCommandHandler<PastResults> pastDataHandler,
            IPastResultsQueryHandler<PastResults> pastQueryHandler,
            ILogger<PastResultsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _pastDataHandler = pastDataHandler;
            _pastQueryHandler = pastQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        
        [HttpGet("{id:guid}")]
        public async Task<ActionResult<IEnumerable<PastResults>>> GetAllById(Guid id)
        {
            ActionResult result;
            try
            {
                var data = await _pastQueryHandler.GetAllResultsById(id);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("{id:guid}/IsActive")]
        public async Task<ActionResult<IEnumerable<PastResults>>> GetAllByIdAndIsActive(Guid id)
        {
            ActionResult result;
            try
            {
                var surgery = await _pastQueryHandler.GetResultByIdAndIsActive(id);
                result = surgery != null ? Ok(surgery) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpPost]
        [Route("AddResult")]
        public async Task<IActionResult> Registration([FromBody] List<PastResults> results)
        {
            if (results == null || results.Count == 0)
            {
                return Ok(_localizer["NoLicense"]);
            }

            try
            {
                await _pastDataHandler.AddResult(results);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        
        [HttpDelete]
        public async Task<IActionResult> DeleteByEntity([FromBody] PastResults past)
        {
            IActionResult result;
            {
                if (past == null)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _pastDataHandler.DeleteResultsByEntity(past);
                        result = Ok(_localizer["DeleteSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["DeleteLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                    }
                }
                return result;
            }
        }

        
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateResultById(Guid id, [FromBody] PastResults PastRes)
        {
            IActionResult result;
            {
                if (PastRes == null || PastRes.PatientId != id)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _pastDataHandler.UpdateResult(PastRes);
                        result = Ok(_localizer["UpdateSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["UpdateLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                    }
                }
                return result;
            }
        }

        
        [HttpPut]
        [Route("UpdatePastList")]
        public async Task<IActionResult> UpdatePastList([FromBody] List<PastResults> pastResult)
        {
            IActionResult result;
            try
            {
                await _pastDataHandler.UpdatePastList(pastResult);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }
    }
}