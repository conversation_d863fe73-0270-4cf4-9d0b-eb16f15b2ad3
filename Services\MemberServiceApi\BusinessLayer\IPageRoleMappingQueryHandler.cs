using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IPageRoleMappingQueryHandler<T>
    {
        Task<T> GetPageRoleMappingByIdAsync(Guid id);
        Task<List<T>> GetPageRoleMappingsByPagePathAsync(string pagePath);
        Task<List<T>> GetAllPageRoleMappingsAsync();
        Task<List<T>> GetPagesByRoleIdAsync(Guid roleId);
        Task<List<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId);
    }
}
