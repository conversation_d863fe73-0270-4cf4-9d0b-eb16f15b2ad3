﻿CREATE TABLE [Practice].[Tasks] (
    [Id]              UNIQUEIDENTIFIER DEFAULT (newid()) NOT NULL,
    [PatientName]     NVARCHAR (255)   NULL,
    [TaskType]        NVARCHAR (255)   NULL,
    [Subject]         NVARCHAR (255)   NULL,
    [AssignedTo]      NVARCHAR (255)   NULL,
    [Status]          NVARCHAR (100)   NULL,
    [CreationDate]    DATETIME         DEFAULT (getdate()) NULL,
    [StartDate]       DATETIME         NULL,
    [DueDate]         DATETIME         NULL,
    [CreatedBy]       NVARCHAR (255)   NULL,
    [Priority]        NVARCHAR (50)    NULL,
    [Notes]           NVARCHAR (MAX)   NULL,
    [RecurringAction] BIT              DEFAULT ((0)) NULL,
    [LastDueDate]     DATETIME         NULL,
    [LastVisitDate]   DATETIME         NULL,
    [Frequency]       INT              DEFAULT ((0)) NULL,
    [SSN]             NVARCHAR (100)   NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC)
);

