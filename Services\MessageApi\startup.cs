using DataAccessLayer.Context;
using MessageDataAccessLayer.Implementation;
using MessageBusinessLayer;
using MessageBusinessLayer.CommandHandler;
using MessageBusinessLayer.QueryHandler;
using MessageContracts;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;

namespace MessageApi
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            DotNetEnv.Env.Load();
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();

            // Add SignalR for real-time messaging
            services.AddSignalR();

            // Swagger configuration
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Swagger Azure AD for Message API", Version = "v1" });
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Description = "Oauth2.0 which uses AuthorizationCode flow",
                    Name = "oauth2.0",
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__AuthorizationUrl")),
                            TokenUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__TokenUrl")),
                            Scopes = new Dictionary<string, string>
                            {
                                { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope"), "Access API as User" }
                            }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                        new[] { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope") }
                    }
                });
            });
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAllOrigins",
                    builder =>
                    {
                        builder.AllowAnyOrigin()
                               .AllowAnyMethod()
                               .AllowAnyHeader();
                    });
            });
            // Localization configuration
            services.AddLocalization();
            // Authentication configuration
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = Environment.GetEnvironmentVariable("AzureAd__Authority");
                    options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidIssuer = Environment.GetEnvironmentVariable("AzureAd__Authority"),
                        ValidAudience = Environment.GetEnvironmentVariable("AzureAd__ClientId")
                    };
                });
            // Dependency Injection configuration
            services.AddDbContext<MessageApiDatabaseContext>(static options =>
                options.UseSqlServer(Environment.GetEnvironmentVariable("DatabaseConnectionString"))
                       .EnableSensitiveDataLogging()
                       .LogTo(Console.WriteLine, LogLevel.Information));

            // Register repositories and unit of work
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IMessageRepository, MessageRepository>();
            services.AddScoped<IAttachmentRepository, AttachmentRepository>();
            services.AddScoped<IUserRepository, UserRepository>();

            // Register business layer services
            services.AddScoped<IMessageCommandHandler<Message>, MessageCommandHandler>();
            services.AddScoped<IMessageQueryHandler<Message>, MessageQueryHandler>();
            services.AddScoped<IAttachmentService, AttachmentService>();
            services.AddScoped<IAcsUserManagementService, AcsUserManagementService>();

            // Register Azure services
            services.AddScoped<IAzureCommunicationService, AzureCommunicationServiceReal>();

            // Register real-time notification service
            services.AddScoped<MessageBusinessLayer.Services.IMessageNotificationService, MessageBusinessLayer.Services.MessageNotificationService>();

            // Register Azure Blob Storage Service (Azure Blob Storage only)
            services.AddScoped<IAzureBlobStorageService, AzureBlobStorageService>();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment() || env.IsProduction())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.OAuthClientId(Environment.GetEnvironmentVariable("SwaggerAzureAd__ClientId"));
                    c.OAuthUsePkce();
                    c.OAuthScopeSeparator(" ");
                    string swaggerJsonBasePath = string.IsNullOrWhiteSpace(c.RoutePrefix) ? "." : "..";
                    c.SwaggerEndpoint($"{swaggerJsonBasePath}/swagger/v1/swagger.json", "MessageApi v1");
                });
            }
            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseCors("AllowAllOrigins");
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHub<MessageApi.Hubs.MessageHub>("/messageHub");
            });
        }
    }
}
