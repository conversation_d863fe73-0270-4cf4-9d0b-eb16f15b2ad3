﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Clients", "Clients", "{7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{F096E0E2-AE29-4E42-AF4F-2576B447238E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{946C0BBB-B53A-49AC-916C-26CCD34D6ABA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Database", "Database", "{693BA719-2BF2-43CB-823E-DA36820ABA38}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaWebApp", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp\TeyaWebApp.csproj", "{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaWebApp.Client", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp.Client\TeyaWebApp.Client.csproj", "{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TestCaseNunit", "Clients\TeyaWebClient\TestCaseNunit\TestCaseNunit.csproj", "{7A5BFFA9-D0B4-4A1D-8979-D1FA46FD1209}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaAIScribeTesting", "Clients\TeyaWebClient\TeyaAIScribeTesting\TeyaAIScribeTesting.csproj", "{166998B5-250F-4C7D-8014-A80F295A7EC4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Appointments", "Services\AppointmentsApi\Appointments.csproj", "{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesService", "Services\EncounterNotesApi\EncounterNotesService.csproj", "{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MemberServiceApi", "Services\MemberServiceApi\MemberServiceApi.csproj", "{AFAC4305-9F1E-4165-9458-01D819B1F87C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeApi", "Services\PracticeApi\PracticeApi.csproj", "{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Appointmentsapi.Test", "Services\AppointmentsApi\Appointmentsapi.Test\Appointmentsapi.Test.csproj", "{599339E0-716E-4775-8396-26900E90D04F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesService.Tests", "Services\EncounterNotesApi\EncounterNotesService.Tests\EncounterNotesService.Tests.csproj", "{E3D61CBB-72F0-45BE-BC2F-ACE2918A0082}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MemberServiceApi-Test", "Services\MemberServiceApi\MemberServiceApi-Test\MemberServiceApi-Test.csproj", "{157ED04E-EACD-460D-A314-1584D43177E3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentsBusinessLayer", "Services\AppointmentsApi\BusinessLayer\AppointmentsBusinessLayer.csproj", "{B2D583D0-BF92-41F5-8D6B-560B7D605749}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentContracts", "Services\AppointmentsApi\Contracts\AppointmentContracts.csproj", "{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentDataAccessLayer", "Services\AppointmentsApi\DataAccessLayer\AppointmentDataAccessLayer.csproj", "{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesBusinessLayer", "Services\EncounterNotesApi\BusinessLayer\EncounterNotesBusinessLayer.csproj", "{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesDataAccessLayer", "Services\EncounterNotesApi\DataAccessLayer\EncounterNotesDataAccessLayer.csproj", "{0970DD64-4320-4510-87B9-B969AA656C0A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AppointmentService", "AppointmentService", "{FBA38A5A-CFC9-466E-9C30-F48BE88F8678}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{15E2B3C7-C7F5-4997-BAC4-943A51C74259}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "EncounterService", "EncounterService", "{D071310F-44FB-43B1-A90F-8EAF203B5F73}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{99F3ED42-8796-4D74-9F43-5FD3B6C3259A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MemberService", "MemberService", "{FF69A0CE-FD05-4BCF-AF49-530A996AD153}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{51BCA120-1F40-427C-AAC9-060C0D0A1EF5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MemberServiceBusinessLayer", "Services\MemberServiceApi\BusinessLayer\MemberServiceBusinessLayer.csproj", "{0DEE2DFA-058B-4649-837A-9FEC470E8044}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MemberServiceDataAccessLayer", "Services\MemberServiceApi\DataAccessLayer\MemberServiceDataAccessLayer.csproj", "{8114711B-7A07-48B3-B337-52014F1CA5FE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PracticeService", "PracticeService", "{2B119102-3734-4406-A301-4778546F17A8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeBusinessLayer", "Services\PracticeApi\PracticeBusinessLayer\PracticeBusinessLayer.csproj", "{054E81E1-5ABE-4081-A14A-102CD789AF5D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeDataAccessLayer", "Services\PracticeApi\PracticeDataAccessLayer\PracticeDataAccessLayer.csproj", "{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeContracts", "Services\PracticeApi\PracticeContracts\PracticeContracts.csproj", "{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Contracts", "Services\MemberServiceApi\Contracts\Contracts.csproj", "{05D62A1B-05F9-4A44-A6A0-543893220654}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesContracts", "Services\EncounterNotesApi\Contracts\EncounterNotesContracts.csproj", "{8598DB8F-A790-4FD8-A646-400BA18FFAB9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaUIViewModels", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp\TeyaUIViewModels\TeyaUIViewModels.csproj", "{F961FC2C-6861-419B-BBF9-28B0042B97F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaUIModels", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp\TeyaUIModels\TeyaUIModels.csproj", "{5747E0B4-87EE-4ED7-85DD-372412EB787F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaWebApp.Tests", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp.Tests\TeyaWebApp.Tests.csproj", "{E8064C21-0321-4AA6-BB76-D96410F55994}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "TeyaHealthSQLDatabase", "Database\TeyaHealthSQLDatabase\TeyaHealthSQLDatabase.sqlproj", "{0D466A75-5329-4633-A5FB-00360A417804}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MessageApi", "Services\MessageApi\MessageApi.csproj", "{59214B9E-95F6-4937-89AB-B079D69F1DED}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MessageContracts", "Services\MessageApi\Contracts\MessageContracts.csproj", "{8E4422CB-A5C8-4DC2-9454-2FDB6CA1570B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MessageDataAccessLayer", "Services\MessageApi\MessageDataAccessLayer\MessageDataAccessLayer.csproj", "{D5F099A1-2A80-46E1-BF3D-C8F8F2A9B71D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MessageBusinessLayer", "Services\MessageApi\MessageBusinessLayer\MessageBusinessLayer.csproj", "{DABD1312-8B67-4BAA-9927-491C45801F48}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MessageApi.Tests", "Services\MessageApi\MessageApi.Tests\MessageApi.Tests.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MessageService", "MessageService", "{B1C2D3E4-F5A6-7890-BCDE-F12345678901}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{C2D3E4F5-A6B7-8901-CDEF-123456789012}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A5BFFA9-D0B4-4A1D-8979-D1FA46FD1209}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A5BFFA9-D0B4-4A1D-8979-D1FA46FD1209}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A5BFFA9-D0B4-4A1D-8979-D1FA46FD1209}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A5BFFA9-D0B4-4A1D-8979-D1FA46FD1209}.Release|Any CPU.Build.0 = Release|Any CPU
		{166998B5-250F-4C7D-8014-A80F295A7EC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{166998B5-250F-4C7D-8014-A80F295A7EC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{166998B5-250F-4C7D-8014-A80F295A7EC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{166998B5-250F-4C7D-8014-A80F295A7EC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Release|Any CPU.Build.0 = Release|Any CPU
		{599339E0-716E-4775-8396-26900E90D04F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{599339E0-716E-4775-8396-26900E90D04F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{599339E0-716E-4775-8396-26900E90D04F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{599339E0-716E-4775-8396-26900E90D04F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3D61CBB-72F0-45BE-BC2F-ACE2918A0082}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3D61CBB-72F0-45BE-BC2F-ACE2918A0082}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3D61CBB-72F0-45BE-BC2F-ACE2918A0082}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3D61CBB-72F0-45BE-BC2F-ACE2918A0082}.Release|Any CPU.Build.0 = Release|Any CPU
		{157ED04E-EACD-460D-A314-1584D43177E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{157ED04E-EACD-460D-A314-1584D43177E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{157ED04E-EACD-460D-A314-1584D43177E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{157ED04E-EACD-460D-A314-1584D43177E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Release|Any CPU.Build.0 = Release|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Release|Any CPU.Build.0 = Release|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Release|Any CPU.Build.0 = Release|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Release|Any CPU.Build.0 = Release|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Release|Any CPU.Build.0 = Release|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E8064C21-0321-4AA6-BB76-D96410F55994}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E8064C21-0321-4AA6-BB76-D96410F55994}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E8064C21-0321-4AA6-BB76-D96410F55994}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E8064C21-0321-4AA6-BB76-D96410F55994}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Release|Any CPU.Build.0 = Release|Any CPU
		{59214B9E-95F6-4937-89AB-B079D69F1DED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59214B9E-95F6-4937-89AB-B079D69F1DED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59214B9E-95F6-4937-89AB-B079D69F1DED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59214B9E-95F6-4937-89AB-B079D69F1DED}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E4422CB-A5C8-4DC2-9454-2FDB6CA1570B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E4422CB-A5C8-4DC2-9454-2FDB6CA1570B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E4422CB-A5C8-4DC2-9454-2FDB6CA1570B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E4422CB-A5C8-4DC2-9454-2FDB6CA1570B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5F099A1-2A80-46E1-BF3D-C8F8F2A9B71D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5F099A1-2A80-46E1-BF3D-C8F8F2A9B71D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5F099A1-2A80-46E1-BF3D-C8F8F2A9B71D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5F099A1-2A80-46E1-BF3D-C8F8F2A9B71D}.Release|Any CPU.Build.0 = Release|Any CPU
		{DABD1312-8B67-4BAA-9927-491C45801F48}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DABD1312-8B67-4BAA-9927-491C45801F48}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DABD1312-8B67-4BAA-9927-491C45801F48}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DABD1312-8B67-4BAA-9927-491C45801F48}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{7A5BFFA9-D0B4-4A1D-8979-D1FA46FD1209} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{166998B5-250F-4C7D-8014-A80F295A7EC4} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{AFAC4305-9F1E-4165-9458-01D819B1F87C} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A} = {2B119102-3734-4406-A301-4778546F17A8}
		{599339E0-716E-4775-8396-26900E90D04F} = {15E2B3C7-C7F5-4997-BAC4-943A51C74259}
		{E3D61CBB-72F0-45BE-BC2F-ACE2918A0082} = {99F3ED42-8796-4D74-9F43-5FD3B6C3259A}
		{157ED04E-EACD-460D-A314-1584D43177E3} = {51BCA120-1F40-427C-AAC9-060C0D0A1EF5}
		{B2D583D0-BF92-41F5-8D6B-560B7D605749} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{0970DD64-4320-4510-87B9-B969AA656C0A} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{FBA38A5A-CFC9-466E-9C30-F48BE88F8678} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{15E2B3C7-C7F5-4997-BAC4-943A51C74259} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{D071310F-44FB-43B1-A90F-8EAF203B5F73} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{99F3ED42-8796-4D74-9F43-5FD3B6C3259A} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{FF69A0CE-FD05-4BCF-AF49-530A996AD153} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{51BCA120-1F40-427C-AAC9-060C0D0A1EF5} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{0DEE2DFA-058B-4649-837A-9FEC470E8044} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{8114711B-7A07-48B3-B337-52014F1CA5FE} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{2B119102-3734-4406-A301-4778546F17A8} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{054E81E1-5ABE-4081-A14A-102CD789AF5D} = {2B119102-3734-4406-A301-4778546F17A8}
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77} = {2B119102-3734-4406-A301-4778546F17A8}
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6} = {2B119102-3734-4406-A301-4778546F17A8}
		{05D62A1B-05F9-4A44-A6A0-543893220654} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{F961FC2C-6861-419B-BBF9-28B0042B97F2} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{5747E0B4-87EE-4ED7-85DD-372412EB787F} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{0D466A75-5329-4633-A5FB-00360A417804} = {693BA719-2BF2-43CB-823E-DA36820ABA38}
		{59214B9E-95F6-4937-89AB-B079D69F1DED} = {B1C2D3E4-F5A6-7890-BCDE-F12345678901}
		{8E4422CB-A5C8-4DC2-9454-2FDB6CA1570B} = {B1C2D3E4-F5A6-7890-BCDE-F12345678901}
		{D5F099A1-2A80-46E1-BF3D-C8F8F2A9B71D} = {B1C2D3E4-F5A6-7890-BCDE-F12345678901}
		{DABD1312-8B67-4BAA-9927-491C45801F48} = {B1C2D3E4-F5A6-7890-BCDE-F12345678901}
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {C2D3E4F5-A6B7-8901-CDEF-123456789012}
		{B1C2D3E4-F5A6-7890-BCDE-F12345678901} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{C2D3E4F5-A6B7-8901-CDEF-123456789012} = {B1C2D3E4-F5A6-7890-BCDE-F12345678901}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6DC8759E-4E3D-4CDD-A6F8-9B93A71EA423}
	EndGlobalSection
EndGlobal
