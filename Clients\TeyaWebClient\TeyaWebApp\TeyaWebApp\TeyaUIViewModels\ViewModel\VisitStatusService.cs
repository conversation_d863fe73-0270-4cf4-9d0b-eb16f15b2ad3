﻿using Azure;
using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModels
{
    /// <summary>
    /// Service to manage visit status operations.
    /// </summary>
    public class VisitStatusService : IVisitStatusService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<VisitStatusService> _localizer;
        private readonly ILogger<VisitStatusService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="VisitStatusService"/> class.
        /// </summary>
        /// <param name="httpClient">The HTTP client to make API requests.</param>
        /// <param name="localizer">Localizer for handling localized strings.</param>
        /// <param name="logger">Logger for logging errors and information.</param>
        /// <param name="tokenService">Service for managing authentication tokens.</param>
        public VisitStatusService(HttpClient httpClient, IStringLocalizer<VisitStatusService> localizer, ILogger<VisitStatusService> logger, ITokenService tokenService)
        {
            _tokenService = tokenService;
            Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        /// <summary>
        /// Retrieves a list of visit status names.
        /// </summary>
        /// <returns>A list of visit status names as strings.</returns>
        public async Task<List<string>> GetVisitStatus_StatusAsync()
        {
            try
            {
                var allVisitStatus = await GetAllVisitStatusAsync();
                return allVisitStatus?.Select(visitStatus => visitStatus.Visitstatus).ToList() ?? new List<string>();
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, _localizer["An error occurred while fetching visit Status names."]);
                throw;
            }
        }

        /// <summary>
        /// Fetches all visit statuses from the external API.
        /// </summary>
        /// <returns>A list of <see cref="VisitStatus"/> objects.</returns>
        /// <exception cref="UnauthorizedAccessException">Thrown when the access token is missing.</exception>
        public async Task<List<VisitStatus>> GetAllVisitStatusAsync()
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }
                var apiUrl = $"{_MemberService}/api/VisitStatus";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<VisitStatus>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingVisitStatus"]);
                throw;
            }
        }
    }
}
