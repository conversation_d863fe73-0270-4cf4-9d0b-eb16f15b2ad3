﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class ChiefComplaintCommandHandler : IChiefComplaintCommandHandler<ChiefComplaint>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ChiefComplaintCommandHandler> _logger;

        public ChiefComplaintCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<ChiefComplaintCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }      

        public async Task AddChiefComplaint(IEnumerable<ChiefComplaint> complaints)
        {
            await _unitOfWork.ChiefComplaintRepository.AddAsync(complaints);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateChiefComplaint(ChiefComplaint complaint)
        {
            await _unitOfWork.ChiefComplaintRepository.UpdateAsync(complaint);
            await _unitOfWork.SaveAsync();
        }
        public async Task DeleteChiefComplaintById(Guid id)
        {
            await _unitOfWork.ChiefComplaintRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
        public async Task DeleteChiefComplaintByEntity(ChiefComplaint complaint)
        {
            await _unitOfWork.ChiefComplaintRepository.DeleteByEntityAsync(complaint);
            await _unitOfWork.SaveAsync();
        }
        public async Task UpdateChiefComplaintListAsync(IEnumerable<ChiefComplaint> complaints)
        {
            foreach (var complaint in complaints)
            {
                await _unitOfWork.ChiefComplaintRepository.UpdateAsync(complaint);
            }
            await _unitOfWork.SaveAsync();
        }

    }
}
