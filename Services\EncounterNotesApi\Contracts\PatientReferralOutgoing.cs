﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class PatientReferralOutgoing : IContract
    {
        public Guid PlanReferralId { get; set; }
        public Guid PatientId { get; set; }
        public Guid? OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? TreatmentPlan { get; set; }
        public string? ReferralFrom { get; set; }
        public string? Tests { get; set; }
        public string? ReferralTo { get; set; }
        public string? ReferralReason { get; set; }
        public bool isActive { get; set; }

    }
}
