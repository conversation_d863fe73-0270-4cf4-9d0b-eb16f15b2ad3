﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class MedicalHistoryDTO
    {
        public Guid MedicalHistoryID { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? History { get; set; }
        public Guid PCPId { get; set; }

        public bool IsActive { get; set; }
    }
}