﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class RolesRepository : GenericRepository<Role>, IRolesRepository
    {
        private readonly AccountDatabaseContext _context;

        public RolesRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }
        public async Task<List<Role>> GetRolesByNameAsync(string name)
        {
            var result = await _context.Role
                .Where(role => role.RoleName.Contains(name, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }

    }
}
