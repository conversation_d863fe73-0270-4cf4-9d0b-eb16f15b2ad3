﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class AllergyRepository : GenericRepository<Allergy>, IAllergyRepository
    {
        private readonly RecordDatabaseContext _context;

        public AllergyRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public void Add(Allergy allergy)
        {
            _context.Allergies.Add(allergy);
        }

        public async Task<Allergy> GetByIdAsync(Guid patientId, Guid medicineId)
        {
            return await _context.Allergies
                .FirstOrDefaultAsync(a => a.PatientId == patientId && a.MedicineId == medicineId);
        }

        public void Update(Allergy allergy)
        {
            _context.Allergies.Update(allergy);
        }
        public async Task<IEnumerable<Allergy>> GetAllAllergyAsync()
        {
            return await _context.Allergies
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task UpdateRangeAsync(List<Allergy> allergies)
        {
            _context.Allergies.UpdateRange(allergies);
            await _context.SaveChangesAsync();
        }
    }
}