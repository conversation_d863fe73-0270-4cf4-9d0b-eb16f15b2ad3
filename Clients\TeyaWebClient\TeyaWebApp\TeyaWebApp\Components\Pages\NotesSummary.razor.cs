﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;

namespace TeyaWebApp.Components.Pages
{
    public partial class NotesSummary : ComponentBase
    {
        private List<FamilyMember> familyMembers { get; set; }
        private List<PatientVitals> patientVitals { get; set; }
        private PatientVitals MostRecentVitals { get; set; }
        private List<BPVitals> formattedVitals { get; set; }
        private List<Allergy> allergies { get; set; }
        private List<MedicalHistoryDTO> medicalHistory { get; set; }
        private List<ActiveMedication> activeMedications { get; set; }

        [Inject] private PatientService _PatientService { get; set; }
        public string fontFamily { get; set; } = "font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif";
        private Guid PatientID { get; set; }

        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            patientVitals = (await VitalService.GetVitalsByIdAsyncAndIsActive(PatientID))
                             .OrderBy(v => v.CreatedDate)
                             .ToList();
            MostRecentVitals = patientVitals
                              ?.OrderByDescending(v => v.CreatedDate).FirstOrDefault();

            familyMembers = (await FamilyMemberService.GetFamilyMemberByIdAsyncAndIsActive(PatientID))
                            .OrderByDescending(fm => fm.CreatedDate) 
                            .Take(5) 
                            .ToList();
            allergies = (await AllergyService.GetAllergyByIdAsyncAndIsActive(PatientID))
                        .OrderByDescending(fm => fm.CreatedOn)
                            .Take(5)
                            .ToList();
            medicalHistory = (await MedicalHistoryService.GetAllByIdAndIsActiveAsync(PatientID))
                              .OrderByDescending(fm => fm.CreatedDate)
                               .Take(5)
                               .ToList();
            activeMedications = (await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID))
                                .OrderByDescending(fm => fm.CreatedDate)
                                .Take(5)
                                .ToList();
            formattedVitals = patientVitals
                            .Where(v => !string.IsNullOrEmpty(v.BP))
                            .Select(v => {
                                var bpParts = v.BP.Split('/');
                                return new BPVitals
                                {
                                    CreatedDate = v.CreatedDate,
                                    Systolic = int.TryParse(bpParts[0], out int sys) ? sys : 0,
                                    Diastolic = int.TryParse(bpParts[1], out int dia) ? dia : 0
                                };
                            })
                            .ToList();


        }

        /// <summary>
        /// Model to show Patient BP Information in graph
        /// </summary>
        private class BPVitals
        {
            public DateTime CreatedDate { get; set; }
            public int Systolic { get; set; }
            public int Diastolic { get; set; }
        }
    }
}
