﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IDiagnosticImagingQueryHandler<T>
    {
        Task<IEnumerable<DiagnosticImage>> GetDiagnosticImagingByIdAndIsActive(Guid id);
        Task<List<T>> GetDiagnosticImagingById(Guid id);
    }
}
