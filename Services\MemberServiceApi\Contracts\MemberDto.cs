﻿using Contracts;
using System;
using System.Text.Json.Serialization;

public class MemberDto : IContract
{
    public Guid Id { get; set; }

    public string? UserName { get; set; }
    public string? Email { get; set; }
    public bool? IsActive { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? PhoneNumber { get; set; }

    public DateTime? DateOfBirth { get; set; }
    public string? Country { get; set; }
    public string? OrganizationName { get; set; } 
    public string? RoleName { get; set; } 
    public string? PreferredName { get; set; }

    public string? ExternalID { get; set; }

    public string? MaritalStatus { get; set; }

    public string? SexualOrientation { get; set; }
    public string? PreviousNames { get; set; }
    public string? Language { get; set; }
    public string? Ethnicity { get; set; }
    public string? Race { get; set; }
    public string? Nationality { get; set; }
    public int? FamilySize { get; set; }
    public DateTime? FinancialReviewDate { get; set; }
    public decimal? MonthlyIncome { get; set; }

    public string? ReferralSource { get; set; }
    public string? Religion { get; set; }



    public DateTime? DateDeceased { get; set; }
    public string? ReasonDeceased { get; set; }

    public string? MiddleName { get; set; }
    public string? Suffix { get; set; }
    public string? FederalTaxId { get; set; }
    public string? DEANumber { get; set; }
    public string? UPIN { get; set; }
    public string? NPI { get; set; }
    public string? ProviderType { get; set; }
    public string? MainMenuRole { get; set; }
    public string? PatientMenuRole { get; set; }
    public string? Supervisor { get; set; }
    public string? JobDescription { get; set; }
    public string? Taxonomy { get; set; }
    public string? NewCropERxRole { get; set; }
    public string? AccessControl { get; set; }
    public string? AdditionalInfo { get; set; }
    public string? DefaultBillingFacility { get; set; }
    public Guid? RoleID { get; set; }
    public Guid? OrganizationID { get; set; }
    public string? PCPName { get; set; }
    public Guid? PCPId { get; set; }

    public Guid? AddressId { get; set; }
    public Guid? InsuranceId { get; set; }
    public Guid? GuardianId { get; set; }
    public Guid? EmployerId { get; set; }
    public string? SSN { get; set; }
    public string? ProfileImageUrl { get; set; }

}
