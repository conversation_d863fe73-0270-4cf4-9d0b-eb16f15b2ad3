﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITemplatesCommandHandler<TText>
    {
        Task DeleteTemplatesByEntity(Templates templates);
        Task DeleteTemplatesById(Guid id);
        Task AddTemplates(List<TText> templates);
        Task UpdateTemplates(List<Templates> templates);
    }
}