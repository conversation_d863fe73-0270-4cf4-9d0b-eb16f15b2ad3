﻿using Microsoft.Identity.Client;
using System.Xml;

namespace TeyaAiScribeMobile
{
    public partial class SignIn : ContentPage
    {
       

        public SignIn()
        {
            InitializeComponent();
            Shell.Current.FlyoutBehavior = FlyoutBehavior.Disabled;
        }

        //private async void OnLoginClicked(object sender, EventArgs e)
        //{
        //    var publicClientApplicationBuilder = PublicClientApplicationBuilder
        //        .Create(EntraConfig.ClientId)
        //        .WithAuthority(EntraConfig.Authority)
        //        .WithIosKeychainSecurityGroup(EntraConfig.IOSKeychainSecurityGroup)
        //        .WithRedirectUri($"msal{EntraConfig.ClientId}://auth")
        //        .Build();
        //    try
        //    {
        //        var accounts = await publicClientApplicationBuilder.GetAccountsAsync();
        //        if (accounts.Any())
        //        {
        //            await publicClientApplicationBuilder.AcquireTokenSilent(EntraConfig.Scopes, accounts.First())
        //           .ExecuteAsync();
        //        }
        //        else
        //        {
        //            var authResult = await publicClientApplicationBuilder.AcquireTokenInteractive(EntraConfig.Scopes)
        //           .WithParentActivityOrWindow(EntraConfig.ParentWindow)
        //           .ExecuteAsync().ConfigureAwait(false);
        //        }

        //    }
        //    catch (MsalException ex)
        //    {
        //        // Interactive mode
        //        var authResult = await publicClientApplicationBuilder.AcquireTokenInteractive(EntraConfig.Scopes)
        //            .WithParentActivityOrWindow(EntraConfig.ParentWindow)
        //            .ExecuteAsync().ConfigureAwait(false);
        //    }



        //    NameLabel.Text = "Login succesful";
        //}


        private async void OnLoginClicked(object sender, EventArgs e)
        {
            var publicClientApplication = PublicClientApplicationBuilder
                .Create(EntraConfig.ClientId)
                .WithAuthority(EntraConfig.Authority)
                .WithIosKeychainSecurityGroup(EntraConfig.IOSKeychainSecurityGroup)
                .WithRedirectUri($"msal{EntraConfig.ClientId}://auth")
                .Build();

            try
            {
                // Try silent login
                var accounts = await publicClientApplication.GetAccountsAsync();
                var authResult = await publicClientApplication.AcquireTokenSilent(EntraConfig.Scopes, accounts.FirstOrDefault())
                    .ExecuteAsync();

                NameLabel.Text = $"Welcome, {authResult.Account.Username}";
                // Redirect to Main Page after successful login
                await Shell.Current.GoToAsync("//MainPage");
            }
            catch (MsalUiRequiredException)
            {
                // Silent login failed, fall back to interactive
                try
                {
                    var authResult = await publicClientApplication.AcquireTokenInteractive(EntraConfig.Scopes)
                        .WithParentActivityOrWindow(EntraConfig.ParentWindow)
                        .ExecuteAsync();

                    NameLabel.Text = $"Welcome, {authResult.Account.Username}";
                }
                catch (Exception ex)
                {
                    NameLabel.Text = $"Login failed: {ex.Message}";
                }
            }
            catch (Exception ex)
            {
                NameLabel.Text = $"Error: {ex.Message}";
            }
        }

        private async void OnLogoutClicked(object sender, EventArgs e)
        {
            var publicClientApplication = PublicClientApplicationBuilder
                .Create(EntraConfig.ClientId)
                .WithAuthority(EntraConfig.Authority)
                .WithIosKeychainSecurityGroup(EntraConfig.IOSKeychainSecurityGroup)
                .WithRedirectUri($"msal{EntraConfig.ClientId}://auth")
                .Build();

            try
            {
                // Remove all accounts from MSAL cache
                var accounts = await publicClientApplication.GetAccountsAsync();
                foreach (var account in accounts)
                {
                    await publicClientApplication.RemoveAsync(account);
                }

                NameLabel.Text = "You have been logged out.";
            }
            catch (Exception ex)
            {
                NameLabel.Text = $"Logout failed: {ex.Message}";
            }
        }

    }

}
