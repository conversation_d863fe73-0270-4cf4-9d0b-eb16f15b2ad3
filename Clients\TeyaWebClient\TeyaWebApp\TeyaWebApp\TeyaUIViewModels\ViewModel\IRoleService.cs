﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace BusinessLayer.Services
{
    public interface IRoleService
    {
        Task<Role> RegisterRoleAsync(Role role);
        Task<Role> GetRoleByIdAsync(Guid roleId);
        Task<List<Role>> GetAllRolesAsync();
        Task DeleteRoleByIdAsync(Guid roleId);
        Task UpdateRoleByIdAsync(Guid roleId, Role role);
        Task<List<Role>> GetRolesByNameAsync(string name);
        Task<List<Role>> GetAllRolesByOrgIdAsync(Guid? ID);
    }
}
