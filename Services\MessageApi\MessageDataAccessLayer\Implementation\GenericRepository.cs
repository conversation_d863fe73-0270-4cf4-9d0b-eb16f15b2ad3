﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;

namespace MessageDataAccessLayer.Implementation
{
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
        protected readonly MessageApiDatabaseContext _context;
        private readonly DbSet<T> _dbSet;

        public GenericRepository(MessageApiDatabaseContext context)
        {
            _context = context;
            _dbSet = context.Set<T>();
        }

        public async Task AddAsync(IEnumerable<T> entities)
        {
            try
            {
                await _dbSet.AddRangeAsync(entities);
            }
            catch (Exception ex)
            {
                // Log the error and re-throw instead of swallowing it
                var errorMessage = ex.InnerException?.Message ?? ex.Message;
                throw new InvalidOperationException($"Error adding entities to database: {errorMessage}", ex);
            }
        }

        public async Task<IEnumerable<T>> GetAllAsync()
        {
            return await _dbSet.ToListAsync();
        }

        public async Task<IEnumerable<T>> GetAsync()
        {
            return await _dbSet.ToListAsync();
        }

        public async Task<IEnumerable<T>> GetAsync(bool includeDeleted)
        {
            if (includeDeleted)
            {
                return await _dbSet.IgnoreQueryFilters().ToListAsync();
            }
            return await _dbSet.ToListAsync();
        }

        public async Task<T> GetByIdAsync(Guid id)
        {
            return await _dbSet.FindAsync(id);
        }

        public async Task<T> GetByIdAsync(Guid id, bool includeDeleted)
        {
            if (includeDeleted)
            {
                return await _dbSet.IgnoreQueryFilters().FirstOrDefaultAsync(e => EF.Property<Guid>(e, "MessageId") == id);
            }
            return await _dbSet.FindAsync(id);
        }

        // Update
        public async Task UpdateAsync(T entity)
        {
            _dbSet.Update(entity);
            await _context.SaveChangesAsync();
        }


        // Delete by entity
        public async Task DeleteByEntityAsync(T entity)
        {
            _dbSet.Remove(entity);
            await _context.SaveChangesAsync();
        }

        // Delete by id
        public async Task DeleteByIdAsync(Guid id)
        {
            var entity = await GetByIdAsync(id);
            if (entity != null)
            {
                await DeleteByEntityAsync(entity);
            }
        }

        public async Task UpdateRangeAsync(IEnumerable<T> entities)
        {
            _context.Set<T>().UpdateRange(entities);
            await Task.CompletedTask;
        }

    }
}
