﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IPrescriptionMedicationQueryHandler<TText>
    {
        Task<IEnumerable<PrescriptionMedication>> GetMedicationByIdAndIsActive(Guid id);
        Task<IEnumerable<PrescriptionMedication>> GetAllMedicationsbyId(Guid id);
    }
}
