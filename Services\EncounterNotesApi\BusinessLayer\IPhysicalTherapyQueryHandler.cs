﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IPhysicalTherapyQueryHandler<TText>
    {
        Task<IEnumerable<PhysicalTherapyData>> GetPhysicalTherapyByIdAndIsActive(Guid id);
        Task<IEnumerable<PhysicalTherapyData>> GetAllPhysicalTherapyById(Guid id);
    }
}
