﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class ExaminationService : IExaminationService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _examinationUrl;
        private readonly ITokenService _tokenService;

        public ExaminationService(HttpClient httpClient, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokens)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _examinationUrl = Environment.GetEnvironmentVariable("EncounterNotesURL")
                              ?? throw new InvalidOperationException("ExaminationUrl is not set in environment variables.");
            _tokenService = tokens;
        }


        /// <summary>
        /// Add Examination Records 
        /// </summary>
        /// <param name="examinations"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task AddExaminationAsync(List<Examination> examinations)
        {
            var apiUrl = $"{_examinationUrl}/api/Examination/Bulk";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(examinations);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }
    
        /// <summary>
        /// Get all the Examination Records by PatientId
        /// </summary>
        /// <param name="PatientId"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<Examination>> GetExaminationByPatientId(Guid PatientId)
        {
            var apiUrl = $"{_examinationUrl}/api/Examination/{PatientId}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Examination>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }
        /// <summary>
        /// Get only the Active Records by PatientId
        /// </summary>
        /// <param name="Patientid"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>

        public async Task<List<Examination>> GetExaminationByPatientIdAsyncAndIsActive(Guid Patientid)
        {
            var apiUrl = $"{_examinationUrl}/api/Examination/{Patientid}/IsActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Examination>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }
  

        /// <summary>
        /// Update Multiple Examination Records
        /// </summary>
        /// <param name="examinations"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task UpdateExaminationListAsync(List<Examination> examinations)
        {
            var apiUrl = $"{_examinationUrl}/api/Examination/UpdateExamList";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(examinations);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }
    }

}
