﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using static System.Net.WebRequestMethods;

namespace TeyaUIViewModels.ViewModel
{
    public class TherapeuticInterventionsService : ITherapeuticInterventionsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public TherapeuticInterventionsService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<TherapeuticInterventionsData>> GetAllByIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<TherapeuticInterventionsData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<TherapeuticInterventionsData>> GetAllByIdAndIsActiveAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/{id}/IsActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<TherapeuticInterventionsData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task AddTherapeuticInterventionsAsync(List<TherapeuticInterventionsData> medicalHistories)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/AddTherapeuticInterventions";

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task UpdateTherapeuticInterventionsAsync(TherapeuticInterventionsData _TherapeuticInterventions)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/{_TherapeuticInterventions.PatientId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_TherapeuticInterventions);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdateTherapeuticInterventionsListAsync(List<TherapeuticInterventionsData> medicalHistories)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/UpdateTherapeuticInterventionsList";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }
        public async Task DeleteTherapeuticInterventionsByEntityAsync(TherapeuticInterventionsData _TherapeuticInterventions)
        {
            if (_TherapeuticInterventions == null)
            {
                throw new ArgumentNullException(nameof(_TherapeuticInterventions), _localizer["InvalidRecord"]);
            }

            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/DeleteTherapeuticInterventions";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_TherapeuticInterventions);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DeleteLogError"]);
            }
        }

    }
}