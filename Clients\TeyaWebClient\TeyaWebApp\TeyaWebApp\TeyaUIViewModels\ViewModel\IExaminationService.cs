﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IExaminationService
    {

        Task<List<Examination>> GetExaminationByPatientId(Guid PatientId);
        Task<List<Examination>> GetExaminationByPatientIdAsyncAndIsActive(Guid PatientId);
        Task AddExaminationAsync(List<Examination> examinations);
        Task UpdateExaminationListAsync(List<Examination> examinations);

    }

}