﻿using AppointmentContracts;
using AppointmentDataAccessLayer.AppointmentContext;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using AppointmentDataAccessLayer.DataAccessLayerResources;

namespace AppointmentDataAccessLayer.AppointmentImplementation
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private readonly IStringLocalizer<DataAccessLayerStrings> _localizer; 
        private readonly ILogger<AppointmentDatabaseContext> _logger; 
        private readonly ShardMapManagerService _shardMapManagerService;


        public IAppointmentRepository AppointmentRepository { get; }

        public UnitOfWork(ShardMapManagerService shardMapManagerService,
            
                          IStringLocalizer<DataAccessLayerStrings> localizer,
                          ILogger<AppointmentDatabaseContext> logger
                         )
        {
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
          
            AppointmentRepository = new AppointmentRepository(_shardMapManagerService, _localizer, _logger);
        }

        public async Task<int> SaveAsync()
        {
            return 0; 
        }

        public void Dispose()
        {
        }
    }
}