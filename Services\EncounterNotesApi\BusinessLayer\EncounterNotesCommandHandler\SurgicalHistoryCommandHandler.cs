﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class SurgicalHistoryCommandHandler : ISurgicalHistoryCommandHandler<SurgicalHistory>
    {
        private readonly IUnitOfWork _unitOfWork;
        public SurgicalHistoryCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddSurgery(List<SurgicalHistory> Surgeries)
        {
            await _unitOfWork.SurgicalRepository.AddAsync(Surgeries);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateSurgery(SurgicalHistory surgery)
        {
            await _unitOfWork.SurgicalRepository.UpdateAsync(surgery);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteSurgeryById(Guid id)
        {
            await _unitOfWork.SurgicalRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteSurgeryByEntity(SurgicalHistory surgery)
        {
            await _unitOfWork.SurgicalRepository.DeleteByEntityAsync(surgery);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateSurgeryList(List<SurgicalHistory> Surgeries)
        {
            await _unitOfWork.SurgicalRepository.UpdateRangeAsync(Surgeries);
            await _unitOfWork.SaveAsync();
        }
    }
}