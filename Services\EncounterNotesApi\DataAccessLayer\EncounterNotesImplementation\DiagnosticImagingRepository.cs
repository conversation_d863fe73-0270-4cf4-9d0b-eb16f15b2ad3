﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class DiagnosticImagingRepository : GenericRepository<DiagnosticImage>, IDiagnosticImagingRepository
    {
        private readonly RecordDatabaseContext _context;

        public DiagnosticImagingRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<List<DiagnosticImage>> GetDiagnosticImagingById(Guid recordId)
        {
            return await _context.DiagnosticImaging
                .Where(wt => wt.PatientId == recordId)
                .ToListAsync();
        }
    }
}
