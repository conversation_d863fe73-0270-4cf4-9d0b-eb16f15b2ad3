﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IMemberCommandHandler<TText>
    {
        Task DeleteMemberByEntity(Member member);
        Task DeleteMemberById(Guid id);
        Task AddMember(List<TText> texts);
        Task UpdateMember(Member member);
        Task<List<string>> RegisterMembersAsync(List<MemberDto> registrations);
    }
}


