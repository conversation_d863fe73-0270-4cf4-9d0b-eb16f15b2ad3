﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class MedicalHistoryQueryHandler : IMedicalHistoryQueryHandler<MedicalHistory>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public MedicalHistoryQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<MedicalHistory>> GetAllMedicalHistoriesById(Guid id)
        {
            var medicalHistories = await _unitOfWork.MedicalHistoryRepository.GetAllMedicalHistoriesAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id);
        }

        public async Task<IEnumerable<MedicalHistory>> GetMedicalHistoryByIdAndIsActive(Guid id)
        {
            var medicalHistories = await _unitOfWork.MedicalHistoryRepository.GetAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id && medHistory.IsActive == true);
        }
    }
}