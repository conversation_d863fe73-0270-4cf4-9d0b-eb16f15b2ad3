﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class PrescriptionMedicationCommandHandler : IPrescriptionMedicationCommandHandler<PrescriptionMedication>
    {
        private readonly IUnitOfWork _unitOfWork;
        public PrescriptionMedicationCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddMedication(List<PrescriptionMedication> Medications)
        {
            await _unitOfWork.PrescriptionMedicationRepository.AddAsync(Medications);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateMedication(PrescriptionMedication medication)
        {
            await _unitOfWork.PrescriptionMedicationRepository.UpdateAsync(medication);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateMedicationList(List<PrescriptionMedication> Medications)
        {
            await _unitOfWork.PrescriptionMedicationRepository.UpdateRangeAsync(Medications);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteMedicationById(Guid id)
        {
            await _unitOfWork.PrescriptionMedicationRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteMedicationByEntity(PrescriptionMedication medication)
        {
            await _unitOfWork.PrescriptionMedicationRepository.DeleteByEntityAsync(medication);
            await _unitOfWork.SaveAsync();
        }
    }
}
