﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using Syncfusion.Blazor.Buttons;
using Unity;
using Syncfusion.Blazor.Navigations;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;

namespace TeyaWebApp.Components.Pages
{
    public partial class Procedure
    {
        [Inject] public ICPTService _CPTService { get; set; }
        [Inject] private ILogger<Procedures> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] public IProcedureService ProcedureService { get; set; }
        [Inject] public SharedNotesService SharedNotesService { get; set; }
        private SfRichTextEditor richTextEditor;
        private MudDialog showBrowsePopup { get; set; }
        private SfGrid<Procedures> ProcedureGrid;
        private string richTextContent = string.Empty;
        private string symptoms = string.Empty;
        private string notes = string.Empty;
        private CPT selectedCPT;
        private Procedures selectedProcedure;
        private List<string> AssessmentDiagnosis = new List<string>();
        public string CPTName { get; set; }
        [Inject] IAssessmentsService assessmentsService { get; set; }
        private List<Procedures> procedure = new();
        private List<Procedures> addedProcedure = new();
        private List<Procedures> updatedProcedure = new();
        private List<Procedures> deletedProcedure = new();
        private List<AssessmentsData> Localdata = new();
        private List<CPT> _cptCodes { get; set; } = new List<CPT>();

        private bool add = false;
        private Guid PatientId { get; set; }

        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = PatientService.PatientData.Id;
                await LoadProcedureAsync();
                Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientId))
                .GroupBy(a => a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
                SharedNotesService.OnChange += UpdateAssessments;
                AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();
                _cptCodes = await _CPTService.GetAllCPTCodesAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Initialization error");
            }
        }
        private void UpdateAssessments()
        {
            //chiefComplaints = SharedNotesService.GetChiefComplaints();
            //chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            OnInitializedAsync();
            StateHasChanged();  // 🔥 Forces UI update dynamically
        }
        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", process.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.AssessmentData = value;
                    // Find and set the corresponding ID
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        //medication.CheifComplaintId = selectedComplaint.Id;
                        process.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(process.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");

            // 🔥 Conditionally disable the dropdown if a value exists
            // builder.AddAttribute(5, "Enabled", string.IsNullOrEmpty(medication.CheifComplaint));

            builder.CloseComponent();
        };
        /// <summary>
        /// Opens the new dialog box .
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task<IEnumerable<CPT>> SearchCPTCodes(string value, CancellationToken cancellationToken)
        {
            return _cptCodes.Where(cpt =>
                (cpt.CPTCode?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (cpt.Description?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }
        


        private async void AddNewProcedure()
        {
            if (selectedCPT == null)
            {
                Snackbar.Add(Localizer["Please select a CPT code first"], Severity.Warning);
                return;
            }

            var newProcedure = new Procedures
            {
                Id = Guid.NewGuid(),
                PatientId = PatientId,
                PcpId = Guid.Parse(User.id),
                CPTCode = selectedCPT.CPTCode,
                OrderedBy =User.givenName + " " +  User.surname,
                Description = selectedCPT.Description,
                Notes = string.Empty,
                OrderDate = DateTime.Now,
                CreatedByUserId = Guid.Parse(User.id),
                UpdatedByUserId = Guid.Parse(User.id),
                LastUpdatedDate = DateTime.Now,
                IsDeleted = true,
            };

            procedure.Add(newProcedure);
            addedProcedure.Add(newProcedure);
            ResetInputFields();
            await ProcedureGrid.Refresh();
        }
        public void ActionBeginHandler(ActionEventArgs<Procedures> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                deletedProcedure.Add(args.Data);
                args.Data.IsDeleted = false;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (!addedProcedure.Contains(args.Data) && !updatedProcedure.Contains(args.Data))
                {
                    args.Data.UpdatedByUserId = Guid.Parse(User.id);
                    args.Data.LastUpdatedDate = DateTime.Now;
                    updatedProcedure.Add(args.Data);
                }
            }

        }
        private async Task SaveChanges()
        {
            try
            {
                if (addedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                {
                    Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }
                if (updatedProcedure.Any(procedure => string.IsNullOrWhiteSpace(procedure.AssessmentData)))
                {
                    Snackbar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }
                // Handle new entries
                if (addedProcedure.Any())
                {
                 
                    await ProcedureService.AddProcedureAsync(addedProcedure);
                    addedProcedure.Clear();
                }

                // Handle updates
                if (updatedProcedure.Any())
                {
                    await ProcedureService.UpdateProcedureListAsync(updatedProcedure);
                    updatedProcedure.Clear();
                }

                // Handle deletes
                if (deletedProcedure.Any())
                {
                    await ProcedureService.UpdateProcedureListAsync(deletedProcedure);
                    deletedProcedure.Clear();
                }

                await LoadProcedureAsync();
                richTextContent = GenerateRichTextContent();
                await richTextEditor.RefreshUIAsync();
                CloseBrowsePopup();
                Snackbar.Add(Localizer["Changes saved successfully!"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error saving changes");
                Snackbar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }
        private async Task CancelChanges()
        {
            addedProcedure.Clear();
            updatedProcedure.Clear();
            deletedProcedure.Clear();
            await LoadProcedureAsync();
            CloseBrowsePopup();
            Snackbar.Add("Changes discarded", Severity.Info);
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        private async Task LoadProcedureAsync()
        {
            try
            {
                procedure = await ProcedureService.LoadProcedureAsync(PatientId);

                richTextContent = GenerateRichTextContent();
                                
                if (richTextEditor != null)
                    await richTextEditor.RefreshUIAsync();
                if (ProcedureGrid != null)
                    await ProcedureGrid.Refresh();


                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading Procedure");
            }
        }

        private string GenerateRichTextContent() =>
     "<table border='1' style='border-collapse: collapse; width: 100%; table-layout: fixed; font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif; line-height: 1.2;'>" +
     "<tr>" +
         "<th style='width: 15%; word-wrap: break-word;'>Order Date</th>" +
         "<th style='width: 15%; word-wrap: break-word;'>CPT</th>" +
         "<th style='width: 20%; word-wrap: break-word;'>Description</th>" +
         "<th style='width: 20%; word-wrap: break-word;'>Notes</th>" +
         "<th style='width: 15%; word-wrap: break-word;'>Assessment</th>" + // ✅ fixed: missing '<' before 'th'
         "<th style='width: 15%; word-wrap: break-word;'>Ordered By</th>" +
     "</tr>" +
     string.Join("", procedure.OrderByDescending(o => o.OrderDate)
         .Select(o => $"<tr>" +
             $"<td style='word-wrap: break-word;'>{o.OrderDate:yyyy-MM-dd}</td>" +
             $"<td style='word-wrap: break-word;'>{o.CPTCode}</td>" +
             $"<td style='word-wrap: break-word;'>{o.Description}</td>" +
             $"<td style='word-wrap: break-word;'>{o.Notes}</td>" +
             $"<td style='word-wrap: break-word;'>{o.AssessmentData}</td>" +
             $"<td style='word-wrap: break-word;'>{o.OrderedBy}</td>" +
         $"</tr>")) +
     "</table>";

        private void CloseBrowsePopup()
        {
            symptoms = string.Empty;
            notes = string.Empty;
            showBrowsePopup.CloseAsync();
        }

        private async Task OnCPTSelected(CPT selected)
        {
            selectedCPT = selected;
            StateHasChanged();
        }
        private void ResetInputFields()
        {
            selectedCPT = null;

        }
        private async Task OpenBrowsePopup()
        {
            if (showBrowsePopup != null)
            {
                await showBrowsePopup.ShowAsync();
            }
            else
            {
                Logger.LogError("showBrowsePopup is null");
            }
        }
    }
}