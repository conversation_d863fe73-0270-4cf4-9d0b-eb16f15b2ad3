using MessageContracts;

namespace MessageBusinessLayer.Services
{
    public interface IMessageNotificationService
    {
        Task NotifyNewMessageAsync(string recipientEmail, Message message);
        Task NotifyMessageReadAsync(string senderEmail, Guid messageId);
        Task NotifyMessageArchivedAsync(string userEmail, Guid messageId);
        Task NotifyMessageDeletedAsync(string userEmail, Guid messageId);
        Task NotifyMessageImportantAsync(string userEmail, Guid messageId, bool isImportant);
    }
}
