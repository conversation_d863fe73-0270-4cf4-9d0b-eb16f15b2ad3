using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class Assessments
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public IAssessmentsService _AssessmentsService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IChiefComplaintService ChiefComplaintService { get; set; }
        [Inject] private SharedNotesService SharedNotesService { get; set; }
        private List<ChiefComplaintDTO> chiefComplaints = new();
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        private List<string> chiefComplaintDescriptions = new List<string>();

        public string ICDName { get; set; }
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private MudDialog __Assessments;
        private Guid PatientId { get; set; }
        private DateTime? _CreatedDate;

        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        public SfGrid<TeyaUIModels.Model.AssessmentsData> AssessmentsGrid { get; set; }

        private List<TeyaUIModels.Model.AssessmentsData> AddList = new();
        private List<TeyaUIModels.Model.AssessmentsData> _Assessments { get; set; }
        private List<TeyaUIModels.Model.AssessmentsData> deleteAssessmentslist { get; set; } = new List<TeyaUIModels.Model.AssessmentsData>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
         };




        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {

            try
            {
                PatientId = _PatientService.PatientData.Id;
                LocalData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId))
                    .GroupBy(c => c.Description)
                    .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First())
                    .ToList();
                SharedNotesService.OnChange += UpdateComplaints;  
                chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();

                _icdCodes = await _ICDService.GetAllICDCodesAsync();
                _Assessments = await _AssessmentsService.GetAllByIdAndIsActiveAsync(PatientId);
                UpdateEditorContent();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving ICD codes: {ex.Message}");
            }
        }
        private void UpdateComplaints()
        {
            chiefComplaints = SharedNotesService.GetChiefComplaints();
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            OnInitializedAsync();
            StateHasChanged();  
        }

        public void Dispose()
        {
            SharedNotesService.OnChange -= UpdateComplaints;  
        }
        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not AssessmentsData assessment) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaintDescriptions);
            builder.AddAttribute(2, "Value", assessment.CheifComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    assessment.CheifComplaint = value;
                    var selectedComplaint = LocalData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        assessment.CheifComplaintId = selectedComplaint.Id;
                        Console.WriteLine(assessment.CheifComplaintId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };
        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void UpdateEditorContent()
        {
            editorContent = string.Join("<br>", _Assessments
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $@"<p><strong>{Localizer["Created Date"]}:</strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToShortDateString() : Localizer["No date"])}  <br><strong>{Localizer["Diagnosis"]}:</strong> {s.Diagnosis} <br><strong>{Localizer["Specify"]}:</strong> {s.Specify}</p>"));
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>

        private async Task OpenNewDialogBox()
        {
            await __Assessments.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await __Assessments.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected async Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            var searchResults = _icdCodes
                .Where(icd => (icd.Code != null && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                              (icd.Description != null && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase)))
                .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                .ToList();
            cancellationToken.ThrowIfCancellationRequested();
            return searchResults;
        }

        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewDiagnosis()
        {
            var newDiagnosis = new TeyaUIModels.Model.AssessmentsData
            {
                AssessmentsID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = _PatientService.PatientData.OrganizationID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Diagnosis = ICDName,
                IsActive = true,
            };

            AddList.Add(newDiagnosis);
            _Assessments.Add(newDiagnosis);
            await AssessmentsGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private async void ResetInputFields()
        {
            ICDName = string.Empty;
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<TeyaUIModels.Model.AssessmentsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleleAssessments = args.Data;
                var existingItem = AddList.FirstOrDefault(c => c.AssessmentsID == deleleAssessments.AssessmentsID);
                args.Data.IsActive = false;

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    deleleAssessments.IsActive = false;
                    deleleAssessments.UpdatedDate = DateTime.Now;
                    deleteAssessmentslist.Add(deleleAssessments);
                }

            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Any(assessment => string.IsNullOrWhiteSpace(assessment.CheifComplaint)))
            {
                Snackbar.Add("Each assessment must have a Chief Complaint.", Severity.Warning);
                return;
            }
            if (AddList.Count != 0)
            {
                await _AssessmentsService.AddAssessmentsAsync(AddList);
                SharedNotesService.AssessmentsChanged();
            }
            await _AssessmentsService.UpdateAssessmentsListAsync(_Assessments);
            await _AssessmentsService.UpdateAssessmentsListAsync(deleteAssessmentslist);
            deleteAssessmentslist.Clear();
            AddList.Clear();
            UpdateEditorContent();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deleteAssessmentslist.Clear();
            AddList.Clear();
            _Assessments = await _AssessmentsService.GetAllByIdAndIsActiveAsync(PatientId);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }
    }
}