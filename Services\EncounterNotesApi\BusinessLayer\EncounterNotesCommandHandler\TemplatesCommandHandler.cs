﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class TemplatesCommandHandler : ITemplatesCommandHandler<Templates>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TemplatesCommandHandler> _logger;

        public TemplatesCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<TemplatesCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddTemplates(List<Templates> templates)
        {
            await _unitOfWork.TemplatesRepository.AddAsync(templates);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateTemplates(List<Templates> templates)
        {
            await _unitOfWork.TemplatesRepository.UpdateRangeAsync(templates);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTemplatesById(Guid id)
        {
            await _unitOfWork.TemplatesRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTemplatesByEntity(Templates templates)
        {
            await _unitOfWork.TemplatesRepository.DeleteByEntityAsync(templates);
            await _unitOfWork.SaveAsync();
        }
    }
}