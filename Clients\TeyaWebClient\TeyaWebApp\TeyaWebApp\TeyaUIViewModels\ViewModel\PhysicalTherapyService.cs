﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using static System.Net.WebRequestMethods;

namespace TeyaUIViewModels.ViewModel
{
    public class PhysicalTherapyService : IPhysicalTherapyService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public PhysicalTherapyService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<PhysicalTherapyData>> GetAllByIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/PhysicalTherapy/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PhysicalTherapyData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<PhysicalTherapyData>> GetAllByIdAndIsActiveAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/PhysicalTherapy/{id}/IsActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PhysicalTherapyData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task AddPhysicalTherapyAsync(List<PhysicalTherapyData> medicalHistories)
        {
            var apiUrl = $"{_EncounterNotes}/api/PhysicalTherapy/AddPhysicalTherapy";

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task UpdatePhysicalTherapyAsync(PhysicalTherapyData _PhysicalTherapy)
        {
            var apiUrl = $"{_EncounterNotes}/api/PhysicalTherapy/{_PhysicalTherapy.PatientId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_PhysicalTherapy);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdatePhysicalTherapyListAsync(List<PhysicalTherapyData> medicalHistories)
        {
            var apiUrl = $"{_EncounterNotes}/api/PhysicalTherapy/UpdatePhysicalTherapyList";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }
        public async Task DeletePhysicalTherapyByEntityAsync(PhysicalTherapyData _PhysicalTherapy)
        {
            if (_PhysicalTherapy == null)
            {
                throw new ArgumentNullException(nameof(_PhysicalTherapy), _localizer["InvalidRecord"]);
            }

            var apiUrl = $"{_EncounterNotes}/api/PhysicalTherapy/DeletePhysicalTherapy";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_PhysicalTherapy);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DeleteLogError"]);
            }
        }

    }
}