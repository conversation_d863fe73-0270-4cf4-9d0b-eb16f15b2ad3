﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class AssessmentsQueryHandler:IAssessmentsQueryHandler<AssessmentsData>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public AssessmentsQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<AssessmentsData>> GetAllAssessmentsById(Guid id)
        {
            var medicalHistories = await _unitOfWork.AssessmentsRepository.GetAllAssessmentsAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id);
        }

        public async Task<IEnumerable<AssessmentsData>> GetAssessmentsByIdAndIsActive(Guid id)
        {
            var medicalHistories = await _unitOfWork.AssessmentsRepository.GetAsync();
            return medicalHistories.Where(medHistory => medHistory.PatientId == id && medHistory.IsActive == true);
        }
        public async Task<List<string>> GetAllMedications(Guid PatientId)
        {
            List<string> asesessmentrelatedmedications = await _unitOfWork.AssessmentsRepository.GetAssessmentRelatedMedications(PatientId);
            return asesessmentrelatedmedications;
        }
    }
}
