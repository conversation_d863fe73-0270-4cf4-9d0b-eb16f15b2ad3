﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IHospitalizationRecordCommandHandler<TText>
    {
        Task DeleteHospitalizationRecordById(Guid id);
        Task<bool> UpdateHospitalizationRecordList(List<HospitalizationRecord> HospitalizationRecordList);
        Task AddHospitalizationRecord(List<TText> hospitalizationRecordList);
        Task UpdateHospitalizationRecord(HospitalizationRecord hospitalizationRecord);
    }
}