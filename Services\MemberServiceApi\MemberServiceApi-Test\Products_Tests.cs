﻿
//Unit Tests Using NUnit 3.0 for ProductController

using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using ProductServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using DataAccessLayer.Context;
using Microsoft.Identity.Web.Resource;
using Microsoft.AspNetCore.Authorization;
using MemberServiceBusinessLayer;

namespace ProductServiceApi.Tests
{
    //Base Test Class for ProductController With Mock Items
    [TestFixture]
    public class ProductsControllerTests
    {
        private Mock<IProductQueryHandler<Product>> _mockQueryHandler;
        private Mock<IProductCommandHandler<Product>> _mockCommandHandler;
        private Mock<ILogger<ProductsController>> _mockLogger;
        private Mock<IStringLocalizer<ProductsController>> _mockLocalizer;
        private Mock<IProductMembersQueryHandler<ProductUserAccess>> _mockMembersQueryHandler;
        private Mock<IUserAccessCommandHandler<ProductUserAccess>> _mockAccessCommandHandler;
        private ProductsController _controller;

        //Setting up Mock Objects
        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IProductQueryHandler<Product>>();
            _mockCommandHandler = new Mock<IProductCommandHandler<Product>>();
            _mockLogger = new Mock<ILogger<ProductsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<ProductsController>>();
            _mockMembersQueryHandler = new Mock<IProductMembersQueryHandler<ProductUserAccess>>();
            _mockAccessCommandHandler = new Mock<IUserAccessCommandHandler<ProductUserAccess>>();

            _controller = new ProductsController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object,
                _mockMembersQueryHandler.Object,
                _mockAccessCommandHandler.Object
            );
        }

       
        [Test] 
        public async Task Get_WhenCalled_ReturnsOkWithProducts()
        {
            // Arrange
            var mockProducts = new List<Product>
            {
                new Product { Id = Guid.NewGuid(), Name = "Product 1", Description = "Description 1" },
                new Product { Id = Guid.NewGuid(), Name = "Product 2", Description = "Description 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetProduct())
                .ReturnsAsync(mockProducts);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockProducts));
        }

        
        [Test] 
        public async Task GetById_WhenCalledWithValidId_ReturnsOkWithProduct()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var mockProduct = new Product { Id = productId, Name = "Product 1", Description = "Description 1" };

            _mockQueryHandler
                .Setup(q => q.GetProductById(productId))
                .ReturnsAsync(mockProduct);

            // Act
            var result = await _controller.GetById(productId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockProduct));
        }

        
        [Test] 
        public async Task UpdateById_WhenCalledWithValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var product = new Product { Id = productId, Name = "Product 1", Description = "Description 1" };

            _mockCommandHandler
                .Setup(handler => handler.UpdateProduct(It.IsAny<Product>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateById(productId, product);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

        
        [Test] 
        public async Task UpdateById_WhenCalledWithNullProduct_ReturnsBadRequest()
        {
            // Arrange
            var productId = Guid.NewGuid();
            Product product = null;  

            var localizedString = new LocalizedString("InvalidProduct", "Invalid Product");

            _mockLocalizer
                .Setup(l => l["InvalidProduct"])
                .Returns(localizedString);

            // Act
            var result = await _controller.UpdateById(productId, product);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));
            Assert.That(badRequestResult.Value.ToString(), Is.EqualTo("Invalid Product"));  
        }

       
        [Test] 
        public async Task DeleteById_WhenCalledWithValidId_ReturnsOkWithMessage()
        {
            // Arrange
            var productId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(handler => handler.DeleteProductById(productId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteById(productId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

        
        [Test] 
        public async Task DeleteByEntity_WhenCalledWithValidProduct_ReturnsOkWithMessage()
        {
            // Arrange
            var product = new Product { Id = Guid.NewGuid(), Name = "Product 1", Description = "Description 1" };

            _mockCommandHandler
                .Setup(handler => handler.DeleteProductByEntity(product))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteByEntity(product);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

        
        [Test] 
        public async Task Registration_WhenCalledWithValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var mockRegistrations = new List<ProductRegistrationDto>
            {
                new ProductRegistrationDto { Name = "Product 1", Description = "Description 1", ByProduct = "Brand A" }
            };

            _mockCommandHandler
                .Setup(handler => handler.AddProduct(It.IsAny<List<Product>>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(mockRegistrations);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

       
        [Test] 
        public async Task Registration_WhenCalledWithNullOrEmptyRegistrations_ReturnsBadRequest()
        {
            // Arrange: Null Registration
            List<ProductRegistrationDto> mockRegistrations = null;  

            var localizedString = new LocalizedString("NoProduct", "No products provided");

            _mockLocalizer
                .Setup(l => l["NoProduct"])
                .Returns(localizedString);

            // Act
            var result = await _controller.Registration(mockRegistrations);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));  
            Assert.That(badRequestResult.Value.ToString(), Is.EqualTo("No products provided")); 

            // Arrange: Empty list scenario
            mockRegistrations = new List<ProductRegistrationDto>();  

            // Act
            result = await _controller.Registration(mockRegistrations);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));  
            Assert.That(badRequestResult.Value.ToString(), Is.EqualTo("No products provided")); 
        }

        
        [Test] 
        public async Task GetMembersForProduct_WhenCalledWithValidProductId_ReturnsOkWithMembers()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var mockMembers = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe" }
            };

            _mockMembersQueryHandler
                .Setup(q => q.GetProductMembers(productId))
                .ReturnsAsync(mockMembers);

            // Act
            var result = await _controller.GetMembersForProduct(productId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockMembers));
        }

        
        [Test] 
        public async Task GetMembersForProduct_WhenNoMembersFound_ReturnsNotFound()
        {
            // Arrange: Null List
            var productId = Guid.NewGuid();

            _mockMembersQueryHandler
                .Setup(q => q.GetProductMembers(productId))
                .ReturnsAsync((List<Member>)null);  

            // Act
            var result = await _controller.GetMembersForProduct(productId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<NotFoundResult>(), "Expected result to be of type NotFoundResult");
            var notFoundResult = result.Result as NotFoundResult;
            Assert.That(notFoundResult!.StatusCode, Is.EqualTo(404));  

            // Arrange: Empty list scenario
            _mockMembersQueryHandler
                .Setup(q => q.GetProductMembers(productId))
                .ReturnsAsync(new List<Member>());  

            // Act
            result = await _controller.GetMembersForProduct(productId);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<NotFoundResult>(), "Expected result to be of type NotFoundResult");
            notFoundResult = result.Result as NotFoundResult;
            Assert.That(notFoundResult!.StatusCode, Is.EqualTo(404));  
        }

        
        [Test] 
        public async Task UpdateAccess_WhenCalledWithValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var memberAccessUpdates = new List<MemberAccessUpdate>
            {
                new MemberAccessUpdate { MemberId = Guid.NewGuid()}
            };

            _mockAccessCommandHandler
                .Setup(handler => handler.UpdateUserAccessAsync(productId, memberAccessUpdates))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateAccess(productId, memberAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Expected result to be of type OkObjectResult");
            var okResult = result as OkObjectResult;
            Assert.That(okResult!.StatusCode, Is.EqualTo(200));
        }

      
        [Test] 
        public async Task UpdateAccess_WhenCalledWithInvalidData_ReturnsBadRequest()
        {
            // Arrange
            var productId = Guid.Empty;  
            var memberAccessUpdates = new List<MemberAccessUpdate>
            {
                new MemberAccessUpdate { MemberId = Guid.NewGuid() }
            };

            // Act
            var result = await _controller.UpdateAccess(productId, memberAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));  
            
            // Arrange: Simulating a null memberAccessUpdates
            productId = Guid.NewGuid();  
            memberAccessUpdates = null;   

            // Act
            result = await _controller.UpdateAccess(productId, memberAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>(), "Expected result to be of type BadRequestObjectResult");
            badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult!.StatusCode, Is.EqualTo(400));  
        }

    }
}
