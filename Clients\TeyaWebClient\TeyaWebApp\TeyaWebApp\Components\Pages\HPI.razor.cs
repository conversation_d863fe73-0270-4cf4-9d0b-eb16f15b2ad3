﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaWebApp.TeyaAIScribeResources;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class HPI : ComponentBase
    {
        [Inject] private IHistoryOfPresentIllnessService _historyOfPresentIllnessService { get; set; }
        [Inject] private ISymptomsService _symptomsService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }

        private SfRichTextEditor RichTextEditor;
        private SfGrid<HistoryOfPresentIllness> HpiGrid;
        private MudDialog _hpiDialog;
        private string editorContent;
        private string Symptom;
        private List<HistoryOfPresentIllness> hpiEntries { get; set; } = new List<HistoryOfPresentIllness>();
        private List<HistoryOfPresentIllness> originalEntries { get; set; } = new List<HistoryOfPresentIllness>();
        private List<Symptoms> symptomSuggestions = new List<Symptoms>();

        // Separate lists for add, update, and delete
        private List<HistoryOfPresentIllness> addList = new List<HistoryOfPresentIllness>();
        private List<HistoryOfPresentIllness> updateList = new List<HistoryOfPresentIllness>();
        private List<HistoryOfPresentIllness> deleteList = new List<HistoryOfPresentIllness>();
        private List<Symptoms> addSymptomList = new List<Symptoms>();


        private Guid patientId { get; set; }


        protected override async Task OnInitializedAsync()
        {
            await LoadHpiEntries();
            await LoadSymptoms();
        }

        public async Task LoadHpiEntries()
        {
            patientId = _PatientService.PatientData.Id;
            // Fetch the active HPI records for the patient
            hpiEntries = await _historyOfPresentIllnessService.GetActiveHpiByPatientIdAsync(patientId);
            originalEntries = hpiEntries.Select(e => CloneHpiEntry(e)).ToList();
            editorContent = string.Join("<p>", hpiEntries.Select(e => $"{e.Symptoms}: {e.Notes}"));
        }

        public async Task LoadSymptoms()
        {
            symptomSuggestions = (await _symptomsService.GetSymptomsAsync()).ToList();
        }

        public HistoryOfPresentIllness CloneHpiEntry(HistoryOfPresentIllness entry)
        {
            return new HistoryOfPresentIllness
            {
                Id = entry.Id,
                PatientId = entry.PatientId,
                OrganizationId = entry.OrganizationId,
                PCPId = entry.PCPId,
                Symptoms = entry.Symptoms,
                Location = entry.Location,
                Severity = entry.Severity,
                StartDate = entry.StartDate,
                EndDate = entry.EndDate,
                Notes = entry.Notes,
                IsActive = entry.IsActive
            };
        }

        /// <summary>
        ///  adding a new entry
        /// </summary>
        /// <returns></returns>
        public async Task AddNewEntry()
        {
            if (string.IsNullOrEmpty(Symptom))
            {
                Snackbar.Add(Localizer["Please fill in the Symptom field"], Severity.Warning);
                return;
            }

            var symptomEntry = symptomSuggestions.FirstOrDefault(s => s.Symptom.Equals(Symptom, StringComparison.OrdinalIgnoreCase));

            if (symptomEntry == null)
            {
                symptomEntry = new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = Symptom
                };
                symptomSuggestions.Add(symptomEntry);
                addSymptomList.Add(symptomEntry);


            }

            var newEntry = new HistoryOfPresentIllness
            {
                Id = Guid.NewGuid(),
                PatientId = patientId,
                OrganizationId = Guid.Parse(User.id),
                PCPId = Guid.Parse(User.id),
                Symptoms = symptomEntry.Symptom,
                Location = "Unknown",
                Severity = "Unknown",
                Duration=null,
                StartDate = null,
                EndDate = null,
                Notes = "NA",
                IsActive = true
            };

            addList.Add(newEntry);
            hpiEntries.Add(newEntry);
            await HpiGrid.Refresh();
            ResetInputFields();
        }

        public void ResetInputFields()
        {
            Symptom = string.Empty;
        }

        public void ActionCompletedHandler(ActionEventArgs<HistoryOfPresentIllness> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                args.Data.IsActive = false;
                deleteList.Add(args.Data);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                var existingEntry = originalEntries.FirstOrDefault(e => e.Id == args.Data.Id && e.PatientId == args.Data.PatientId);
                if (existingEntry != null)
                {
                    updateList.Add(args.Data);
                }
            }
        }


        public void ActionBeginHandler(ActionEventArgs<HistoryOfPresentIllness> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Validate Start Date and End Date

                var today = DateTime.Today;

                if (args.Data.StartDate.HasValue && args.Data.EndDate.HasValue)
                {
                    if (args.Data.StartDate > today)
                    {
                        Snackbar.Add(@Localizer["Start Date cannot be in the future"], Severity.Warning);
                        args.Cancel = true;
                    }
                    else if (args.Data.EndDate < today)
                    {
                        Snackbar.Add(@Localizer["End Date cannot be in the past"], Severity.Warning);
                        args.Cancel = true;
                    }
                    else if (args.Data.EndDate < args.Data.StartDate)
                    {
                        Snackbar.Add(@Localizer["End Date cannot be before Start Date"], Severity.Warning);
                        args.Cancel = true;
                    }
                }
                else if (args.Data.StartDate.HasValue && args.Data.StartDate > today)
                {
                    Snackbar.Add(@Localizer["Start Date cannot be in the future"], Severity.Warning);
                    args.Cancel = true;
                }
                else if (args.Data.EndDate.HasValue && args.Data.EndDate < today)
                {
                    Snackbar.Add(@Localizer["End Date cannot be in the past"], Severity.Warning);
                    args.Cancel = true;
                }


                // Validate Notes 
                if (!string.IsNullOrEmpty(args.Data.Notes) && !IsAlphabeticWithSpaces(args.Data.Notes))
                {
                    Snackbar.Add(@Localizer["Notes field should only contain alphabets and spaces"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                // Validate Severity
                if (!string.IsNullOrEmpty(args.Data.Severity) && !IsAlphabeticWithSpaces(args.Data.Severity))
                {
                    Snackbar.Add(@Localizer["Severity field should only contain alphabets and spaces"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                // Validate Location
                if (!string.IsNullOrEmpty(args.Data.Location) && !IsAlphabeticWithSpaces(args.Data.Location))
                {
                    Snackbar.Add(@Localizer["Location field should only contain alphabets and spaces"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
            }
        }

        // Method to check if a string contains only alphabets and spaces
        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s]+$");
        }

        /// <summary>
        /// This will made permanent changes in the Database
        /// </summary>
        /// <returns></returns>
        public async Task SaveChanges()
        {
            try
            {
                // Add new symptoms to the database

                if (addSymptomList.Any())
                {
                    await _symptomsService.AddSymptomsAsync(addSymptomList);
                    symptomSuggestions.AddRange(addSymptomList);
                }


                if (addList.Any())
                {
                    await _historyOfPresentIllnessService.AddHpiAsync(addList);
                    addList.Clear();
                }
                if (updateList.Any())
                {
                    await _historyOfPresentIllnessService.UpdateHpiListAsync(updateList);
                    updateList.Clear();
                }

                if (deleteList.Any())
                {
                    await _historyOfPresentIllnessService.UpdateHpiListAsync(deleteList);
                    deleteList.Clear();
                }

                await LoadHpiEntries();

                Snackbar.Add(Localizer["Changes saved successfully."], Severity.Success);
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to save changes: {ex.Message}");
                Console.WriteLine($"[DEBUG] Stack Trace: {ex.StackTrace}");

                Snackbar.Add(Localizer["Failed to save changes. Please try again."], Severity.Error);
            }
            CloseHpiDialog();
        }

        /// <summary>
        /// Cancel changes will discard all the changes that are not saved and go back to its previous state
        /// </summary>
        /// <returns></returns>
        public async Task CancelChanges()
        {
            hpiEntries = originalEntries.Select(e => CloneHpiEntry(e)).ToList();
            addList.Clear();
            updateList.Clear();
            deleteList.Clear();
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseHpiDialog();
        }


        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        public void OpenHpiDialog()
        {
            _hpiDialog.ShowAsync();
        }

        public void CloseHpiDialog()
        {
            ResetInputFields();
            _hpiDialog.CloseAsync();
        }

        public List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add" },
        };

    }
}
