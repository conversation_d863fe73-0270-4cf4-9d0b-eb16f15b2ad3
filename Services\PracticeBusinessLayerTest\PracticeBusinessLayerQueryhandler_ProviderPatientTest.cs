﻿
using Moq;
using PracticeBusinessLayer.QueryHandler;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;

namespace PracticeBusinessLayerTest
{
    public class PracticeBusinessLayerQueryhandlerPeopleTest
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IProviderPatientRepository> _mockPeopleRepository;
        private readonly ProviderPatientQueryHandler _queryHandler;

        public PracticeBusinessLayerQueryhandlerPeopleTest()
        {

            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPeopleRepository = new Mock<IProviderPatientRepository>();


            _mockUnitOfWork.Setup(uow => uow.ProviderPatientRepository).Returns(_mockPeopleRepository.Object);
            _queryHandler = new ProviderPatientQueryHandler(null, _mockUnitOfWork.Object);
        }

        [Fact]
        public async Task GetPeople_ShouldReturnAllPeople()
        {
            // Arrange:
            var peopleList = new List<ProviderPatient>
            {
                new ProviderPatient { Id = Guid.NewGuid(),PCPId = Guid.NewGuid(), PatientName = "P01", PCPName = "Dr.D1" },
                new ProviderPatient { Id = Guid.NewGuid(),PCPId = Guid.NewGuid(), PatientName = "P02", PCPName = "Dr.D2" }
            };

            _mockPeopleRepository.Setup(repo => repo.GetAllAsync()).ReturnsAsync(peopleList);

            // Act:
            var result = await _queryHandler.GetProviderPatient();

            // Assert:
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Equal(peopleList, result);
            Assert.Equal(peopleList[0].Id, result.First().Id);
            Assert.Equal("P01", result.First().PatientName);
        }

        [Fact]
        public async Task GetPeople_ShouldReturnEmptyList_WhenNoPeopleExist()
        {
            // Arrange:
            _mockPeopleRepository.Setup(repo => repo.GetAllAsync()).ReturnsAsync(new List<ProviderPatient>());

            // Act:
            var result = await _queryHandler.GetProviderPatient();

            // Assert:
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetPeopleById_ShouldReturnPeople_WhenIdExists()
        {

            var id = Guid.NewGuid();
            var expectedPerson = new ProviderPatient { Id = id, PCPId = Guid.NewGuid(), PatientName = "P01", PCPName = "Dr.D1" };

            //Arrange
            _mockPeopleRepository.Setup(repo => repo.GetByIdAsync(id)).ReturnsAsync(expectedPerson);

            // Act:
            var result = await _queryHandler.GetProviderPatientById(id);

            // Assert:
            Assert.NotNull(result);
            Assert.Equal(expectedPerson, result);
        }

        [Fact]
        public async Task GetPeopleById_ShouldReturnNull_WhenIdDoesNotExist()
        {
            // Arrange:
            var id = Guid.NewGuid();

            _mockPeopleRepository.Setup(repo => repo.GetByIdAsync(id)).ReturnsAsync((ProviderPatient)null);

            // Act:
            var result = await _queryHandler.GetProviderPatientById(id);

            // Assert:
            Assert.Null(result);
        }
    }
}

