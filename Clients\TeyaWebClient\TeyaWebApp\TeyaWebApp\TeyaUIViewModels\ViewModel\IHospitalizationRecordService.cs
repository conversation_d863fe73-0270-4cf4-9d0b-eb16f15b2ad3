﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IHospitalizationRecordService
    {
        Task CreateHospitalizationRecordAsync(List<HospitalizationRecord> hospitalizationRecord);
        Task<List<HospitalizationRecord>> GetHospitalizationRecordAsync(Guid id);
        Task UpdateHospitalizationRecordList(List<HospitalizationRecord> hospitalizationRecords);
        Task<List<HospitalizationRecord>> GetHospitalizationRecordByIdAsyncAndIsActive(Guid id);
        Task DeleteHospitalizationRecordAsync(Guid taskId);
        Task UpdateHospitalizationRecordAsync(HospitalizationRecord hospitalizationRecord);
    }
}