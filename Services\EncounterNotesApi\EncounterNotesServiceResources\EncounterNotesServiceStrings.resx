﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="GetLogError" xml:space="preserve">
    <value>An error occurred while fetching members.</value>
  </data>
  <data name="PostLogError" xml:space="preserve">
    <value>An error occurred while registering members.</value>
  </data>
  <data name="NoMembers" xml:space="preserve">
    <value>No members to register.</value>
  </data>
  <data name="SuccessfulEncounterNote" xml:space="preserve">
    <value>Members registered successfully.</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>A database error occurred.</value>
  </data>
  <data name="DeleteSuccessful" xml:space="preserve">
    <value>Delete Successful</value>
  </data>
  <data name="MemberNotFound" xml:space="preserve">
    <value>Member Not Found</value>
  </data>
  <data name="UpdateLogError" xml:space="preserve">
    <value>Log not updated</value>
  </data>
  <data name="UpdateSuccessful" xml:space="preserve">
    <value>Update Successful</value>
  </data>
  <data name="InvalidId" xml:space="preserve">
    <value>The given Id is not valid</value>
  </data>
  <data name="InvalidMember" xml:space="preserve">
    <value>Not a valid member</value>
  </data>
  <data name="DeleteLogError" xml:space="preserve">
    <value>Item is not deleted</value>
  </data>
  <data name="AccessError" xml:space="preserve">
    <value>ProductId and MemberId are required.</value>
  </data>
  <data name="AccessUpdateSuccessful" xml:space="preserve">
    <value>Access updated successfully</value>
  </data>
  <data name="AccessNotFound" xml:space="preserve">
    <value>Access record not found</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Records" xml:space="preserve">
    <value>Records</value>
  </data>
  <data name="NewIdFunction" xml:space="preserve">
    <value>NEWID()</value>
  </data>
  <data name="EncounterNote" xml:space="preserve">
    <value>EncounterNote</value>
  </data>
  <data name="ProductUserAccess" xml:space="preserve">
    <value>ProductUserAccess</value>
  </data>
  <data name="RecordService" xml:space="preserve">
    <value>RecordService</value>
  </data>
  <data name="key" xml:space="preserve">
    <value>bd759e57228940a39fb69f4a1981d2f7</value>
  </data>
  <data name="model" xml:space="preserve">
    <value>gpt-4o</value>
  </data>
  <data name="SummarizePrompt" xml:space="preserve">
    <value>Summarize in 20 words or less: {0}</value>
  </data>
  <data name="&quot;200&quot;" xml:space="preserve">
    <value>200</value>
  </data>
  <data name="&quot;500&quot;" xml:space="preserve">
    <value>500</value>
  </data>
  <data name="EncounterNoteFailed" xml:space="preserve">
    <value>EncounterNote Failed</value>
  </data>
  <data name="SpeechProcessingError" xml:space="preserve">
    <value>An error occurred while processing speeches.</value>
  </data>
  <data name="InternalServerError" xml:space="preserve">
    <value>Internal server error</value>
  </data>
  <data name="UploadEndpointHit" xml:space="preserve">
    <value>Upload endpoint hit</value>
  </data>
  <data name="NoFilesFound" xml:space="preserve">
    <value>No files in request</value>
  </data>
  <data name="ReceivedFile" xml:space="preserve">
    <value>Received file:</value>
  </data>
  <data name="audio/wav" xml:space="preserve">
    <value>audio/wav</value>
  </data>
  <data name="FileUploadedToBlob" xml:space="preserve">
    <value>File uploaded successfully to Blob Storage: </value>
  </data>
  <data name="ErrorProcessingAudioFile" xml:space="preserve">
    <value>Error processing audio upload</value>
  </data>
  <data name="&quot;16000&quot;" xml:space="preserve">
    <value>16000</value>
  </data>
  <data name="pcm_s16le" xml:space="preserve">
    <value>pcm_s16le</value>
  </data>
  <data name="wav" xml:space="preserve">
    <value>wav</value>
  </data>
  <data name="/app/ffmpeg" xml:space="preserve">
    <value>/app/ffmpeg</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>RecordNotFound</value>
  </data>
  <data name="ErrorCreatingComplaint" xml:space="preserve">
    <value>Error creating chief complaint</value>
  </data>
  <data name="ComplaintNotFound" xml:space="preserve">
    <value>Chief complaint not found</value>
  </data>
  <data name="ErrorFetchingComplaint" xml:space="preserve">
    <value>Error retrieving chief complaint</value>
  </data>
  <data name="ErrorFetchingComplaints" xml:space="preserve">
    <value>Error retrieving complaints list</value>
  </data>
  <data name="ErrorUpdatingComplaint" xml:space="preserve">
    <value>Error updating chief complaint</value>
  </data>
  <data name="ErrorDeletingComplaint" xml:space="preserve">
    <value>Error deleting chief complaint</value>
  </data>

  <data name="RecordNotFound " xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="GetProcedureError" xml:space="preserve">
    <value> An error occurred while retrieving procedures</value>
  </data>
  <data name="NoProcedureProvided " xml:space="preserve">
    <value>No procedure data provided</value>
  </data>
  <data name="ProcedureAdded" xml:space="preserve">
    <value> Procedure(s) added successfully</value>
  </data>
  <data name="DatabaseError A database error occurred" xml:space="preserve">
    <value>A database error occurred</value>
  </data>
  <data name="AddProcedureError " xml:space="preserve">
    <value>An error occurred while adding the procedure(s).</value>
  </data>
  <data name="InvalidProcedure " xml:space="preserve">
    <value>Invalid procedure data provided.</value>
  </data>
  <data name="ProcedureUpdated " xml:space="preserve">
    <value>Procedure updated successfully</value>
  </data>
  <data name="UpdateProcedureError " xml:space="preserve">
    <value>An error occurred while updating the procedure</value>
  </data>
  
  <data name="ErrorDeletingGynHistory" xml:space="preserve">
    <value>Error occurred while deleting the OB history.</value>
  </data>
  <data name="ErrorDeletingObHistory" xml:space="preserve">
    <value>ErrorDeletingObHistory</value>
  </data>
  <data name="ErrorFetchingGynHistories" xml:space="preserve">
    <value>ErrorFetchingGynHistories</value>
  </data>
  <data name="ErrorFetchingGynHistoriesByPatient" xml:space="preserve">
    <value>ErrorFetchingGynHistoriesByPatient</value>
  </data>
  <data name="ErrorFetchingGynHistory" xml:space="preserve">
    <value>ErrorFetchingGynHistory</value>
  </data>
  <data name="ErrorFetchingObHistories" xml:space="preserve">
    <value>ErrorFetchingObHistories</value>
  </data>
  <data name="ErrorFetchingObHistoriesByPatient" xml:space="preserve">
    <value>ErrorFetchingObHistoriesByPatient</value>
  </data>
  <data name="ErrorFetchingObHistory" xml:space="preserve">
    <value>ErrorFetchingObHistory</value>
  </data>
  <data name="ErrorUpdatingGynHistory" xml:space="preserve">
    <value>ErrorUpdatingGynHistory</value>
  </data>
  <data name="ErrorUpdatingGynHistoryList" xml:space="preserve">
    <value>ErrorUpdatingGynHistoryList</value>
  </data>
  <data name="ErrorUpdatingObHistory" xml:space="preserve">
    <value>ErrorUpdatingObHistory</value>
  </data>
  <data name="ErrorUpdatingObHistoryList" xml:space="preserve">
    <value>ErrorUpdatingObHistoryList</value>

  </data>
</root>