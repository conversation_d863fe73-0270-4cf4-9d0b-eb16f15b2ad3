﻿using Contracts;
using DataAccessLayer.Implementation;
using MemberServiceDataAccessLayer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class VisitStatusCommandHandler : IVisitStatusCommandHandler<VisitStatus>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<VisitStatusCommandHandler> _logger;
        private readonly IStringLocalizer<VisitStatusCommandHandler> _localizer;

        public VisitStatusCommandHandler(
            IConfiguration configuration,
            IUnitOfWork unitOfWork,
            ILogger<VisitStatusCommandHandler> logger,
            IStringLocalizer<VisitStatusCommandHandler> localizer
        )
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Updates an existing visit status.
        /// </summary>
        /// <param name="visitStatus">The visit status entity to update.</param>
        public async Task UpdateVisitStatusAsync(VisitStatus visitStatus)
        {
            try
            {
                await _unitOfWork.VisitStatusRepository.UpdateAsync(visitStatus);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation(_localizer["VisitStatus updated successfully."]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["An error occurred while updating the VisitStatus."]);
                throw;
            }
        }

        /// <summary>
        /// Deletes a visit status by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the visit status to delete.</param>
        public async Task DeleteVisitStatusByIdAsync(Guid id)
        {
            try
            {
                await _unitOfWork.VisitStatusRepository.DeleteByIdAsync(id);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation(_localizer["VisitStatus deleted successfully."]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["An error occurred while deleting the VisitStatus by ID."]);
                throw;
            }
        }

        /// <summary>
        /// Deletes a visit status by entity reference.
        /// </summary>
        /// <param name="visitStatus">The visit status entity to delete.</param>
        public async Task DeleteVisitStatusByEntityAsync(VisitStatus visitStatus)
        {
            try
            {
                await _unitOfWork.VisitStatusRepository.DeleteByEntityAsync(visitStatus);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation(_localizer["VisitStatus deleted successfully."]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["An error occurred while deleting the VisitStatus by entity."]);
                throw;
            }
        }
    }
}
