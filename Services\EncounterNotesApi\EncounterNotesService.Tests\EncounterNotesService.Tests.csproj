﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="Carbon.Redis" Version="2.8.2" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="FluentAssertions" Version="7.0.0" />
    <PackageReference Include="Markdig" Version="0.40.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="NUnit" Version="4.3.2" />
    <PackageReference Include="NUnit.Analyzers" Version="4.5.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />
	  <PackageReference Include="OpenAI" Version="2.1.0" />
	  <PackageReference Include="Microsoft.SemanticKernel" Version="1.24.1" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BusinessLayer\EncounterNotesBusinessLayer.csproj" />
    <ProjectReference Include="..\Contracts\EncounterNotesContracts.csproj" />
    <ProjectReference Include="..\DataAccessLayer\EncounterNotesDataAccessLayer.csproj" />
    <ProjectReference Include="..\EncounterNotesService.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
  </ItemGroup>

</Project>
