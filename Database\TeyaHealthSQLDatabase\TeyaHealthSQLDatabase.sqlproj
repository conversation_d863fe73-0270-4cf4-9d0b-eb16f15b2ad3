﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build">
  <Sdk Name="Microsoft.Build.Sql" Version="0.2.3-preview" />
  <PropertyGroup>
    <Name>TeyaHealthSQLDatabase</Name>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <ModelCollation>1033, CI</ModelCollation>
    <RootNamespace>TeyaHealthSQLDatabase</RootNamespace>
    <AssemblyName>TeyaHealthSQLDatabase</AssemblyName>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <ProjectGuid>{0d466a75-5329-4633-a5fb-00360a417804}</ProjectGuid>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="Practice\" />
    <Folder Include="Practice\Tables\" />
    <Folder Include="EncounterNotesService\" />
    <Folder Include="EncounterNotesService\Tables\" />
    <Folder Include="Appointments\" />
    <Folder Include="Appointments\Tables\" />
    <Folder Include="AccountService\" />
    <Folder Include="AccountService\Tables\" />
    <Folder Include="Security\" />
    <Folder Include="AccountService\Post_Deployment_Script" />
    <Folder Include="MessageService\" />
    <Folder Include="MessageService\Tables\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="Practice\Tables\Tasks.sql" />
    <Build Include="EncounterNotesService\Tables\WordTimings.sql" />
    <Build Include="EncounterNotesService\Tables\ChiefComplaint.sql" />
    <Build Include="EncounterNotesService\Tables\ProgressNotes.sql" />
    <Build Include="Appointments\Tables\Appointments.sql" />
    <Build Include="AccountService\Tables\UserThemes.sql" />
    <Build Include="AccountService\Tables\UpToDate.sql" />
    <Build Include="AccountService\Tables\Insurance.sql" />
    <Build Include="AccountService\Tables\Addresses.sql" />
    <Build Include="AccountService\Tables\Users.sql" />
    <Build Include="AccountService\Tables\Role.sql" />
    <Build Include="AccountService\Tables\ProductUserAccess.sql" />
    <Build Include="AccountService\Tables\Facility.sql" />
    <Build Include="AccountService\Tables\Licenses.sql" />
    <Build Include="AccountService\Tables\Products.sql" />
    <Build Include="AccountService\Tables\Organization.sql" />
    <Build Include="Security\Practice.sql" />
    <Build Include="Security\EncounterNotesService.sql" />
    <Build Include="Security\Appointments.sql" />
    <Build Include="Security\AccountService.sql" />
    <Build Include="EncounterNotesService\Tables\ProgressNotesTemplates.sql" />
    <Build Include="EncounterNotesService\Tables\ProgressNotesPredefinedTemplates.sql" />
    <Build Include="AccountService\Tables\PageRoleMappings.sql" />
    <Build Include="EncounterNotesService\Tables\HistoryOfPresentIllness.sql" />
    <Build Include="EncounterNotesService\Tables\Symptoms.sql" />
    <Build Include="EncounterNotesService\Tables\ProgressNotesTemplates.sql" />
    <Build Include="EncounterNotesService\Tables\ProgressNotesPredefinedTemplates.sql" />
    <Build Include="AccountService\Tables\VisitType.sql" />
    <Build Include="AccountService\Tables\VisitStatus.sql" />
    <Build Include="AccountService\Tables\PagePath.sql" />
    <Build Include="AccountService\Tables\Country.sql" />
    <Build Include="EncounterNotesService\Tables\PatientCurrentMedication.sql" />
    <Build Include="EncounterNotesService\Tables\PatientSurgicalHistory.sql" />
    <Build Include="EncounterNotesService\Tables\ICD.sql" />
    <Build Include="EncounterNotesService\Tables\Allergy.sql" />
    <Build Include="EncounterNotesService\Tables\PatientMedicalHistory.sql" />
    <Build Include="EncounterNotesService\Tables\Relations.sql" />
    <Build Include="EncounterNotesService\Tables\FamilyMember.sql" />
    <Build Include="EncounterNotesService\Tables\PatientSocialHistory.sql" />
    <Build Include="EncounterNotesService\Tables\ReviewOfSystem.sql" />
    <Build Include="EncounterNotesService\Tables\MedicalHistory.sql" />
    <Build Include="EncounterNotesService\Tables\PatientVitals.sql" />
    <Build Include="EncounterNotesService\Tables\PatientReferralOutgoing.sql" />
    <Build Include="AccountService\Tables\PlanTypes.sql" />
    <Build Include="AccountService\Tables\License.sql" />
    <Build Include="EncounterNotesService\Tables\SoapNotesComponents.sql" />
    <Build Include="EncounterNotesService\Tables\Assessments.sql" />
    <Build Include="EncounterNotesService\Tables\PhysicalTherapy.sql" />
    <Build Include="AccountService\Tables\GuardianDetails.sql" />
    <Build Include="AccountService\Tables\EmployerDetails.sql" />
    <Build Include="EncounterNotesService\Tables\Procedure.sql" />
    <Build Include="EncounterNotesService\Tables\CPT.sql" />
    <Build Include="EncounterNotesService\Tables\PatientPrescriptionMedication.sql" />
    <Build Include="EncounterNotesService\Tables\PatientPhysicalExamination.sql" />
    <Build Include="EncounterNotesService\Tables\PatientPastResults.sql" />
    <Build Include="EncounterNotesService\Tables\TherapeuticInterventionsList.sql" />
    <Build Include="EncounterNotesService\Tables\PatientTherapeuticInterventions.sql" />
    <Build Include="EncounterNotesService\Tables\HospitalizationRecord.sql" />
    <Build Include="EncounterNotesService\Tables\ObHistory.sql" />
    <Build Include="EncounterNotesService\Tables\GynHistory.sql" />
    <Build Include="EncounterNotesService\Tables\DiagnosticImagingDTO.sql" />
    <Build Include="EncounterNotesService\Tables\DiagnosticImagingAssessment.sql" />
    <Build Include="EncounterNotesService\Tables\DiagnosticImaging.sql" />
    <PostDeploy Include="AccountService\Post_Deployment_Script\Post_Deployment_Script.sql" />
    <Build Include="EncounterNotesService\Tables\Vaccines.sql" />
    <Build Include="EncounterNotesService\Tables\PatientImmunization.sql" />
    <Build Include="EncounterNotesService\Tables\Examination.sql" />
    <Build Include="AccountService\Tables\PreDefinedPageRoleMappings.sql" />
    <Build Include="AccountService\Tables\PredefinedVisitType.sql" />
    <Build Include="AccountService\Tables\Roleslist.sql" />
    <Build Include="EncounterNotesService\Tables\LabTests.sql" />
    <Build Include="MessageService\Tables\MessageAttachments.sql" />
    <Build Include="MessageService\Tables\User.sql" />
    <Build Include="MessageService\Tables\Messages.sql" />
    <Build Include="Security\MessageService.sql" />
  </ItemGroup>
</Project>