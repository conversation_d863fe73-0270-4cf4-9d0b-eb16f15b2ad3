{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=MessageServiceDb;Trusted_Connection=true;MultipleActiveResultSets=true", "AzureBlobStorage": "DefaultEndpointsProtocol=https;AccountName=your_storage_account;AccountKey=your_account_key;EndpointSuffix=core.windows.net"}, "AzureBlobStorage": {"ContainerName": "message-attachments"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "your-domain.onmicrosoft.com", "TenantId": "your-tenant-id", "ClientId": "your-client-id"}}