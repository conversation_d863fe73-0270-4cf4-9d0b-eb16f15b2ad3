﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;


namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class ExaminationCommandHandler : IExaminationCommandHandler
    {
        private readonly IUnitOfWork _unitOfWork;
        public ExaminationCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }



        public async Task AddExaminationList(List<Examination> examinations)
        {
            await _unitOfWork.ExaminationRepository.AddAsync(examinations);
            await _unitOfWork.SaveAsync();
        }

      

        public async Task UpdateExaminationList(List<Examination> examinations)
        {
            await _unitOfWork.ExaminationRepository.UpdateRangeAsync(examinations);
            await _unitOfWork.SaveAsync();
        }

       

       
    }
}
