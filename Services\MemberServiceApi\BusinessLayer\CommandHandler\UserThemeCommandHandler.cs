﻿using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class UserThemeCommandHandler : IUserThemeCommandHandler<UserTheme>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserThemeCommandHandler> _logger;

        public UserThemeCommandHandler(IUnitOfWork unitOfWork, ILogger<UserThemeCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddUserTheme(UserTheme userTheme)
        {
            await _unitOfWork.UserThemeRepository.AddAsync(userTheme);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateUserTheme(UserTheme userTheme)
        {
            await _unitOfWork.UserThemeRepository.UpdateAsync(userTheme);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteUserThemeById(Guid id)
        {
            await _unitOfWork.UserThemeRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
