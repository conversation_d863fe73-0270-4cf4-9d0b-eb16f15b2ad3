﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Employer : IModel
    {
        public Guid? EmployerId { get; set; }
        public string? Occupation { get; set; }
        public string? EmployerName { get; set; }
        public string? EmployerAddress { get; set; }
        public string? EmployerAddressLine2 { get; set; }
        public string? EmployerCity { get; set; }
        public string? EmployerState { get; set; }
        public string? EmployerPostalCode { get; set; }
        public string? EmployerCountry { get; set; }
        public string? EmployerIndustry { get; set; }
    }
}
