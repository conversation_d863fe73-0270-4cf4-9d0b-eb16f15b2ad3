﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPracticeService
    {
        Task CreateTasksAsync(List<Tasks> Tasks);
        Task<List<Tasks>> GetTasksAsync();
        Task<List<ProviderPatient>> GetMemberAsync();
        Task DeleteTasksAsync(Guid taskId);
        Task UpdateTasksByPatientIdAsync(Tasks task);
        Task UpdateTasksAsync(Tasks task);
    }
}
