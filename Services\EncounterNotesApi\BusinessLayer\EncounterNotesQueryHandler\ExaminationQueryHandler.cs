﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ExaminationQueryHandler : IExaminationQueryHandler
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public ExaminationQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;

        }

        public async Task<IEnumerable<Examination>> GetExaminationByPatientIdAndIsActive(Guid PatientId)
        {
            var examinations = await _unitOfWork.ExaminationRepository.GetAsync();
            return examinations.Where(examination => examination.PatientId == PatientId && examination.IsActive == true);
        }

        public async Task<IEnumerable<Examination>> GetExaminationByPatientId(Guid PatientId)
        {
            var examinations = await _unitOfWork.ExaminationRepository.GetAsync();
            return examinations.Where(examination => examination.PatientId == PatientId);
        }
    }
}
