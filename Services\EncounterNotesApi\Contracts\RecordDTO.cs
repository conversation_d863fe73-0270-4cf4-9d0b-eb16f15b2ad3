﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class RecordDTO : IContract
    {
        public Guid Id { get; set; }
        public string PatientName { get; set; }
        public DateTime DateTime { get; set; }

        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }

        public Guid PatientId { get; set; }

        public string Notes { get; set; }

        public string Transcription { get; set; }
        public bool isEditable { get; set; }
        public List<WordTimingDTO> WordTimings { get; set; } = new List<WordTimingDTO>();
    }
}

