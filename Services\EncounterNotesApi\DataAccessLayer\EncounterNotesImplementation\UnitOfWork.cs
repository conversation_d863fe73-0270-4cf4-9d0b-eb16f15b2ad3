﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly RecordDatabaseContext _context;
        public IRecordRepository RecordRepository { get; }
        public IFamilyMemberRepository FamilyMemberRepository { get; }
        public IRelationRepository RelationRepository { get; }
        public IDiagnosticImagingAssessmentRepository DiagnosticImagingAssessmentRepository { get; }
        public IAllergyRepository AllergyRepository { get; }
        public IHospitalizationRecordRepository HospitalizationRecordRepository { get; }
        public ITemplatesRepository TemplatesRepository { get; }
        public IPredefinedTemplatesRepository PredefinedTemplatesRepository { get; }
        public ISurgicalRepository SurgicalRepository { get; }
        public IImmunizationRepository ImmunizationRepository { get; }
        public IPastResultsRepository PastResultsRepository { get; }
        public ISoapNotesComponentsRepository SoapNotesComponentsRepository { get; }
        public IPhysicalExaminationRepository PhysicalExaminationRepository { get; }
        public IICDRepository ICDRepository { get; }
        public IVaccinesRepository VaccinesRepository { get; }
        public ITherapeuticInterventionsListRepository TherapeuticInterventionsListRepository { get; }
        public IHistoryOfPresentIllnessRepository HistoryOfPresentIllnessRepository {  get; }
        public ISymptomsRepository SymptomsRepository { get; }
        public IChiefComplaintRepository<ChiefComplaint> ChiefComplaintRepository { get; }   
        public IMedicalHistoryRepository MedicalHistoryRepository { get; }
        public ICurrentMedicationRepository CurrentMedicationRepository { get; }
        public ISocialHistoryRepository SocialHistoryRepository { get; }
        public IVitalRepository VitalRepository { get; }
        public IReferralOutgoingRepository ReferralOutgoingRepository { get; }
        public IReviewOfSystemRepository ReviewOfSystemRepository { get; }
        public IAssessmentsRepository AssessmentsRepository { get; }
        public ITherapeuticInterventionsRepository TherapeuticInterventionsRepository { get; }
        public IPhysicalTherapyRepository PhysicalTherapyRepository { get; }

        public IProcedureRepository ProcedureRepository { get; }
        public ICPTRepository CPTRepository { get; }

        public IPrescriptionMedicationRepository PrescriptionMedicationRepository { get; }
        public IDiagnosticImagingRepository DiagnosticImagingRepository { get; }
        public IDiagnosticImagingPageRepository DiagnosticImagingPageRepository { get; }
        public IObHistoryRepository<ObHistory> ObHistoryRepository { get; }
        public IGynHistoryRepository<GynHistory> GynHistoryRepository { get; }
        public IExaminationRepository ExaminationRepository { get; }
        public ILabTestsRepository LabTestsRepository { get; }

        public UnitOfWork(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            RecordRepository = new RecordRepository(_context, localizer);
            FamilyMemberRepository = new FamilyMemberRepository(_context, localizer);
            DiagnosticImagingAssessmentRepository = new DiagnosticImagingAssessmentRepository(_context, localizer);
            DiagnosticImagingRepository = new DiagnosticImagingRepository(_context, localizer);
            DiagnosticImagingPageRepository = new DiagnosticImagingPageRepository(_context, localizer);
            TemplatesRepository = new TemplatesRepository(_context, localizer);
            PredefinedTemplatesRepository = new PredefinedTemplatesRepository(_context, localizer);
            CurrentMedicationRepository = new CurrentMedicationRepository(_context, localizer);
            SocialHistoryRepository = new SocialHistoryRepository(_context, localizer);
            ICDRepository =new ICDRepository(_context, localizer);
            VaccinesRepository = new VaccinesRepository(_context, localizer);
            TherapeuticInterventionsListRepository = new TherapeuticInterventionsListRepository(_context, localizer);
            SoapNotesComponentsRepository = new SoapNotesComponentsRepository(_context, localizer);
            SurgicalRepository = new SurgicalRepository(_context, localizer);
            ImmunizationRepository = new ImmunizationRepository(_context, localizer);
            PhysicalExaminationRepository = new PhysicalExaminationRepository(_context, localizer);
            PastResultsRepository = new PastResultsRepository(_context, localizer);
            RelationRepository = new RelationRepository(_context, localizer);
            HospitalizationRecordRepository = new HospitalizationRecordRepository(_context, localizer);
            HistoryOfPresentIllnessRepository =new HistoryOfPresentIllnessRepository(_context, localizer);
            SymptomsRepository = new SymptomsRepository(_context, localizer);
            AllergyRepository = new AllergyRepository(_context, localizer);
            ChiefComplaintRepository = new ChiefComplaintRepository(_context, localizer);
            MedicalHistoryRepository = new MedicalHistoryRepository(_context, localizer);
            PhysicalTherapyRepository = new PhysicalTherapyRepository(_context, localizer);
            AssessmentsRepository = new AssessmentsRepository(_context, localizer);
            TherapeuticInterventionsRepository = new TherapeuticInterventionsRepository(_context, localizer);
            ReviewOfSystemRepository = new ReviewOfSystemRepository(_context, localizer);
            VitalRepository = new VitalRepository(_context, localizer);
            ReferralOutgoingRepository = new ReferralOutgoingRepository(_context, localizer);

            ProcedureRepository = new ProcedureRepository(_context, localizer);
            CPTRepository = new CPTRepository(_context, localizer);

            PrescriptionMedicationRepository = new PrescriptionMedicationRepository(_context,localizer);
            ObHistoryRepository = new ObHistoryRepository(_context, localizer);
            GynHistoryRepository = new GynHistoryRepository(_context, localizer);
            ExaminationRepository = new ExaminationRepository(_context, localizer);
            LabTestsRepository =  new LabTestsRepository(_context, localizer);

        }
        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }
        public void Dispose()
        {
            _context.Dispose();
        }
    }
}