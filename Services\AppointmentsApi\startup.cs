﻿using BusinessLayer.CommandHandler;
using BusinessLayer;
using AppointmentContracts;
using DotNetEnv;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using BusinessLayer.QueryHandler;
using AppointmentDataAccessLayer.AppointmentImplementation;
using Microsoft.EntityFrameworkCore;
using AppointmentDataAccessLayer.AppointmentContext;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Identity.Web;


namespace Appointments
{
    public class Startup
    {

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            var builder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables();
            Configuration = builder.Build();
            Env.Load();
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddScoped<IAppointmentCommandHandler<Appointment>, AppointmentCommandHandler>();
            services.AddScoped<IAppointmentQueryHandler<Appointment>, AppointmentQueryHandler>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IMigration, Migration>();



            services.AddScoped<ShardMapManagerService>(provider =>
       new ShardMapManagerService(Environment.GetEnvironmentVariable("ShardMapManagerConnectionString")));

            services.AddControllers();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Swagger Azure AD for Appointments api", Version = "v1" });
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Description = "Oauth2.0 which uses AuthorizationCode flow",
                    Name = "oauth2.0",
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__AuthorizationUrl")),
                            TokenUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__TokenUrl")),
                            Scopes = new Dictionary<string, string>
                            {
                                { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope"), "Access API as User" }
                            }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                         new[] { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope") }
                    }
                });
            });

            services.AddLocalization();


            services.AddHttpClient();

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = Environment.GetEnvironmentVariable("AzureAd__Authority");
                    options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidIssuer = Environment.GetEnvironmentVariable("AzureAd__Authority"),
                        ValidAudience = Environment.GetEnvironmentVariable("AzureAd__ClientId")
                    };
                });


        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment() || env.IsProduction())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.OAuthClientId(Environment.GetEnvironmentVariable("SwaggerAzureAd__ClientId"));
                    c.OAuthUsePkce();
                    c.OAuthScopeSeparator(" ");
                    string swaggerJsonBasePath = string.IsNullOrWhiteSpace(c.RoutePrefix) ? "." : "..";
                    c.SwaggerEndpoint($"{swaggerJsonBasePath}/swagger/v1/swagger.json", "MemberServiceApi v1");
                });
            }

            app.UseHttpsRedirection();

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}