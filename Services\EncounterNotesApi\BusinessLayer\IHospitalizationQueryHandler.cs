﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IHospitalizationRecordQueryHandler<T>
    {
        Task<IEnumerable<HospitalizationRecord>> GetHospitalizationRecordByIdAndIsActive(Guid id);
        Task<List<T>> GetHospitalizationRecordById(Guid id);

    }
}