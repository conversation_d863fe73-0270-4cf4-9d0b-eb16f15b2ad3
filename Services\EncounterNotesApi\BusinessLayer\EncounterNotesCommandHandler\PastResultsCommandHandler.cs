﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public class PastResultsCommandHandler : IPastResultsCommandHandler<PastResults>
    {
        private readonly IUnitOfWork _unitOfWork;
        public PastResultsCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddResult(List<PastResults> Results)
        {
            await _unitOfWork.PastResultsRepository.AddAsync(Results);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateResult(PastResults results)
        {
            await _unitOfWork.PastResultsRepository.UpdateAsync(results);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteResultById(Guid id)
        {
            await _unitOfWork.PastResultsRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteResultsByEntity(PastResults past)
        {
            await _unitOfWork.PastResultsRepository.DeleteByEntityAsync(past);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdatePastList(List<PastResults> Past)
        {
            await _unitOfWork.PastResultsRepository.UpdateRangeAsync(Past);
            await _unitOfWork.SaveAsync();
        }
    }
}