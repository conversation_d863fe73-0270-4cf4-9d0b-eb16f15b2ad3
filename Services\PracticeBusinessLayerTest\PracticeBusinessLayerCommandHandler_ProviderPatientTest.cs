﻿
using Microsoft.Extensions.Logging;
using Moq;
using PracticeBusinessLayer.CommandHandler;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;

namespace PracticeBusinessLayerTest
{
    public class PracticeBusinessLayerCommandHandlerPeopleTest
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<ILogger<ProviderPatientCommandHandler>> _mockLogger;
        private readonly ProviderPatientCommandHandler _peopleCommandHandler;

        public PracticeBusinessLayerCommandHandlerPeopleTest()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<ProviderPatientCommandHandler>>();
            _peopleCommandHandler = new ProviderPatientCommandHandler(null, _mockUnitOfWork.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task AddPeople_Should_AddPeople_ToRepository()
        {
            // Arrange
            var peopleList = new List<ProviderPatient>
            {
                new ProviderPatient { Id = Guid.NewGuid(), PatientName = "P001", PCPName = "D01",PCPId=Guid.NewGuid()},
                new ProviderPatient { Id = Guid.NewGuid(), PatientName = "P002", PCPName = "D02",PCPId=Guid.NewGuid() }
            };

            _mockUnitOfWork.Setup(u => u.ProviderPatientRepository.AddAsync(It.IsAny<List<ProviderPatient>>()))
                           .Returns(Task.CompletedTask);


            _mockUnitOfWork.Setup(u => u.SaveAsync())
                           .Returns(Task.FromResult(0));

            // Act
            await _peopleCommandHandler.AddProviderPatient(peopleList);

            // Assert
            _mockUnitOfWork.Verify(u => u.ProviderPatientRepository.AddAsync(It.IsAny<List<ProviderPatient>>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdatePeople_Should_UpdatePerson_InRepository()
        {
            // Arrange
            var person = new ProviderPatient { Id = Guid.NewGuid(), PatientName = "P003", PCPName = "D03", PCPId = Guid.NewGuid() };

            _mockUnitOfWork.Setup(u => u.ProviderPatientRepository.UpdateAsync(It.IsAny<ProviderPatient>()))
                           .Returns(Task.CompletedTask);

            _mockUnitOfWork.Setup(u => u.SaveAsync())
                           .Returns(Task.FromResult(0));

            // Act
            await _peopleCommandHandler.UpdateProviderPatient(person);

            // Assert
            _mockUnitOfWork.Verify(u => u.ProviderPatientRepository.UpdateAsync(It.IsAny<ProviderPatient>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeletePeopleById_Should_DeletePerson_ById()
        {
            // Arrange
            var id = Guid.NewGuid();

            _mockUnitOfWork.Setup(u => u.ProviderPatientRepository.DeleteByIdAsync(It.IsAny<Guid>()))
                           .Returns(Task.CompletedTask);


            _mockUnitOfWork.Setup(u => u.SaveAsync())
                           .Returns(Task.FromResult(0));

            // Act
            await _peopleCommandHandler.DeleteProviderPatientById(id);

            // Assert
            _mockUnitOfWork.Verify(u => u.ProviderPatientRepository.DeleteByIdAsync(It.IsAny<Guid>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeletePeopleByEntity_Should_DeletePerson_ByEntity()
        {
            // Arrange
            var person = new ProviderPatient { Id = Guid.NewGuid(), PatientName = "P001", PCPName = "D01" };

            _mockUnitOfWork.Setup(u => u.ProviderPatientRepository.DeleteByEntityAsync(It.IsAny<ProviderPatient>()))
                           .Returns(Task.CompletedTask);


            _mockUnitOfWork.Setup(u => u.SaveAsync())
                           .Returns(Task.FromResult(0));

            // Act
            await _peopleCommandHandler.DeleteProviderPatientByEntity(person);

            // Assert
            _mockUnitOfWork.Verify(u => u.ProviderPatientRepository.DeleteByEntityAsync(It.IsAny<ProviderPatient>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}

