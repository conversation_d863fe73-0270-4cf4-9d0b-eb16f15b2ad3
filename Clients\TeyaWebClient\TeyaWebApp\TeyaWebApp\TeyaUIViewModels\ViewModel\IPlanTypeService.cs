﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPlanTypeService
    {
        Task<IEnumerable<PlanType>> GetAllPlanTypesAsync();
        Task<PlanType> GetPlanTypeByIdAsync(Guid id);
        Task AddPlanTypeAsync(PlanType plan);
        Task UpdatePlanTypeAsync(PlanType plan);
        Task DeletePlanTypeByIdAsync(Guid id);
    }
}
