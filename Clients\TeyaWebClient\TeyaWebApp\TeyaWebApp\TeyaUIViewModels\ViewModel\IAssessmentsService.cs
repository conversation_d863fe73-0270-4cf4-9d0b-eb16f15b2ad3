﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IAssessmentsService
    {
        Task<List<AssessmentsData>> GetAllByIdAsync(Guid id);
        Task<List<AssessmentsData>> GetAllByIdAndIsActiveAsync(Guid id);
        Task<List<string>> GetDiagnosisListByIdAsync(Guid id);
        Task AddAssessmentsAsync(List<AssessmentsData> medicalHistories);
        Task UpdateAssessmentsAsync(AssessmentsData _Assessments);
        Task UpdateAssessmentsListAsync(List<AssessmentsData> medicalHistories);
        Task DeleteAssessmentsByEntityAsync(AssessmentsData _Assessments);
        Task<Dictionary<string, List<string>>> GetAllMedicationsRelatedToAssessments(Guid PatientId);
    }
}