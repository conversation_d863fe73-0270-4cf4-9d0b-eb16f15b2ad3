﻿using EncounterNotesBusinessLayer;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesContracts;
using EncounterNotesService.Controllers;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesService.Tests.EncounterNotesService_Controllers.Tests
{
    [TestFixture]
    public class SpeechControllerTests
    {
        private Mock<ISpeechDataHandler<Speech>> _speechHandlerMock;
        private Mock<ILogger<SpeechController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private SpeechController _speechController;

        [SetUp]
        public void SetUp()
        {
            _speechHandlerMock = new Mock<ISpeechDataHandler<Speech>>();
            _loggerMock = new Mock<ILogger<SpeechController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Mocking localizer strings
            _localizerMock.Setup(x => x["SuccessfulEncounterNote"])
                .Returns(new LocalizedString("SuccessfulEncounterNote", "Encounter note processed successfully."));
            _localizerMock.Setup(x => x["EncounterNoteFailed"])
                .Returns(new LocalizedString("EncounterNoteFailed", "Failed to process encounter note."));
            _localizerMock.Setup(x => x["SpeechProcessingError"])
                .Returns(new LocalizedString("SpeechProcessingError", "Error processing speech."));
            _localizerMock.Setup(x => x["NoFilesFound"])
                .Returns(new LocalizedString("NoFilesFound", "No files found."));
            _localizerMock.Setup(x => x["UploadSuccessful"])
                .Returns(new LocalizedString("UploadSuccessful", "Upload successful."));
            _localizerMock.Setup(x => x["ErrorProcessingAudioFile"])
                .Returns(new LocalizedString("ErrorProcessingAudioFile", "Error processing audio file."));
            _localizerMock.Setup(x => x["500"])
                .Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["/app/ffmpeg"])
                .Returns(new LocalizedString("/app/ffmpeg", "/app/ffmpeg"));
            _localizerMock.Setup(x => x["wav"])
                .Returns(new LocalizedString("wav", "wav"));
            _localizerMock.Setup(x => x["audio/wav"])
                .Returns(new LocalizedString("audio/wav", "audio/wav"));

            _speechController = new SpeechController(_speechHandlerMock.Object, _loggerMock.Object, _localizerMock.Object);
        }

        [Test]
        public async Task TranscribeAsync_ShouldReturnOk_WhenHandlerReturnsValidGuid()
        {
            // Arrange
            var speechRequest = new SpeechRequest();
            var expectedGuid = Guid.NewGuid();

            _speechHandlerMock.Setup(x => x.Handle(It.IsAny<SpeechRequest>()))
                .ReturnsAsync(expectedGuid);

            // Act
            var result = await _speechController.TranscribeAsync(speechRequest);

            // Assert
            var okResult = result.Result as OkObjectResult;

            Assert.That(okResult, Is.Not.Null, "Expected result to be of type OkObjectResult");
            Assert.That(okResult.StatusCode, Is.EqualTo(200), "Expected HTTP status code 200");
            Assert.That(okResult.Value, Is.Not.Null, "Expected value in response");
            Assert.That(okResult.Value, Is.InstanceOf<object>(), "Expected response value to be an object");
        }

        [Test]
        public async Task TranscribeAsync_ShouldReturnBadRequest_WhenHandlerReturnsNull()
        {
            // Arrange
            var speechRequest = new SpeechRequest();

            _speechHandlerMock.Setup(x => x.Handle(It.IsAny<SpeechRequest>()))
                .ReturnsAsync((Guid?)null);

            // Act
            var result = await _speechController.TranscribeAsync(speechRequest);

            // Assert
            var badRequestResult = result.Result as BadRequestObjectResult;

            Assert.That(badRequestResult, Is.Not.Null, "Expected result to be of type BadRequestObjectResult");
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400), "Expected HTTP status code 400");
        }
    }
}