﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class PastResultsQueryHandler : IPastResultsQueryHandler<PastResults>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PastResultsQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PastResults>> GetAllResultsById(Guid id)
        {
            var past = await _unitOfWork.PastResultsRepository.GetAllResultsAsync();

            return past.Where(PastRes => PastRes.PatientId == id);
        }
        public async Task<IEnumerable<PastResults>> GetResultByIdAndIsActive(Guid id)
        {
            var prevresult = await _unitOfWork.PastResultsRepository.GetAsync();

            return prevresult.Where(PastRes => PastRes.PatientId == id && PastRes.IsActive == true);
        }
    }
}