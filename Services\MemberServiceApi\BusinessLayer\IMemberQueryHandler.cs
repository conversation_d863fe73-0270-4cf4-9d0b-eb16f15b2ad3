﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IMemberQueryHandler<TText>
    {
        Task<Patient> GetPatientdatabyid(Guid id);
        Task<IEnumerable<TText>> GetMember();
        Task<TText> GetMemberById(Guid id);
        Task<IEnumerable<TText>> SearchMembersAsync(string searchTerm);
        Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds);
        Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid id);
        Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid id);
    }
}
